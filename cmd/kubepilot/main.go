package main

import (
	"flag"
	"time"

	"github.com/containers/buildah"
	"github.com/spf13/pflag"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd"
	"k8s.io/apimachinery/pkg/util/wait"
	cliflag "k8s.io/component-base/cli/flag"
	"k8s.io/klog/v2"
)

// The default klog flush interval is 30 seconds, it seems to long.
var logFlushFreq = pflag.Duration("log-flush-frequency", 5*time.Second, "Maximum number of seconds between log flushes")

func main() {
	// 这段代码是在检查当前的进程是否是一个由 InitReexec 函数创建的重新执行（reexec）的进程。
	// 如果是，InitReexec() 函数会返回 true，并执行容器的运行时程序，然后结束当前进程。
	// 如果不是，函数会返回 false，主程序会正常执行.
	if buildah.InitReexec() {
		klog.Info("The process is a reexec process created by the InitReexec function")
		return
	}

	klog.InitFlags(nil)
	pflag.CommandLine.SetNormalizeFunc(cliflag.WordSepNormalizeFunc)
	pflag.CommandLine.AddGoFlagSet(flag.CommandLine)
	pflag.Set("logtostderr", "true")

	go wait.Until(klog.Flush, *logFlushFreq, wait.NeverStop)
	defer klog.Flush()

	cmd.Execute()
}
