package remote

import (
	"context"
	"net"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	pkgcf "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"golang.org/x/sync/errgroup"
)

// exampleScp 提供scp命令的使用示例
const exampleScp = `
# 通过角色标签复制文件:
  kubepilot scp -r master,node "/root/source.txt" "/root/target.txt"

# 通过IP地址复制文件:
  kubepilot scp --ips *********** "/root/source.txt" "/root/target.txt"

# 不指定目标节点时，将文件复制到所有节点:
  kubepilot scp "/root/source.txt" "/root/target.txt"
`

// newScpCmd 创建并返回scp子命令
func newScpCmd() *cobra.Command {
	var (
		roles   []string
		ips     []string
		cluster v1beta1.Cluster
	)
	var scpCmd = &cobra.Command{
		Use:     "scp",
		Short:   "在指定节点间复制文件",
		Long:    "该命令用于在Kubernetes集群的指定节点之间复制文件，可以通过节点角色或IP地址来指定目标节点",
		Example: exampleScp,
		Args:    cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			targets := cluster.GetTargetsIPList(ips, roles)
			return runCopy(cluster, targets, args)
		},
		PreRunE: func(cmd *cobra.Command, args []string) (err error) {
			var manager pkgcf.Manager
			manager, _, err = pkgcf.GetActualClusterFile()
			if err != nil {
				return err
			}
			cluster = manager.GetCluster()
			return
		},
	}
	scpCmd.Flags().StringSliceVarP(&roles, "roles", "r", []string{}, "指定目标节点的角色标签(如: master,node)")
	scpCmd.Flags().StringSliceVar(&ips, "ips", []string{}, "指定目标节点的IP地址")
	return scpCmd
}

// runCopy 执行文件复制操作
func runCopy(cluster v1beta1.Cluster, targets []net.IP, args []string) error {
	infraDriver, err := infradriver.NewInfraDriver(&cluster)
	if err != nil {
		return err
	}
	eg, _ := errgroup.WithContext(context.Background())
	for _, ipAddr := range targets {
		ip := ipAddr
		eg.Go(func() error {
			// 递归复制文件
			return infraDriver.Copy(ip, args[0], args[1])
		})
	}
	if err = eg.Wait(); err != nil {
		return err
	}
	return nil
}
