package expansion

import (
	"os"

	"github.com/spf13/cobra"
	"k8s.io/klog/v2"
)

func NewCompletionCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:                   "completion",
		Short:                 "为bash命令生成自动补全脚本",
		ValidArgs:             []string{"bash"},
		Args:                  cobra.MatchAll(cobra.ExactArgs(1), cobra.OnlyValidArgs),
		DisableFlagsInUseLine: true,
		Run: func(cmd *cobra.Command, args []string) {
			switch args[0] {
			case "bash":
				if err := cmd.Root().GenBashCompletion(os.Stdout); err != nil {
					klog.Errorf("failed to use bash completion, err: %v", err)
					os.Exit(1)
				}
			}
		},
	}

	return cmd
}
