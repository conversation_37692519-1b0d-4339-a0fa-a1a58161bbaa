package expansion

import (
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/version"
)

func NewVersionCmd() *cobra.Command {
	var versionCmd = &cobra.Command{
		Use:     "version",
		Short:   "查看版本信息",
		Args:    cobra.NoArgs,
		Example: `kubepilot version`,
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println(version.Get().String())
			return nil
		},
	}

	return versionCmd
}

// Compeleted return compeleted info
func Compeleted() string {
	return fmt.Sprintf(constants.Logo, version.Get().String())
}
