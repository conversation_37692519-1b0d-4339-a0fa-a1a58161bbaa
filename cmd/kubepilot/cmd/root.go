package cmd

import (
	"os"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/alpha"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/cluster"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/expansion"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/image"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/registry"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/remote"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/system"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/version"
	"k8s.io/klog/v2"
)

// rootCmd Parent command to which all subcommands are added
var rootCmd = &cobra.Command{
	Use:   "kubepilot",
	Short: "A client to init/reset k8s cluster like kubeadm and run any application",
	Run: func(cmd *cobra.Command, args []string) {
		runHelp(cmd, args)
	},
	SilenceUsage:  true,
	SilenceErrors: true,
}

// Execute adds all sub commands into the root command and sets flags appropriately.
// This is called by main func. It only needs to happen once to the rootCmd
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		klog.Errorf("[kubepilot:%s]: %v", version.GetSingleVersion(), err)
		os.Exit(1)
	}
}

func init() {
	cobra.OnInitialize(runOrDie)
	// 拓展相关命令
	rootCmd.AddCommand(alpha.NewAlphaCmd(), expansion.NewVersionCmd(), expansion.NewCompletionCmd())
	// 操作集群相关命令
	rootCmd.AddCommand(cluster.NewClusterCmds()...)
	// 集群镜像相关命令
	rootCmd.AddCommand(image.NewImageCmds()...)
	// registry相关命令
	rootCmd.AddCommand(registry.NewRegistryCmds())
	// 运程执行命令、拷贝文件相关命令
	rootCmd.AddCommand(remote.NewRemoteCmds()...)

	rootCmd.DisableAutoGenTag = true

}

func runHelp(cmd *cobra.Command, args []string) {
	_ = cmd.Help()
}

func runOrDie() {
	value, err := system.GetConfigValue(system.DataRootConfigKey)
	if err != nil {
		klog.Exit(err)
	}
	constants.DefaultRuntimeRootDir = value

	value, err = system.GetConfigValue(system.RuntimeRootConfigKey)
	if err != nil {
		klog.Exit(err)
	}
	constants.DefaultClusterRootFsDir = value

	dirs := []string{
		constants.WorkDir(),
	}
	if err = file.MkDirs(dirs...); err != nil {
		klog.Exit(err)
	}
}
