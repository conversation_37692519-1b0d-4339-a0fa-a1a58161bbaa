package alpha

import "github.com/spf13/cobra"

var longAlphaCmdDescription = `
Inside the alpha are some experimental commands, may not be mature for the time being, later will be slowly iterated to beta or rc versions.
`

func NewAlphaCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "alpha",
		Short: "Kubepilot实验性功能",
		Long:  longAlphaCmdDescription,
	}

	cmd.AddCommand(newAliasCmd())
	cmd.AddCommand(newSearchCmd())
	// TODO: add other commands here

	return cmd
}
