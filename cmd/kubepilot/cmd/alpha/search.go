package alpha

import (
	"github.com/spf13/cobra"
)

var example = `kubepilot alpha search <imageDomain>/<imageRepo>/<imageName>
e.g.
	kubepilot alpha search kubernetes
	kubepilot alpha search bingokube/kubernetes
	kubepilot alpha search registry.bingosoft.net/bingokube/kubernetes 
`

func newSearchCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "search",
		Short:   "在默认的regisry中搜索集群镜像",
		Example: example,
		Args:    cobra.MinimumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			// TODO: 编写search逻辑
			return nil
		},
	}

	return cmd
}
