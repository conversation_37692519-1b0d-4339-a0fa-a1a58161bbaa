package cluster

import (
	"errors"
	"fmt"
	"net"
	"strings"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/expansion"
	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	pkgcf "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/interaction"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	"k8s.io/klog/v2"
)

var (
	exampleReset = `
# 重置集群中的指定节点:
  kubepilot reset -n *********** [--force]
  kubepilot reset -m *********** -n *********** [--force]
  kubepilot reset -m ***********,*********** -n ***********,*********** [--force]

# 重置整个集群:
  kubepilot reset --all [--force]
  kubepilot reset -a -f Clusterfile [--force]

# 通过Clusterfile重置集群或者节点:
  kubepilot reset -f Clusterfile

# 跳过特定阶段的重置:
  kubepilot reset --skip-phases=container-runtime [--force]
  kubepilot reset --skip-phases=container-runtime,other-phase [--force]
`
	longResetCmdDescription = `
  该命令用于重置集群或集群中的某些节点，但当管理registry没有使用HA模式的时候，不能重置第一个master节点!
`
)

// newResetCmd 重置集群或者集群节点
func newResetCmd() *cobra.Command {
	f := &commands.ResetFlags{}
	cmd := &cobra.Command{
		Use:     "reset",
		Short:   "重置整个集群或者集群中的某些节点",
		Long:    longResetCmdDescription,
		Args:    cobra.NoArgs,
		Example: exampleReset,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(f.Masters) == 0 && len(f.Nodes) == 0 && !f.ResetAll {
				return fmt.Errorf("the node or cluster what will be reset is empty, use example: %s", exampleReset)
			}
			if f.ResetAll {
				return deleteCluster(f)
			}
			return deleteNodes(f)
		},
		// reset命令完成后打印 kubepilot Logo info
		PostRun: func(cmd *cobra.Command, args []string) {
			klog.Info(expansion.Compeleted())
		},
	}
	fs := cmd.Flags()
	f.InitResetCmdFlags(fs)

	return cmd
}

var (
	ErrCancelled = errors.New("cancel reset")
)

// kebabCaseToPascalCase 将"短横线命名"格式转换为"大写驼峰命名"格式
// 示例: "container-runtime" -> "ContainerRuntime"
func kebabCaseToPascalCase(kebab string) string {
	caser := cases.Title(language.English)
	parts := strings.Split(kebab, "-")
	for i := 0; i < len(parts); i++ {
		parts[i] = caser.String(parts[i])
	}
	return strings.Join(parts, "")
}

// deleteCluster 删除整个集群
func deleteCluster(args *commands.ResetFlags) error {
	var (
		cf  pkgcf.Manager
		err error
	)

	if err = confirmAction(args.ForceDelete, "确认要删除整个集群吗?!!😱", "你已取消删除整个集群🎉"); err != nil {
		return err
	}

	if len(args.ClusterFile) == 0 {
		if cf, _, err = pkgcf.GetActualClusterFile(); err != nil {
			return err
		}
	} else {
		cf = pkgcf.NewClusterfile(pkgcf.WithPath(args.ClusterFile))
		if err = cf.Process(); err != nil {
			return err
		}
	}
	cluster := cf.GetCluster()

	cluster.Spec.Env = append(cluster.Spec.Env, args.CustomEnv...)

	// 添加跳过阶段的环境变量
	addSkipPhasesToEnv(&cluster, args.SkipPhases)

	cf.SetCluster(cluster)

	e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
	if err != nil {
		return err
	}
	// 开始获取相关的镜像文件
	imgId, err := e.Pull(&image.PullOpts{
		SkipTLSVerify: true,
		Quiet:         false,
		PullPolicy:    "missing",
		Image:         cluster.Spec.Image,
		Platform:      "local",
	})
	if err != nil {
		return fmt.Errorf("unable to pull image: %v", err)
	}
	imageSpec, err := e.Inspect(&image.InspectOpts{
		ImageNameOrID: imgId,
	})
	if err != nil {
		return err
	}
	clusterInstaller, err := NewClusterInstaller(cf, e, imageSpec)
	if err != nil {
		return err
	}
	// 开始删除集群
	return clusterInstaller.Delete(clusterDeleteOpts{
		Prune: args.Prune,
	})
}

// deleteNodes 删除指定的集群节点
func deleteNodes(args *commands.ResetFlags) error {
	var (
		cf  pkgcf.Manager
		err error
	)

	if err = confirmAction(args.ForceDelete, "确认要删除该节点吗?😓", "你已取消删除该节点😊"); err != nil {
		return err
	}

	// 校验节点IP列表
	if err = unet.ValidateScaleIPStr(args.Masters, args.Nodes); err != nil {
		return fmt.Errorf("unable to valid input ip: %v", err)
	}
	// 转换成ip数组
	ms, ws, err := unet.ParseToNetIPList(args.Masters, args.Nodes)
	if err != nil {
		return fmt.Errorf("unable to parse ip string to ip list: %v", err)
	}
	if len(args.ClusterFile) == 0 {
		if cf, _, err = pkgcf.GetActualClusterFile(); err != nil {
			return err
		}
	} else {
		cf = pkgcf.NewClusterfile(pkgcf.WithPath(args.ClusterFile))
		if err = cf.Process(); err != nil {
			return err
		}
	}
	cluster := cf.GetCluster()

	// 添加跳过阶段的环境变量
	addSkipPhasesToEnv(&cluster, args.SkipPhases)

	// 若registry不是高可用模式，那么第一个master节点不能被重置,
	// 因为会失去registry导致后期集群拓展失败。
	if cluster.Spec.Registry.LocalRegistry != nil && *cluster.Spec.Registry.LocalRegistry.HA && unet.IsInIPList(cluster.GetMaster0IP(), ms) {
		return fmt.Errorf("the first master node [%s] of the cluster cann't be reseted when registry is no HA mode", cluster.GetMaster0IP())
	}

	var validMasters []net.IP
	for _, m := range ms {
		if unet.IsInIPList(m, cluster.GetMasterIPList()) {
			if unet.IsLocalIP(m, nil) {
				return fmt.Errorf("not allow delete master [%s] from itself, should Login other master to Exec this cmd", m)
			}
			validMasters = append(validMasters, m)
		}
	}
	// 重新赋值
	ms = validMasters

	var validWorkers []net.IP
	for _, w := range ws {
		if unet.IsInIPList(w, cluster.GetNodeIPList()) {
			validWorkers = append(validWorkers, w)
		}
	}
	// 重新赋值
	ws = validWorkers
	if len(ms) == 0 && len(ws) == 0 {
		klog.Warningf("The nodes what input none belong to the current cluster")
		return nil
	}

	// if !args.ForceDelete {
	// 	if err = confirmDeleteHosts(ms, ws); err != nil {
	// 		return err
	// 	}
	// }
	e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
	if err != nil {
		return err
	}

	imgId, err := e.Pull(&image.PullOpts{
		SkipTLSVerify: true,
		Quiet:         false,
		PullPolicy:    "missing",
		Image:         cluster.Spec.Image,
		Platform:      "local",
	})
	if err != nil {
		return err
	}
	imageSpec, err := e.Inspect(&image.InspectOpts{
		ImageNameOrID: imgId,
	})
	if err != nil {
		return err
	}
	clusterInstaller, err := NewClusterInstaller(cf, e, imageSpec)
	if err != nil {
		return err
	}

	return clusterInstaller.ScaleDown(ms, ws, clusterScaleDownOpts{Prune: args.Prune, IgnoreError: args.IgnoreError})
}

func confirmAction(forceDelete bool, confirmMsg, cancelMsg string) error {
	if !forceDelete {
		if yes, _ := interaction.Confirm(confirmMsg, cancelMsg); !yes {
			return ErrCancelled
		}
	}
	return nil
}

// addSkipPhasesToEnv 将跳过阶段的环境变量添加到集群环境变量中
// 如果环境变量已存在则更新其值，不存在则追加
// 例如: phase为container-runtime时，会添加skipContainerRuntime='true'到环境变量中
func addSkipPhasesToEnv(cluster *ctv1beta1.Cluster, phases []string) {
	for _, phase := range phases {
		skipVarName := fmt.Sprintf("skip%s", kebabCaseToPascalCase(phase))
		skipVarAssignment := fmt.Sprintf("%s='true'", skipVarName)

		// 检查环境变量是否已存在
		found := false
		for i, env := range cluster.Spec.Env {
			if strings.HasPrefix(env, skipVarName+"=") {
				cluster.Spec.Env[i] = skipVarAssignment
				found = true
				break
			}
		}

		// 如果不存在则追加
		if !found {
			cluster.Spec.Env = append(cluster.Spec.Env, skipVarAssignment)
		}
	}
}
