package cluster

import (
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/expansion"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	"k8s.io/klog/v2"
)

var (
	exampleApply = `
# 使用Clusterfile文件创建集群:
  kubepilot apply -f {$Path}/Clusterfile
`
)

// Deprecated
func newApplyCmd() *cobra.Command {
	f := &commands.ApplyFlags{}
	cmd := &cobra.Command{
		Use:     "apply",
		Short:   "通过Clusterfile文件安装集群(已弃用,使用run命令代替)",
		Args:    cobra.NoArgs,
		Example: exampleApply,
		RunE: func(cmd *cobra.Command, args []string) error {
			// TODO: 补全逻辑
			klog.Warningf("This feature has been Deprecated!!! Please use the run command")
			return nil
		},
		PostRun: func(cmd *cobra.Command, args []string) {
			klog.Info(expansion.Compeleted())
		},
	}
	flags := cmd.Flags()
	f.InitApplyCmdFlags(flags)

	return cmd
}
