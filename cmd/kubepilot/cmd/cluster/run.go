package cluster

import (
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/expansion"
	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	pkgcluster "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	etv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	utilerrs "k8s.io/apimachinery/pkg/util/errors"
	"k8s.io/klog/v2"
)

var longRunCmdDescription = `
  该命令用于创建不存在的集群或操作已有集群，以及通过tar或clusterfile镜像文件安装云原生应用组件.
`
var exampleRun = `
# Tip: 以下命令中的集群镜像名称可以替换为对应的tar文件 [kubernetes.tar/kubeverse.tar]

# 在服务器上创建最基本的集群:
  kubepilot run bingokube/kubernetes:v1.22.22 -m *************, *************, ************* -n ************, ************ \
	-u $username -p $password.

# 同时创建集群和安装应用组件:
  kubepilot run bingokube/kubernetes:v1.22.22 -c helm:v3.8.0, kube-nvs:v1.5.0, kubeverse:v1.4.3  -m *************, *************, ************* -n ************, ************ \
  -u $username -p $password.

# 创建单一master节点的集群:
  kubepilot run bingokube/kubernetes:v1.22.22

# 使用自定义Env变量创建集群/组件(value可以是JSON格式的字符串,可以是普通的字符串):
  kubepilot run -c $component -e "key1=value2，key2=value2" bingokube/kubeverse:v1.5.0 -m xxx -n xxx -p $password
  kubepilot run -c registry.bingosoft.net/bingokube/kube-nvs:v1.5.0 -e "key1=[{"a":"1","b":2,"c":[1,2,3]},{"a":"2","b":3,"c":[4,5,6]}],key2=value2,...."

# 使用新的Clusterfile文件创建集群:
  kubepilot run -f Clusterfile

# 使用当前Clusterfile文件 + 参数创建集群
  kubepilot run -f Clusterfile -m xxx -n xxx -e "k1=v1"
`

// newRunCmd 创建集群或组件
func newRunCmd() *cobra.Command {
	f := &commands.RunFlags{}
	cmd := &cobra.Command{
		Use:     "run",
		Short:   "安装Kubernetes集群或在集群上云原生应用组件",
		Long:    longRunCmdDescription,
		Example: exampleRun,
		Args:    cobra.MaximumNArgs(1),
		RunE:    runCmd(f),

		PreRunE: func(cmd *cobra.Command, args []string) error {
			return f.Validate()
		},
		// run命令完成后打印 kubepilot Logo info
		PostRun: func(cmd *cobra.Command, args []string) {
			klog.Info(expansion.Compeleted())
		},
	}
	// setRequireBuildahAnnotation(runCmd)
	fs := cmd.Flags()
	f.InitRunCmdFlags(fs)

	return cmd
}

func runCmd(f *commands.RunFlags) func(cmd *cobra.Command, args []string) error {
	return func(cmd *cobra.Command, args []string) error {
		// 这里看起来似乎有逻辑bug,例如：若组件不为空，args和Clusterfile为空,且集群不存在的情况就可以往下执行？
		// 其实不然😊后面代码会对这种情况进行过滤阻止的,毕竟直接在这里判断拦截则太多`if-else`会让代码臃肿不堪!
		if len(args) == 0 && len(f.Components) == 0 && f.ClusterFile == "" {
			return fmt.Errorf("must be input cluster image or use Clusterfile to deploy")
		}
		switch {
		case len(f.ClusterFile) > 0:
			// demo: kubepilot run -f Clusterfile ...
			return runWithClusterfile(f)
		default:
			// demo: kubepilot run kubernetes:v1.22.22 -m xxx -n xxx ...
			return runWithClusterImage(args, f)
		}
	}
}

// runWithClusterfile 基于Clusterfile安装集群或者应用
func runWithClusterfile(args *commands.RunFlags) error {
	// 初始化image engine
	e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
	if err != nil {
		return fmt.Errorf("failed to new image engine: %v", err)
	}
	// 需要判断集群是否已经存在，若存在则慎重执行Clusterfile
	var currentImage string
	if IsClusterExist() {
		m, _, err := pkgcluster.GetActualClusterFile()
		if err != nil {
			return err
		}
		currentImage = m.GetCluster().Spec.Image
	}

	cf := pkgcluster.NewClusterfile(pkgcluster.WithPath(args.ClusterFile),
		pkgcluster.WithValueFiles(args.Values),
	)
	if err = cf.Process(); err != nil {
		return err
	}

	cluster, err := mergeArgs(currentImage, cf.GetCluster(), args)
	if err != nil {
		return err
	}
	// 重新赋值clusterfile
	cf.SetCluster(*cluster)
	img := cluster.Spec.Image
	if err = handleClusterfile(img, cf, cluster, args, e); err != nil {
		klog.Exitf("Failed to install cluster image [%s], no need to continue with the next component: %v", img, err)
	}

	var pendingInstalled []string
	pendingInstalled = append(pendingInstalled, cluster.Spec.Components...)

	// TODO: 这里若能确认组件没有任何依赖，则可以并发式安装
	for len(pendingInstalled) > 0 {
		component := pendingInstalled[0]
		if err = handleClusterfile(component, cf, cluster, args, e); err != nil {
			klog.Exitf("Failed to install component [%s]: %v", component, err)
		}
		pendingInstalled = pendingInstalled[1:]
	}
	klog.Info("Success install with Clusterfile")

	return nil
}

// runWithClusterImage 基于Cluster image 安装集群或者应用
func runWithClusterImage(img []string, args *commands.RunFlags) error {
	e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
	if err != nil {
		return fmt.Errorf("failed to new image engine: %v", err)
	}
	if len(img) > 0 {
		// inspect image
		imageSpec, err := pkgcluster.GetImageInfo(img[0])
		if err != nil {
			return fmt.Errorf("unable to get image info [id=%s]: %v", img, err)
		}
		if !imageSpec.IsRootfs() {
			klog.Exit("The image is not k8s cluster image!")
		}
		if err = handleClusterImage(img[0], args, e, imageSpec); err != nil {
			klog.Exitf("failed to install cluster image [%s], no need to continue with the next component: %v", img, err)
		}
	}
	var pendingInstalled []string
	// 组件集群镜像列表
	pendingInstalled = append(pendingInstalled, args.Components...)

	// TODO: 这里若能确认组件没有任何依赖，则可以并发式安装
	var errs []error
	for _, v := range pendingInstalled {
		component := v
		// inspect image
		imageSpec, err := pkgcluster.GetImageInfo(component)
		if err != nil {
			return fmt.Errorf("unable to get image info [id=%s]: %v", img, err)
		}
		// 防止用户填参数的时候搞错组件镜像类型
		if !imageSpec.IsApplication() {
			errs = append(errs, fmt.Errorf("the type of component [%s] is not application, it will be skip install", component))
			continue
		}
		if err = handleClusterImage(component, args, e, imageSpec); err != nil {
			errs = append(errs, fmt.Errorf("failed to install component [%s]: %v", component, err))
		}
	}
	if len(errs) > 0 {
		return utilerrs.NewAggregate(errs)
	}
	klog.Info("Success install with Cluster images")

	return nil
}

// handleClusterfile 通过Clusterfile安装
func handleClusterfile(img string, cf pkgcluster.Manager, cluster *ctv1beta1.Cluster, args *commands.RunFlags, e imageengine.ImageManager) error {
	// inspect image
	imageSpec, err := pkgcluster.GetImageInfo(img)
	if err != nil {
		return fmt.Errorf("unable to get image info [name=%s]: %v", img, err)
	}

	if imageSpec.ImageExtension.Type == etv1beta1.AppType {
		// 说明是安装应用组件
		app := application.InitApplication(nil, cluster.Spec.CMD, cluster.Spec.APPNames, args.CustomEnv, img)
		return installApp(img, app, e, imageSpec, constants.InstallWithClusterfile, args.CustomEnv, args.Mode, args.IgnoreCache, commands.DistributionMode(args.DistributeMode))
	}
	return installCluster(img, cf, e, imageSpec, constants.InstallWithClusterfile, args.Mode, args.IgnoreCache, commands.DistributionMode(args.DistributeMode))
}

// handleClusterImage 通过集群镜像安装
func handleClusterImage(img string, args *commands.RunFlags, e imageengine.ImageManager, imageSpec *etv1beta1.ImageSpec) error {
	klog.V(5).Infof("start handle cluster image %s", img)
	if imageSpec.ImageExtension.Type == etv1beta1.AppType {
		// 说明是安装应用组件
		app := application.InitApplication(nil, args.Cmds, args.AppNames, args.CustomEnv, img)
		return installApp(img, app, e, imageSpec, constants.InstallWithClusterImage, args.CustomEnv, args.Mode, args.IgnoreCache,
			commands.DistributionMode(args.DistributeMode))
	}
	// 从cluster image和args构建出cluster
	cluster, err := pkgcluster.BuildClusterStruct(img, args)
	if err != nil {
		return fmt.Errorf("unable to build cluster struct: %v", err)
	}

	cf := pkgcluster.NewClusterfile(pkgcluster.WithClusterInfo(cluster))
	if err = cf.Process(); err != nil {
		return err
	}

	return installCluster(img, cf, e, imageSpec, constants.InstallWithClusterImage, args.Mode, args.IgnoreCache, commands.DistributionMode(args.DistributeMode))
}

// installApp 安装业务组件
func installApp(img string, app *appv1beta1.Application, e imageengine.ImageManager,
	imageSpec *etv1beta1.ImageSpec, mode constants.InstallMode, customEnv []string, runMode string, ignoreCache bool,
	distributeMode commands.DistributionMode) error {

	appInstaller, err := NewApplicationInstaller(app, imageSpec.ImageExtension, e)
	if err != nil {
		return fmt.Errorf("unable to init application installer: %v", err)
	}
	return appInstaller.Install(img, appInstallOpts{
		InstallMode:  mode,
		Envs:         customEnv,
		RunMode:      runMode,
		IgnoreCache:  ignoreCache,
		Distribution: distributeMode,
	})
}

// installCluster 安装集群
func installCluster(img string, cf pkgcluster.Manager, e imageengine.ImageManager,
	imageSpec *etv1beta1.ImageSpec, mode constants.InstallMode, runMode string, ignoreCache bool,
	distributeMode commands.DistributionMode) error {

	clusterInstaller, err := NewClusterInstaller(cf, e, imageSpec)
	if err != nil {
		return fmt.Errorf("unable to init cluster installer: %v", err)
	}
	return clusterInstaller.Install(img, clusterInstallOpts{
		InstallMode:    mode,
		RunMode:        runMode,
		IgnoreCache:    ignoreCache,
		DistributeMode: distributeMode,
	})
}

// mergeArgs merge cluster args
func mergeArgs(currentImage string, cluster ctv1beta1.Cluster, args *commands.RunFlags) (*ctv1beta1.Cluster, error) {
	return pkgcluster.MergeArgsToCluster(currentImage, cluster, &commands.MergeFlags{
		Masters:    args.Masters,
		Nodes:      args.Nodes,
		User:       args.User,
		Password:   args.Password,
		Port:       args.Port,
		Pk:         args.Pk,
		PkPassword: args.PkPassword,
		CustomEnv:  args.CustomEnv,
		Cmds:       args.Cmds,
		AppNames:   args.AppNames,
		Components: args.Components,
	})
}
