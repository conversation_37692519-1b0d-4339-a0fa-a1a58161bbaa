package cluster

import (
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/app"
	pkgcf "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	etv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
)

type DeployCluster struct {
}

type DeployClusterOpts struct {
}

type DeployApp struct {
	cf             pkgcf.Manager
	imageExtension etv1beta1.ImageExtension
	infraDriver    infradriver.InfraDriver
	appDriver      app.ApplicationDriver
	imageEngine    imageengine.ImageManager
}

type DeployAppOpts struct {
	// Envs 环境变量
	Envs []string
	// RunMode 运行模式
	RunMode string
	// IgnoreExistApp 是否忽略已安装的App(集群升级)
	IgnoreExistApp bool
	// IgnoreCache 是否忽略镜像缓存
	IgnoreCache bool
	// DistributeMode 镜像分发模式
	DistributeMode commands.DistributionMode
}
