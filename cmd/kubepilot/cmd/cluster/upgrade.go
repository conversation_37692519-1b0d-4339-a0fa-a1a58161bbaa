package cluster

import (
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/expansion"
	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	clusterruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-runtime"
	pkgcf "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	pkgcluster "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	etv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/plugin"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	utilerrs "k8s.io/apimachinery/pkg/util/errors"
	"k8s.io/klog/v2"
)

// appUpgradeOpts 升级应用时所需参数
type appUpgradeOpts struct {
	Envs                    []string
	RunMode                 string
	SkipPrepareAppMaterials bool
	IgnoreCache             bool
	Distribution            commands.DistributionMode
	DistributionDir         string
	Force                   bool
}

var longUpgradeCmdDescription = `
  该命令用于升级集群上的云原生应用组件.
`
var exampleUpgrade = `
# 升级应用组件:
  kubepilot upgrade -c registry.bingosoft.net/bingokube/cluster-image/metrics-server:v1.0.0 
`

// newUpgradeCmd 升级组件
func newUpgradeCmd() *cobra.Command {
	f := &commands.UpgradeFlags{}
	cmd := &cobra.Command{
		Use:     "upgrade",
		Short:   "升级集群上云原生应用组件",
		Long:    longUpgradeCmdDescription,
		Args:    cobra.NoArgs,
		Example: exampleUpgrade,
		RunE:    upgradeCmd(f),
		PostRun: func(cmd *cobra.Command, args []string) {
			klog.Info(expansion.Compeleted())
		},
	}
	fs := cmd.Flags()
	f.InitUpgradeCmdFlags(fs)

	return cmd
}

func upgradeCmd(f *commands.UpgradeFlags) func(cmd *cobra.Command, args []string) error {
	return func(cmd *cobra.Command, args []string) error {
		if len(f.Components) == 0 {
			return fmt.Errorf("must be input cluster image or use Clusterfile to deploy")
		}

		e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
		if err != nil {
			return fmt.Errorf("failed to new image engine: %v", err)
		}
		var pendingUpgraded []string
		// 组件集群镜像列表
		pendingUpgraded = append(pendingUpgraded, f.Components...)

		// TODO: 这里若能确认组件没有任何依赖，则可以并发式升级
		var errs []error
		for _, v := range pendingUpgraded {
			component := v
			// inspect image
			imageSpec, err := pkgcluster.GetImageInfo(component)
			if err != nil {
				return fmt.Errorf("unable to get image info [id=%s]: %v", component, err)
			}
			// 防止用户填参数的时候搞错组件镜像类型
			if !imageSpec.IsApplication() {
				errs = append(errs, fmt.Errorf("the type of component [%s] is not application, it will be skip upgrade", component))
				continue
			}
			app := application.InitApplication(nil, nil, nil, f.CustomEnv, component)
			if err = upgradeApp(component, app, e, imageSpec, f.Mode, f.IgnoreCache, commands.DistributionMode(f.DistributeMode), f.CustomEnv, f.Force); err != nil {
				errs = append(errs, fmt.Errorf("failed to upgrade component [%s]: %v", component, err))
			}
		}
		if len(errs) > 0 {
			return utilerrs.NewAggregate(errs)
		}
		klog.Info("Success upgrade with Cluster images")
		return nil
	}
}

// upgradeApp 升级组件
func upgradeApp(img string, app *appv1beta1.Application, e imageengine.ImageManager,
	imageSpec *etv1beta1.ImageSpec, runMode string, ignoreCache bool,
	distributeMode commands.DistributionMode, customEnv []string, force bool) error {

	appInstaller, err := NewApplicationInstaller(app, imageSpec.ImageExtension, e)
	if err != nil {
		return fmt.Errorf("unable to init application installer: %v", err)
	}
	return appInstaller.Upgrade(img, appUpgradeOpts{
		Envs:         customEnv,
		RunMode:      runMode,
		IgnoreCache:  ignoreCache,
		Distribution: distributeMode,
		Force:        force,
	})
}

// Upgrade 执行升级组件
func (app *AppInstaller) Upgrade(imageName string, options appUpgradeOpts) (err error) {
	var (
		isChanged bool
		handler   plugin.Handler
	)

	klog.Infof("Start to upgrade application using image [%s]", imageName)

	app.infraDriver.AddApplicationEnv(options.Envs)
	app.infraDriver.AddClusterEnv(options.Envs)

	isChanged, err = app.cfManager.CheckComponentVersion(app.appDriver.GetApplication())
	if err != nil {
		return err
	}
	// 若版本不发生改变并且不是强制升级,则仅更新helm相关的参数,因此单独分发etc目录下的文件即可,其他目录下的文件不用分发
	if !isChanged && !options.Force {
		options.DistributionDir = constants.Etc
	}

	if !options.SkipPrepareAppMaterials {
		if err = app.prepareMaterials(imageName, options.RunMode, options.IgnoreCache, options.Distribution, options.DistributionDir); err != nil {
			return err
		}
	}
	// 仅仅是加载镜像
	if options.RunMode == string(constants.LoadImage) {
		return nil
	}

	defer func() {
		err = app.handleError(err, func() appv1beta1.ApplicationCondition { return appv1beta1.NewFailedUpgradeAppCondition(err.Error()) }, app.imgExt)
	}()

	// 若是新组件,则需要执行升级相关的插件操作,并且移除clusterfile文件中旧组件的相关操作记录,若非新组件则跳过此步骤
	if isChanged || options.Force {
		handler, err = plugin.NewHookOptions(app.plugins, app.infraDriver)
		if err != nil {
			return fmt.Errorf("failed to init plugin handler: %v", err)
		}
		app.appDriver.SetPluginHandler(handler)
		klog.V(5).Info("success to set plugin handler")
	}

	// 在升级之前，先往clusterfile文件保存application信息，防止升级成功但保存application信息失败，无法保存application参数
	confPath := clusterruntime.GetClusterConfPath(app.imgExt.Labels)
	if err = app.cfManager.SaveAll(pkgcf.SaveOptions{SaveToCluster: true, ConfigPath: confPath, Application: app.resetApplication(appv1beta1.NewSuccessUpgradeAppCondition(), appv1beta1.AppProcess, app.imgExt)}); err != nil {
		return err
	}

	if err = app.appDriver.Upgrade(app.infraDriver); err != nil {
		return err
	}

	if err = app.appDriver.Save(); err != nil {
		return SaveErr{Err: fmt.Errorf("the application[%s] upgrade succeeded, but failed to save the application.json,err:%v,"+
			"for synchronous cluster component state, please manually modify the application.json and clusterfile，"+
			"the application.json path is %s, the clusterfile is configmap in the kube-system namespace", imageName, err, file.GetDefaultApplicationFile()).Error()}
	}
	// 升级成功刷新应用状态，往clusterfile文件保存application信息
	confPath = clusterruntime.GetClusterConfPath(app.imgExt.Labels)

	if err = app.cfManager.SaveAll(pkgcf.SaveOptions{SaveToCluster: true, ConfigPath: confPath, Application: app.resetApplication(appv1beta1.NewSuccessUpgradeAppCondition(), appv1beta1.AppSuccess, app.imgExt)}); err != nil {
		return SaveErr{
			Err: fmt.Errorf("the application[%s] upgrade succeeded, but failed to save the clsterfile file,err:%v,"+
				"for synchronous cluster component state, please manually modify the clusterfile,the clusterfile is configmap in the kube-system namespace", imageName, err).Error()}
	}

	klog.Infof("succeeded in upgradeing application with image %s", imageName)

	return nil
}
