package cluster

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"k8s.io/klog/v2"
)

var (
	exampleCerts = `
# 为所有控制平面证书生成新的api服务器证书和密钥:
  kubepilot certs --alt-names *************,bingokube.bingosoft.net
`
	longCertsCmdDescription = `
  此命令将在证书中添加新的域名或IP地址，以更新集群API服务器.sealer在证书过程中内置了一些默认域和IP: localhost,
出站IP地址和一些与kubeadm.yml配置的apiserver CertSANs密切相关的DNS域.使用kubepilot certs后,您需要手动重启API服务器,
然后可以使用命令 "openssl x509 -noout -text -in apiserver.crt" 查看证书详情.
`
)

// newCertsCmd 证书相关
func newCertsCmd() *cobra.Command {
	f := &commands.CertsFlags{}
	cmd := &cobra.Command{
		Use:     "certs",
		Short:   "更新kube-apiserver的证书",
		Args:    cobra.NoArgs,
		Long:    longCertsCmdDescription,
		Example: exampleCerts,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(f.AltNames) == 0 {
				return errors.New("IP addr or dns domain is empty")
			}
			cf, _, err := clusterfile.GetActualClusterFile()
			if err != nil {
				return err
			}
			cluster := cf.GetCluster()
			driver, err := infradriver.NewInfraDriver(&cluster)
			if err != nil {
				return err
			}
			// 实际是调用pilotctl命令更新证书
			certCmd := fmt.Sprintf("pilotctl certs update --alt-names %s", strings.Join(f.AltNames, ","))
			// 更新所有master节点的api证书
			for _, ip := range cluster.GetMasterIPList() {
				if err = driver.CmdAsync(ip, nil, certCmd); err != nil {
					return fmt.Errorf("failed to update certs for [%s]: %v", ip, err)
				}
			}
			if f.Wait {
				time.Sleep(30 * time.Second)
			}
			klog.Info("success to update certs")

			return nil
		},
	}
	fs := cmd.Flags()
	f.InitCertsFlags(fs)

	return cmd
}
