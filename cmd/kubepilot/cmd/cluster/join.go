package cluster

import (
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	pkgcf "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/strs"
)

var (
	exampleJoinCmd = `
# 为默认集群添加节点:
  kubepilot join -m ************* -n ************* -p $yourpassword.
`
)

// newJoinCmd 添加节点
func newJoinCmd() *cobra.Command {
	f := &commands.JoinFlags{}
	cmd := &cobra.Command{
		Use:     "join",
		Short:   "通过IP地址添加master或者worker节点到指定集群",
		Args:    cobra.NoArgs,
		Example: exampleJoinCmd,
		RunE: func(cmd *cobra.Command, args []string) error {
			return run(f)
		},
	}
	flags := cmd.Flags()
	f.InitJoinCmdFlags(flags)

	return cmd
}

func run(args *commands.JoinFlags) error {
	var (
		manager pkgcf.Manager
		err     error
	)

	if err = unet.ValidateScaleIPStr(args.Masters, args.Nodes); err != nil {
		return fmt.Errorf("failed to validate input run args: %v", err)
	}

	msList, nsList, err := unet.ParseToNetIPList(args.Masters, args.Nodes)
	if err != nil {
		return fmt.Errorf("failed to parse ip string to net IP list: %v", err)
	}

	manager, _, err = pkgcf.GetActualClusterFile()
	if err != nil {
		return err
	}

	cluster := manager.GetCluster()
	client := pkgcf.GetClusterClient()
	if client == nil {
		return fmt.Errorf("failed to get cluster client")
	}

	currentCluster, err := pkgcf.GetCurrentCluster(client)
	if err != nil {
		return fmt.Errorf("failed to get current cluster: %v", err)
	}
	currentNodes := currentCluster.GetAllIPList()

	mj, _ := strs.Diff(currentNodes, msList)
	nj, _ := strs.Diff(currentNodes, nsList)

	if len(mj) == 0 && len(nj) == 0 {
		return fmt.Errorf("scale ip %v is already in the current cluster %v", append(msList, nsList...), currentNodes)
	}

	if err = pkgcf.ConstructClusterForScaleUp(clusterfile.ClusterInfo{Masters: mj, Workers: nj, CurrentNodes: currentNodes, Cluster: &cluster, ScaleFlags: args}); err != nil {
		return err
	}

	e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
	if err != nil {
		return err
	}

	imgId, err := e.Pull(&image.PullOpts{
		SkipTLSVerify: true,
		Quiet:         false,
		PullPolicy:    "missing",
		Image:         cluster.Spec.Image,
		Platform:      "local",
	})
	if err != nil {
		return err
	}

	imageSpec, err := e.Inspect(&image.InspectOpts{ImageNameOrID: imgId})
	if err != nil {
		return fmt.Errorf("failed to get cluster image extension: %s", err)
	}
	// 将cluster image中的Env合并到cluster中
	mergedWithExt := pkgcf.MergeClusterWithImageExtension(&cluster, imageSpec.ImageExtension)

	manager.SetCluster(*mergedWithExt)

	clusterInstaller, err := NewClusterInstaller(manager, e, imageSpec)
	if err != nil {
		return err
	}
	// 拓展节点
	return clusterInstaller.ScaleUp(mj, nj, clusterScaleUpOpts{IgnoreCache: args.IgnoreCache, Args: args})
}
