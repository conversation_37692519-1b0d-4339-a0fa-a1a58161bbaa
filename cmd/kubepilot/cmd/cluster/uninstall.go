package cluster

import (
	"errors"
	"fmt"
	"path/filepath"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/kubepilot/cmd/expansion"
	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	clusterruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-runtime"
	pkgcf "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	pkgcluster "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	etv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/plugin"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	utilerrs "k8s.io/apimachinery/pkg/util/errors"
	"k8s.io/klog/v2"
)

type SaveErr struct {
	Err string
}

func (s SaveErr) Error() string {
	return s.Err
}

// appUninstallOpts 卸载应用时所需参数
type appUninstallOpts struct {
	RunMode string
}

var longUninstallCmdDescription = `
  该命令用于卸载集群上的云原生应用组件.
`
var exampleUninstall = `
# 卸载应用组件:
  kubepilot uninstall -c registry.bingosoft.net/bingokube/cluster-image/metrics-server:v1.0.0
`

// newUninstallCmd 卸载组件
func newUninstallCmd() *cobra.Command {
	f := &commands.UninstallFlags{}
	cmd := &cobra.Command{
		Use:     "uninstall",
		Short:   "卸载集群上云原生应用组件",
		Long:    longUninstallCmdDescription,
		Args:    cobra.NoArgs,
		Example: exampleUninstall,
		RunE:    unInstallCmd(f),
		PostRun: func(cmd *cobra.Command, args []string) {
			klog.Info(expansion.Compeleted())
		},
	}
	fs := cmd.Flags()
	f.InitUninstallCmdFlags(fs)

	return cmd
}

func unInstallCmd(f *commands.UninstallFlags) func(cmd *cobra.Command, args []string) error {
	return func(cmd *cobra.Command, args []string) error {
		if len(f.Components) == 0 {
			return fmt.Errorf("must be input cluster image or use Clusterfile to deploy")
		}

		e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
		if err != nil {
			return fmt.Errorf("failed to new image engine: %v", err)
		}
		var pendingUnInstalled []string
		// 组件集群镜像列表
		pendingUnInstalled = append(pendingUnInstalled, f.Components...)

		// TODO: 这里若能确认组件没有任何依赖，则可以并发式卸载
		var errs []error
		for _, v := range pendingUnInstalled {
			component := v
			// inspect image
			imageSpec, err := pkgcluster.GetImageInfo(component)
			if err != nil {
				return fmt.Errorf("unable to get image info [id=%s]: %v", component, err)
			}
			// 防止用户填参数的时候搞错组件镜像类型
			if !imageSpec.IsApplication() {
				errs = append(errs, fmt.Errorf("the type of component [%s] is not application, it will be skip install", component))
				continue
			}
			app := application.InitApplication(nil, nil, nil, f.CustomEnv, component)
			if err = unInstallApp(component, app, e, imageSpec); err != nil {
				errs = append(errs, fmt.Errorf("failed to uninstall component [%s]: %v", component, err))
			}
		}
		if len(errs) > 0 {
			return utilerrs.NewAggregate(errs)
		}
		klog.Info("Success uninstall with Cluster images")
		return nil
	}
}

// unInstallApp 卸载组件
func unInstallApp(img string, app *appv1beta1.Application, e imageengine.ImageManager,
	imageSpec *etv1beta1.ImageSpec) error {

	appInstaller, err := NewApplicationInstaller(app, imageSpec.ImageExtension, e)
	if err != nil {
		return fmt.Errorf("unable to init application installer: %v", err)
	}
	return appInstaller.Uninstall(img, appUninstallOpts{})
}

// Uninstall 执行卸载组件
func (app *AppInstaller) Uninstall(imageName string, options appUninstallOpts) (err error) {
	var (
		handler plugin.Handler
		isExist bool
	)
	klog.Infof("Start to uninstall application using image [%s]", imageName)

	for _, item := range app.cfManager.GetApplication() {
		if item.GetName() == imageName {
			isExist = true
			break
		}
	}
	if !isExist {
		return fmt.Errorf("cannot uninstall because the application[%s] does not exist", imageName)
	}

	defer func() {
		err = app.handleError(err, func() appv1beta1.ApplicationCondition { return appv1beta1.NewFailedUnInstallAppCondition(err.Error()) }, app.imgExt)
	}()

	plugins, err := plugin.LoadPluginsFromFile(filepath.Join(filepath.Join(app.infraDriver.GetClusterRootfsPath(), string(app.appDriver.GetApplication().UID)), constants.Plugins))
	if err != nil {
		return
	}

	// 根据组件名称过滤出跟该组件相关的插件
	app.plugins, err = filterPlugins(app.appDriver.GetApplication().Name, plugins)
	if err != nil {
		return err
	}

	handler, err = plugin.NewHookOptions(app.plugins, app.infraDriver)
	if err != nil {
		return fmt.Errorf("failed to init plugin handler: %v", err)
	}
	app.appDriver.SetPluginHandler(handler)
	klog.V(5).Info("success to set plugin handler")

	if err = app.appDriver.Uninstall(app.infraDriver); err != nil {
		return err
	}

	if err = app.appDriver.Save(); err != nil {
		return SaveErr{Err: fmt.Errorf("the application[%s] uninstall succeeded, but failed to save the application.json,err:%v,"+
			"for synchronous cluster component state, please manually modify the application.json and clusterfile，"+
			"the application.json path is %s, the clusterfile is configmap in the kube-system namespace", imageName, err, file.GetDefaultApplicationFile()).Error()}
	}

	confPath := clusterruntime.GetClusterConfPath(app.imgExt.Labels)

	if err = app.cfManager.SaveAll(pkgcf.SaveOptions{SaveToCluster: true, ConfigPath: confPath, Application: app.resetApplication(appv1beta1.NewSuccessUnInstallAppCondition(), appv1beta1.AppSuccess, app.imgExt)}); err != nil {
		return SaveErr{
			Err: fmt.Errorf("the application[%s] uninstall succeeded, but failed to save the clsterfile file,err:%v,"+
				"for synchronous cluster component state, please manually modify the clusterfile,the clusterfile is configmap in the kube-system namespace", imageName, err).Error()}
	}

	klog.Infof("succeeded in uninstalling application with image %s", imageName)

	return nil
}

func (app *AppInstaller) handleError(err error, conditionFunc func() appv1beta1.ApplicationCondition, imgExt etv1beta1.ImageExtension) error {
	if err == nil {
		return nil
	}
	if errors.As(err, &SaveErr{}) {
		return err
	}
	confPath := clusterruntime.GetClusterConfPath(imgExt.Labels)
	if saveErr := app.cfManager.SaveAll(pkgcf.SaveOptions{SaveToCluster: true, ConfigPath: confPath, Application: app.resetApplication(conditionFunc(), appv1beta1.AppFailed, imgExt)}); saveErr != nil {
		return fmt.Errorf("%v,err:%v,and failed to save cluster configuration to file, err:%v", conditionFunc().Type, err, saveErr)
	}
	return err
}
