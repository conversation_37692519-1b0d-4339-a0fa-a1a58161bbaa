package cluster

import (
	"context"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strings"

	"github.com/pkg/errors"
	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	pgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/plugin/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/app"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/client/k8s"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/nodeconfig"
	clusterruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-runtime"
	clusterfileutiles "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	pkgcf "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	containerruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/container-runtime"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	etv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/env"
	imagedistributor "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-distributor"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	imageutils "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image/util"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/plugin"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"golang.org/x/sync/errgroup"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog/v2"
)

// AppInstaller 应用安装器application installer
type AppInstaller struct {
	cfManager   pkgcf.Manager
	imgExt      etv1beta1.ImageExtension
	infraDriver infradriver.InfraDriver
	appDriver   app.ApplicationDriver
	imgEngine   imageengine.ImageManager
	plugins     []pgv1beta1.Plugin
}

// appInstallOpts 安装应用时所需参数
type appInstallOpts struct {
	InstallMode             constants.InstallMode
	Envs                    []string
	RunMode                 string
	SkipPrepareAppMaterials bool
	IgnoreCache             bool
	Distribution            commands.DistributionMode
	Hosts                   []net.IP
}

// PluginInstaller Plugin installer
type PluginInstaller struct {
	infraDriver infradriver.InfraDriver
	imgEngine   imageengine.ImageManager
	plugins     []pgv1beta1.Plugin
}

// Install 执行安装
func (app *AppInstaller) Install(imageName string, options appInstallOpts) (err error) {
	var (
		handler plugin.Handler
	)
	klog.Infof("Start to install application using image [%s] with install mode [%s]", imageName, options.InstallMode)

	app.infraDriver.AddApplicationEnv(options.Envs)
	app.infraDriver.AddClusterEnv(options.Envs)

	// 在安装之前，先往clusterfile文件保存application信息，防止出现应用安装成功但保存clusterfile文件失败的情况，application参数丢失
	confPath := clusterruntime.GetClusterConfPath(app.imgExt.Labels)
	if err = app.cfManager.SaveAll(pkgcf.SaveOptions{SaveToCluster: true, ConfigPath: confPath, Application: app.resetApplication(appv1beta1.NewSuccessInstallingAppCondition(), appv1beta1.AppProcess, app.imgExt)}); err != nil {
		return err
	}

	if !options.SkipPrepareAppMaterials {
		if err = app.prepareMaterials(imageName, options.RunMode, options.IgnoreCache, options.Distribution, ""); err != nil {
			return err
		}
	}
	// 仅仅是加载镜像
	if options.RunMode == string(constants.LoadImage) {
		return nil
	}

	defer func() {
		err = app.handleError(err, func() appv1beta1.ApplicationCondition { return appv1beta1.NewFailedInstallAppCondition(err.Error()) }, app.imgExt)
	}()

	name, _, err := imageutils.ParseComponentFromImageName(imageName)
	if err != nil {
		return err
	}
	app.infraDriver.SetApplicationName(name)

	handler, err = plugin.NewHookOptions(app.plugins, app.infraDriver)
	if err != nil {
		return fmt.Errorf("failed to init plugin handler: %v", err)
	}
	app.appDriver.SetPluginHandler(handler)
	klog.V(5).Info("success to set plugin handler")

	if err = app.appDriver.Install(app.infraDriver); err != nil {
		return err
	}
	if err = app.appDriver.Save(); err != nil {
		return SaveErr{Err: fmt.Errorf("the application[%s] install succeeded, but failed to save the application.json,err:%v,"+
			"for synchronous cluster component state, please manually modify the application.json and clusterfile，"+
			"the application.json path is %s, the clusterfile is configmap in the kube-system namespace", imageName, err, file.GetDefaultApplicationFile()).Error()}
	}
	// 安装成功后仅刷新应用状态，往clusterfile文件保存application信息
	if err = app.cfManager.SaveAll(pkgcf.SaveOptions{SaveToCluster: true, ConfigPath: confPath, Application: app.resetApplication(appv1beta1.NewSuccessInstallAppCondition(), appv1beta1.AppSuccess, app.imgExt)}); err != nil {
		return SaveErr{
			Err: fmt.Errorf("the application[%s] install succeeded, but failed to save the clsterfile file,err:%v,"+
				"for synchronous cluster component state, please manually modify the clusterfile,the clusterfile is configmap in the kube-system namespace", imageName, err).Error()}
	}

	klog.Infof("succeeded in installing application with image %s", imageName)

	return nil
}

// convertPlugins 将 pgv1beta1.Plugin 类型的插件列表转换为 appv1beta1.Plugin 类型的列表
func convertPlugins(appInstallerPlugins []pgv1beta1.Plugin) []appv1beta1.Plugin {
	var applicationPlugins []appv1beta1.Plugin
	for _, p := range appInstallerPlugins {
		// 创建新的 appv1beta1.Plugin 对象,只保留 Name 和 Scope 字段
		applicationPlugin := appv1beta1.Plugin{
			Name:  p.Name,
			Scope: p.Spec.Scope,
		}
		// 将转换后的插件添加到结果列表中
		applicationPlugins = append(applicationPlugins, applicationPlugin)
	}
	return applicationPlugins
}

// resetApplication 更新application状态
func (app *AppInstaller) resetApplication(condition appv1beta1.ApplicationCondition, phase appv1beta1.ApplicationPhase, imgExt etv1beta1.ImageExtension) *appv1beta1.Application {
	klog.V(5).Info("update application status")
	info := app.appDriver.GetApplication()
	info.Spec.Cmds = imgExt.Launch.Cmds
	if len(imgExt.Labels[appv1beta1.KubepilotPreApplicationsLabelKey]) > 0 {
		info.Spec.PreApplications = strings.Split(imgExt.Labels[appv1beta1.KubepilotPreApplicationsLabelKey], ",")
	}
	info.Status.Phase = phase
	info.Status.Conditions = append(info.Status.Conditions, condition)
	info.Spec.Plugins = convertPlugins(app.plugins)
	return &info
}

// prepareMaterials 主要是将组件的image文件上传到集群节点的regsitry中
func (app *AppInstaller) prepareMaterials(appImageName string, mode string, ignoreCache bool, distribution commands.DistributionMode, distributionDir string) error {
	clusterHosts := app.infraDriver.GetHostIPList()
	// One platform corresponds to multiple ip,
	// Nodes in a cluster maybe have multiple platforms,
	// So classify all the hosts.
	clusterHostsPlatform, err := app.infraDriver.GetHostsPlatform(clusterHosts)
	if err != nil {
		return err
	}
	// 获取clusterfile 架构配置
	clusterEnv := app.infraDriver.GetClusterEnv()
	clusterOsPlatformValueStr := clusterEnv[constants.EnvClusterPlatform]
	// 解析架构配置
	osPlatforms, err := clusterfileutiles.ParseClusterOsPlatform(clusterOsPlatformValueStr)
	if err != nil {
		return err
	}
	// 注入集群的多架构配置
	clusterfileutiles.AppendOsPlatform(clusterHostsPlatform, osPlatforms)
	imageMounter, err := imagedistributor.NewImageMounter(app.imgEngine, clusterHostsPlatform)
	if err != nil {
		return err
	}

	imageMountInfo, err := imageMounter.Mount(appImageName)
	if err != nil {
		return err
	}
	klog.V(5).Infof("The component image mount info: [%v]", imageMountInfo)

	defer func() {
		if err = imageMounter.Umount(appImageName, imageMountInfo); err != nil {
			klog.Errorf("failed to umount cluster image: %v", err)
		}
	}()

	for _, info := range imageMountInfo {
		if err = app.appDriver.FileProcess(info.MountDir); err != nil {
			return errors.Wrapf(err, "failed to execute file processor")
		}
	}

	var distributor imagedistributor.Distributor
	switch distribution {
	case commands.P2PDistribution:
		distributor, err = imagedistributor.NewP2PDistributor(imageMountInfo, app.infraDriver, nil, imagedistributor.DistributeOption{
			IgnoreCache:     ignoreCache,
			DistributionDir: distributionDir,
		})
		if err != nil {
			klog.Warningf("failed to initialize p2p distributor: %s", err)
		}
		// Why not add one case of SFTPDistribution branch, instead of the default directly?
		// Since the value of distributor will be null when installing with clusterfile,
		// be sure to understand this logic if there is any future code that adds a case branch!
		// So why not set the default value when run.go executes Install? Because the installation there is more scattered, the code is not easy to control.
	default:
		distributor, err = imagedistributor.NewScpDistributor(imageMountInfo, app.infraDriver, nil, imagedistributor.DistributeOption{
			IgnoreCache:     ignoreCache,
			DistributionDir: distributionDir,
			IsApplication:   true,
		})
		if err != nil {
			return fmt.Errorf("failed to init sftp distributor: %v", err)
		}
	}
	// just Load image
	if mode == string(constants.LoadImage) {
		klog.Infof("it will load image to registry with apply mode [%s]", mode)
		return loadToRegistry(app.infraDriver, distributor)
	}

	masters := app.infraDriver.GetHostIPListByRole(consts.MASTER)
	// acquire cluster registry config
	regConfig := app.infraDriver.GetClusterRegistry()

	// 新建一个空目录用于存放组件，目录名称为组件的uid，并先分发该空目录到各个节点下的rootfs目录下
	if err = distributor.DistributorApplicationDir(string(app.appDriver.GetApplication().UID), clusterHosts); err != nil {
		return err
	}

	// distribute
	if err = distributor.Distribute(masters, filepath.Join(app.infraDriver.GetClusterRootfsPath(), string(app.appDriver.GetApplication().UID))); err != nil {
		return err
	}
	// Tips: golang fmt placeholder for slice should use `%v` or `%#v`
	klog.Infof("successed distribute rootfs with [%s] mode to [%v]", distribution, masters)

	if len(distributionDir) != 0 {
		return nil
	}

	// 加载插件
	plugins, err := plugin.LoadPluginsFromFile(filepath.Join(filepath.Join(app.infraDriver.GetClusterRootfsPath(), string(app.appDriver.GetApplication().UID)), constants.Plugins))
	if err != nil {
		return err
	}

	// 根据组件名称过滤出跟该组件相关的插件
	app.plugins, err = filterPlugins(app.appDriver.GetApplication().Name, plugins)
	if err != nil {
		return err
	}

	// if we use local registry service, load container image to registry
	if regConfig.LocalRegistry == nil {
		klog.Warning("the Local registry is nil")
		return nil
	}
	deployHosts := masters
	if !*regConfig.LocalRegistry.HA {
		deployHosts = []net.IP{masters[0]}
	}

	c, err := registry.NewConfigurator(deployHosts, containerruntime.RuntimeOpts{}, regConfig, app.infraDriver, distributor)
	if err != nil {
		return err
	}

	driver, err := c.GetDriver()
	if err != nil {
		return err
	}

	return driver.UploadContainerImages2Registry()
}

// NewPluginInstaller 初始化插件安装器
func NewPluginInstaller(imageEngine imageengine.ImageManager, cluster *ctv1beta1.Cluster) (*PluginInstaller, error) {
	infraDriver, err := infradriver.NewInfraDriver(cluster)
	if err != nil {
		return nil, err
	}

	return &PluginInstaller{
		infraDriver: infraDriver,
		imgEngine:   imageEngine,
	}, nil
}

// distribute 分发集群镜像文件
func (p *PluginInstaller) distribute(imageMountInfo []imagedistributor.ClusterImageMountInfo) error {
	for _, info := range imageMountInfo {
		eg, _ := errgroup.WithContext(context.Background())

		for _, ip := range info.Hosts {
			host := ip
			eg.Go(func() error {
				files, err := os.ReadDir(info.MountDir)
				if err != nil {
					return fmt.Errorf("failed to read dir %s: %s", info.MountDir, err)
				}

				for _, f := range files {
					// 跳过 registry 目录
					if f.IsDir() && f.Name() == constants.RegistryDirName {
						continue
					}

					// 拷贝集群镜像文件到目标节点
					if err = p.infraDriver.Copy(host, filepath.Join(info.MountDir, f.Name()), filepath.Join(p.infraDriver.GetClusterRootfsPath(), f.Name())); err != nil {
						return fmt.Errorf("failed to copy rootfs files: %v", err)
					}
				}
				return nil
			})
		}

		if err := eg.Wait(); err != nil {
			return err
		}
	}

	return nil
}

// install 从指定镜像加载插件并执行它们
func (p *PluginInstaller) install(imageName string) error {
	// 从镜像加载插件
	klog.Infof("Starting install plugin for image: %s", imageName)
	if err := p.distributeRootfsAndLoadPlugins(imageName); err != nil {
		return fmt.Errorf("failed to load plugins: %v", err)
	}

	// 执行插件
	return p.execPlugins()
}

// process 渲染每个挂载点中的插件模板
func (p *PluginInstaller) process(imageMountInfo []imagedistributor.ClusterImageMountInfo) error {
	// 渲染插件模板
	for _, info := range imageMountInfo {
		pluginsPath := filepath.Join(info.MountDir, constants.Plugins)
		if err := env.RenderTemplate(pluginsPath, p.infraDriver.GetClusterEnv()); err != nil {
			return fmt.Errorf("failed to render plugin templates: %v", err)
		}
	}
	return nil
}

// loadPluginsFromImage 挂载指定镜像，分发集群镜像文件，并加载插件
func (p *PluginInstaller) distributeRootfsAndLoadPlugins(imageName string) error {
	// 获取主机平台信息
	hostsPlatform, err := p.infraDriver.GetHostsPlatform(p.infraDriver.GetHostIPList())
	if err != nil {
		return fmt.Errorf("failed to get hosts platform: %v", err)
	}

	// 初始化镜像挂载器
	mounter, err := imagedistributor.NewImageMounter(p.imgEngine, hostsPlatform)
	if err != nil {
		return fmt.Errorf("failed to initialize image mounter: %v", err)
	}

	// 挂载集群镜像
	infos, err := mounter.Mount(imageName)
	if err != nil {
		return fmt.Errorf("failed to mount image %s: %v", imageName, err)
	}
	defer func() {
		if err := mounter.Umount(imageName, infos); err != nil {
			klog.Errorf("failed to unmount cluster image [%s]: %v", imageName, err)
		}
	}()

	// 分发集群镜像文件
	if err = p.distribute(infos); err != nil {
		return err
	}

	// 处理挂载信息
	if err := p.process(infos); err != nil {
		return err
	}

	// 从集群镜像加载插件
	p.plugins, err = loadPluginsFromImage(infos)
	if err != nil {
		return fmt.Errorf("failed to load plugins from image: %v", err)
	}

	return nil
}

// execPlugins 运行所有插件的前置和后置安装钩子
func (p *PluginInstaller) execPlugins() error {
	// 初始化插件处理器
	handler, err := plugin.NewHookOptions(p.plugins, p.infraDriver)
	if err != nil {
		return fmt.Errorf("failed to initialize plugin handler: %v", err)
	}

	// 执行前置安装钩子
	if err = handler.RunOnHosts(plugin.PreInstallApp, p.infraDriver.GetHostIPList()); err != nil {
		return fmt.Errorf("failed to run pre-install hooks: %v", err)
	}

	// 执行后置安装钩子
	if err = handler.RunOnHosts(plugin.PostInstallApp, p.infraDriver.GetHostIPList()); err != nil {
		return fmt.Errorf("failed to run post-install hooks: %v", err)
	}

	return nil
}

// NewApplicationInstaller 初始化app安装器
func NewApplicationInstaller(appSpec *appv1beta1.Application, ext etv1beta1.ImageExtension, imageEngine imageengine.ImageManager) (*AppInstaller, error) {
	driver, err := app.NewApplicationDriver(appSpec, ext)
	if err != nil {
		return nil, fmt.Errorf("failed to parse application:%v ", err)
	}
	// 安装应用组件时,会检查是否已存在集群信息,若不存在,则不允许单独安装组件
	m, _, err := pkgcf.GetActualClusterFile()
	if err != nil {
		return nil, fmt.Errorf("it seems the cluster isn't exist, alone components is not allowed to install: %v", err)
	}

	cluster := m.GetCluster()
	infraDriver, err := infradriver.NewInfraDriver(&cluster)
	if err != nil {
		return nil, err
	}

	return &AppInstaller{
		cfManager:   m,
		imgExt:      ext,
		appDriver:   driver,
		infraDriver: infraDriver,
		imgEngine:   imageEngine,
	}, nil
}

// IsClusterExist 判断集群是否已经存在
func IsClusterExist() bool {
	return pkgcf.IsExistClusterfile()
}

// ClusterInstaller 集群安装器
type ClusterInstaller struct {
	clusterfile pkgcf.Manager
	infraDriver infradriver.InfraDriver
	imgOperator imageengine.ImageManager
	imgSpec     *etv1beta1.ImageSpec
	fs          file.Interface
}

// clusterInstallOpts 安装集群所需参数
type clusterInstallOpts struct {
	// InstallMode 集群/组件 的安装方式
	InstallMode constants.InstallMode
	// RunMode 运行方式
	RunMode string
	// IgnoreCache 是否忽略缓存
	IgnoreCache bool
	// DistributeMode 镜像文件分发方式
	DistributeMode commands.DistributionMode
}

// clusterScaleUpOpts 拓展集群所需参数
type clusterScaleUpOpts struct {
	IgnoreCache bool
	Args        *commands.JoinFlags
}

// clusterScaleDownOpts 缩放集群所需参数
type clusterScaleDownOpts struct {
	Prune bool
	// 增加IgnoreError字段,用于控制是否忽略错误继续执行
	IgnoreError bool
}

// clusterDeleteOpts 删除集群所需参数
type clusterDeleteOpts struct {
	Prune bool
}

func (c ClusterInstaller) Install(imageName string, opts clusterInstallOpts) error {
	klog.Infof("start to install new cluster with install mode [%s], cluster image [%s]", opts.InstallMode, imageName)
	var (
		// 集群参数
		cluster               = c.clusterfile.GetCluster()
		hosts                 = c.infraDriver.GetHostIPList()
		pluginsFromFile       = c.clusterfile.GetPlugins()
		configs               = c.clusterfile.GetConfigs()
		kubeadmConfigFromFile = c.clusterfile.GetKubeadmConfig()
		// 应用参数
		cmds = c.infraDriver.GetClusterLaunchCmds()
		apps = c.infraDriver.GetClusterLaunchApps()
	)

	if c.infraDriver.GetClusterRegistry().ExternalRegistry != nil {
		if err := c.ConfigExternalRegistry(); err != nil {
			return fmt.Errorf("failed to config external registry: %v", err)
		}
	}

	klog.V(5).Infof("the info of cluster: %+v", cluster)
	// 获取控制面
	hostsPlatform, err := c.infraDriver.GetHostsPlatform(hosts)
	if err != nil {
		return fmt.Errorf("unable to get hosts platform: %v", err)
	}
	// 获取clusterfile 架构配置
	clusterEnv := c.infraDriver.GetClusterEnv()
	clusterOsPlatformValueStr := clusterEnv[constants.EnvClusterPlatform]
	// 解析架构配置
	osPlatforms, err := clusterfileutiles.ParseClusterOsPlatform(clusterOsPlatformValueStr)
	if err != nil {
		return err
	}
	// 注入集群的多架构配置
	clusterfileutiles.AppendOsPlatform(hostsPlatform, osPlatforms)
	// 初始化挂载器
	mounter, err := imagedistributor.NewImageMounter(c.imgOperator, hostsPlatform)
	if err != nil {
		return fmt.Errorf("unable to init image mounter: %v", err)
	}
	// 挂载cluster image
	infos, err := mounter.Mount(imageName)
	if err != nil {
		return fmt.Errorf("unable to mount image: %s", imageName)
	}
	defer func() {
		// umount cluster image
		if err = mounter.Umount(imageName, infos); err != nil {
			klog.Errorf("failed to unmount cluster image [%s]: %v", imageName, err)
		}
	}()

	driver, err := app.NewApplicationDriver(application.InitApplication(nil, cmds, apps, cluster.Spec.Env, imageName), c.imgSpec.ImageExtension)
	if err != nil {
		return fmt.Errorf("unable to new application driver: %v", err)
	}
	for _, info := range infos {
		if err = driver.FileProcess(info.MountDir); err != nil {
			return err
		}
	}

	var distributor imagedistributor.Distributor
	klog.V(5).Infof("distribute mode is: [%s]", opts.DistributeMode)
	switch opts.DistributeMode {
	case commands.P2PDistribution:
		distributor, err = imagedistributor.NewP2PDistributor(infos, c.infraDriver, configs, imagedistributor.DistributeOption{
			IgnoreCache: opts.IgnoreCache,
		})
		if err != nil {
			klog.Warningf("failed to init sftp distribution: %s", err)
		}
		// 这里没有将commands.SFTPDistribution作为单独的case分支,而是使用default分支也是同上面的原理一致
	default:
		distributor, err = imagedistributor.NewScpDistributor(infos, c.infraDriver, configs, imagedistributor.DistributeOption{
			IgnoreCache: opts.IgnoreCache,
		})
		if err != nil {
			return err
		}
	}
	klog.V(5).Infof("successed distribute cluster image with [%s] mode", opts.DistributeMode)
	if opts.RunMode == string(constants.LoadImage) {
		klog.Infof("it will load image to registry with apply mode [%s]", opts.RunMode)
		return clusterruntime.LoadToRegistry(c.infraDriver, distributor)
	}
	// 从cluster image 加载插件
	plugins, err := loadPluginsFromImage(infos)
	if err != nil {
		return err
	}
	if pluginsFromFile != nil {
		plugins = append(plugins, pluginsFromFile...)
	}
	// 初始化cluster-runtime配置
	runtimeConfig := &clusterruntime.RuntimeConfig{
		Distributor:            distributor,
		ContainerRuntimeConfig: cluster.Spec.ContainerRuntime,
		Plugins:                plugins,
	}
	if kubeadmConfigFromFile != nil {
		runtimeConfig.KubeadmConfig = *kubeadmConfigFromFile
	}
	// 初始化集群安装器
	installer, err := clusterruntime.NewInstaller(c.infraDriver, *runtimeConfig, clusterruntime.GetClusterInstallInfo(c.imgSpec.ImageExtension.Labels, cluster.Spec.ContainerRuntime))
	if err != nil {
		return fmt.Errorf("unable to int cluster installer: %v", err)
	}
	// 保存clusterfile
	if err = c.clusterfile.SaveAll(pkgcf.SaveOptions{PreInstallationCluster: true}); err != nil {
		return err
	}
	// 开始安装集群
	if err = installer.Install(); err != nil {
		return fmt.Errorf("unable to install cluster: %v", err)
	}
	handler, err := plugin.NewHookOptions(plugins, c.infraDriver)
	if err != nil {
		return err
	}
	driver.SetPluginHandler(handler)
	// 开始安装应用
	if err = driver.Install(c.infraDriver); err != nil {
		return fmt.Errorf("unabe to install Install: %v", err)
	}
	if err = driver.Save(); err != nil {
		return err
	}
	// 重置clusterfile
	c.resetCluster(ctv1beta1.NewSuccessInstallClusterCondition(), ctv1beta1.ClusterSuccess)

	path := clusterruntime.GetClusterConfPath(c.imgSpec.ImageExtension.Labels)
	// 保存Clusterfile
	if err = c.clusterfile.SaveAll(pkgcf.SaveOptions{SaveToCluster: true, ConfigPath: path}); err != nil {
		return fmt.Errorf("unable to save cluster config: %v", err)
	}
	klog.Infof("Success to install a new cluster with cluster image: %s", imageName)

	return nil
}

func (c ClusterInstaller) ConfigExternalRegistry() error {
	hosts := c.infraDriver.GetHostIPList()
	registryDomain, ok := c.infraDriver.GetClusterEnv()[constants.EnvRegistryDomain]
	if !ok {
		return fmt.Errorf("missing required environment variable: %s", constants.EnvRegistryDomain)
	}

	registryAddr := c.infraDriver.GetClusterRegistry().ExternalRegistry.RegistryVIP
	if len(registryAddr) == 0 {
		return errors.New("external registry address is empty")
	}

	err := c.infraDriver.SetClusterHostFile(hosts, registryAddr, registryDomain)
	if err != nil {
		return fmt.Errorf("failed to config cluster hosts file in cmd: %v", err)
	}
	return nil
}

// ScaleUp 拓展节点
func (c ClusterInstaller) ScaleUp(ms, ns []net.IP, options clusterScaleUpOpts) error {
	klog.Infof("start to scale up cluster")
	var (
		newHosts              = append(ms, ns...)
		clusterImageName      = c.infraDriver.GetClusterImageName()
		cluster               = c.clusterfile.GetCluster()
		pluginsFromFile       = c.clusterfile.GetPlugins()
		configsFromFile       = c.clusterfile.GetConfigs()
		kubeadmConfigFromFile = c.clusterfile.GetKubeadmConfig()
	)

	clusterHostsPlatform, err := c.infraDriver.GetHostsPlatform(newHosts)
	if err != nil {
		return err
	}

	imageMounter, err := imagedistributor.NewImageMounter(c.imgOperator, clusterHostsPlatform)
	if err != nil {
		return err
	}

	imageMountInfo, err := imageMounter.Mount(clusterImageName)
	if err != nil {
		return err
	}
	defer func() {
		if err = imageMounter.Umount(clusterImageName, imageMountInfo); err != nil {
			klog.Errorf("unable to umount cluster image: %v", err)
		}
	}()
	// 单独join节点的时候直接采用sftp的协议分发
	distributor, err := imagedistributor.NewScpDistributor(imageMountInfo, c.infraDriver, configsFromFile, imagedistributor.DistributeOption{
		IgnoreCache: options.IgnoreCache,
	})
	if err != nil {
		return fmt.Errorf("unable to new scp distributor: %v", err)
	}
	// 加载插件信息
	plugins, err := loadPluginsFromImage(imageMountInfo)
	if err != nil {
		return fmt.Errorf("unable to load plugins from cluster image: %v", err)
	}

	if pluginsFromFile != nil {
		plugins = append(plugins, pluginsFromFile...)
		klog.V(5).Infof("The plugins info is: [%+v]", plugins)
	}

	runtimeConfig := &clusterruntime.RuntimeConfig{
		Distributor:            distributor,
		Plugins:                plugins,
		ContainerRuntimeConfig: cluster.Spec.ContainerRuntime,
	}

	if kubeadmConfigFromFile != nil {
		runtimeConfig.KubeadmConfig = *kubeadmConfigFromFile
	}

	installer, err := clusterruntime.NewInstaller(c.infraDriver, *runtimeConfig, clusterruntime.GetClusterInstallInfo(c.imgSpec.ImageExtension.Labels, runtimeConfig.ContainerRuntimeConfig))
	if err != nil {
		return err
	}

	_, _, err = installer.ScaleUp(ms, ns)
	if err != nil {
		return err
	}

	pkgcf.ConstructClusterWithSpecifiedHosts(&cluster, newHosts)
	pluginInstaller, err := NewPluginInstaller(c.imgOperator, &cluster)
	if err != nil {
		return fmt.Errorf("failed to create plugin installer : %v", err)
	}

	// 为新节点安装插件
	filteredApps := filterApplicationsWithPlugins(c.clusterfile.GetApplication())
	filteredApps, err = sortApplications(filteredApps)
	if err != nil {
		return fmt.Errorf("failed to sort applications : %v", err)
	}

	klog.V(5).Infof("The filtered applications with plugins are: %+v", filteredApps)
	for _, app := range filteredApps {
		if err := pluginInstaller.install(app.Name); err != nil {
			return fmt.Errorf("failed to install plugin for %s on new nodes: %v", app.Name, err)
		}
	}

	c.resetCluster(ctv1beta1.NewSuccessScaleUpClusterCondition(), ctv1beta1.ClusterSuccess)

	confPath := clusterruntime.GetClusterConfPath(c.imgSpec.ImageExtension.Labels)
	if err = c.clusterfile.SaveAll(
		pkgcf.SaveOptions{
			SaveToCluster: true,
			ConfigPath:    clusterruntime.GetClusterConfPath(c.imgSpec.ImageExtension.Labels),
			ClusterInfo:   pkgcf.ClusterInfo{Masters: ms, Workers: ns, ScaleFlags: options.Args, OptionFunc: []pkgcf.ClusterOptionFunc{pkgcf.SaveNodeToCluster}}},
	); err != nil {
		return fmt.Errorf("unable save all cluster file to %s: %v", confPath, err)
	}
	klog.Infof("succeeded in scaling up cluster")

	return nil
}

// filterApplicationsWithPlugins 过滤有插件的应用
func filterApplicationsWithPlugins(applications []*appv1beta1.Application) []*appv1beta1.Application {
	var filteredApps []*appv1beta1.Application
	for _, app := range applications {
		if len(app.Spec.Plugins) > 0 {
			filteredApps = append(filteredApps, app)
		}
	}
	return filteredApps
}

func sortApplications(apps []*appv1beta1.Application) ([]*appv1beta1.Application, error) {
	// 创建 UID 到 Application 的映射
	appMap := make(map[types.UID]*appv1beta1.Application)
	// 创建 UID 到依赖该应用的应用程序列表的映射
	graph := make(map[types.UID][]types.UID)
	// 记录每个应用的入度
	inDegree := make(map[types.UID]int)

	// 初始化数据结构
	for _, app := range apps {
		uid := app.ObjectMeta.UID
		appMap[uid] = app
		inDegree[uid] = 0
	}

	// 构建图和入度计数
	for _, app := range apps {
		uid := app.ObjectMeta.UID
		for _, depUID := range app.Spec.PreApplications {
			if _, exists := appMap[types.UID(depUID)]; exists {
				graph[types.UID(depUID)] = append(graph[types.UID(depUID)], uid)
				inDegree[uid]++
			}
		}
	}

	// 拓扑排序 - Kahn 算法
	var queue []types.UID
	for uid, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, uid)
		}
	}

	var result []*appv1beta1.Application
	for len(queue) > 0 {
		currentUID := queue[0]
		queue = queue[1:]

		result = append(result, appMap[currentUID])

		for _, neighbor := range graph[currentUID] {
			inDegree[neighbor]--
			if inDegree[neighbor] == 0 {
				queue = append(queue, neighbor)
			}
		}
	}

	// 检查是否有环
	if len(result) != len(apps) {
		return nil, fmt.Errorf("cycle detected in application dependencies")
	}

	return result, nil
}

// execScaleDown 删除节点
func (c ClusterInstaller) execScaleDown(ms, ns []net.IP) error {
	var (
		newHosts              = append(ms, ns...)
		clusterImageName      = c.infraDriver.GetClusterImageName()
		cluster               = c.clusterfile.GetCluster()
		pluginsFromFile       = c.clusterfile.GetPlugins()
		kubeadmConfigFromFile = c.clusterfile.GetKubeadmConfig()
		runtimeConfig         = &clusterruntime.RuntimeConfig{ContainerRuntimeConfig: cluster.Spec.ContainerRuntime}
	)

	// 获取主机平台信息
	clusterHostsPlatform, err := c.infraDriver.GetHostsPlatform(newHosts)
	if err != nil {
		return fmt.Errorf("failed to get hosts platform: %v", err)
	}

	// 初始化镜像挂载器
	imageMounter, err := imagedistributor.NewImageMounter(c.imgOperator, clusterHostsPlatform)
	if err != nil {
		return fmt.Errorf("failed to create image mounter: %v", err)
	}

	// 挂载集群镜像
	imageMountInfo, err := imageMounter.Mount(clusterImageName)
	if err != nil {
		return fmt.Errorf("failed to mount cluster image: %v", err)
	}

	defer func() {
		if unmountErr := imageMounter.Umount(clusterImageName, imageMountInfo); unmountErr != nil {
			klog.ErrorS(unmountErr, "Failed to unmount cluster image",
				"imageName", clusterImageName)
		}
	}()

	// 创建分发器
	distributor, err := imagedistributor.NewScpDistributor(imageMountInfo, c.infraDriver, nil, imagedistributor.DistributeOption{})
	if err != nil {
		return fmt.Errorf("failed to create scp distributor: %v", err)
	}

	runtimeConfig.Distributor = distributor

	// 加载插件
	plugins, err := loadPluginsFromImage(imageMountInfo)
	if err != nil {
		return fmt.Errorf("failed to load plugins from image: %v", err)
	}

	if len(pluginsFromFile) > 0 {
		plugins = append(plugins, pluginsFromFile...)
	}
	runtimeConfig.Plugins = plugins

	if kubeadmConfigFromFile != nil {
		runtimeConfig.KubeadmConfig = *kubeadmConfigFromFile
	}

	// 创建安装器
	installer, err := clusterruntime.NewInstaller(c.infraDriver, *runtimeConfig,
		clusterruntime.GetClusterInstallInfo(c.imgSpec.ImageExtension.Labels, cluster.Spec.ContainerRuntime))
	if err != nil {
		return fmt.Errorf("failed to create cluster installer: %v", err)
	}

	// 执行缩容
	_, _, err = installer.ScaleDown(ms, ns)
	if err != nil {
		return fmt.Errorf("failed to scale down cluster: %v", err)
	}

	return nil
}

// filterNodesFromK8s 从 k8s 集群中过滤出 master 和 worker 节点
func (c ClusterInstaller) filterNodesFromK8s(ms, ns []net.IP) ([]net.IP, []net.IP, error) {
	cli, err := k8s.NewK8sClient()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create kubernetes client: %v", err)
	}

	nodes, err := cli.ListNodes()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list k8s nodes: %v", err)
	}

	// 分别存储 master 和 worker 节点的 IP
	masterIPs := make(map[string]struct{})
	workerIPs := make(map[string]struct{})

	for _, node := range nodes.Items {
		var nodeIP string
		// 获取节点 IP
		for _, addr := range node.Status.Addresses {
			if addr.Type == corev1.NodeInternalIP {
				nodeIP = addr.Address
				break
			}
		}
		if nodeIP == "" {
			continue
		}

		// 根据节点标签判断角色
		if _, isMaster := node.Labels[constants.NodeRoleOldControlPlane]; isMaster {
			masterIPs[nodeIP] = struct{}{}
		} else if _, isControlPlane := node.Labels[constants.NodeRoleControlPlane]; isControlPlane {
			masterIPs[nodeIP] = struct{}{}
		} else {
			workerIPs[nodeIP] = struct{}{}
		}
	}

	// 过滤 master 节点
	var filteredMs []net.IP
	for _, ip := range ms {
		if _, exists := masterIPs[ip.String()]; exists {
			filteredMs = append(filteredMs, ip)
		}
	}

	// 过滤 worker 节点
	var filteredNs []net.IP
	for _, ip := range ns {
		if _, exists := workerIPs[ip.String()]; exists {
			filteredNs = append(filteredNs, ip)
		}
	}

	return filteredMs, filteredNs, nil
}

func (c ClusterInstaller) ScaleDown(ms, ns []net.IP, options clusterScaleDownOpts) error {
	klog.Info("Start to scale down cluster")
	var (
		cluster      = c.clusterfile.GetCluster()
		all          = append(ms, ns...)
		scaleDownErr error
	)

	// 根据 k8s 集群实际状态过滤节点
	filteredMs, filteredNs, err := c.filterNodesFromK8s(ms, ns)
	if err != nil {
		return fmt.Errorf("failed to filter nodes from k8s: %v", err)
	}

	filteredNodes := append(filteredMs, filteredNs...)

	klog.Infof("Scale down expected nodes:[%+v],scale down filtered nodes=%v", all, filteredNodes)
	if len(filteredMs) != 0 || len(filteredNs) != 0 {
		if scaleDownErr = c.execScaleDown(filteredMs, filteredNs); scaleDownErr != nil {
			if !options.IgnoreError {
				return fmt.Errorf("scale down operation failed and was aborted (IgnoreError=false): %v", scaleDownErr)
			}
			klog.Warningf("Scale down cluster failed: [%v],but continuing due to IgnoreError option", scaleDownErr)
		}
	}

	// 删除节点,防止execScaleDown未将节点移出集群
	if scaleDownErr != nil && options.IgnoreError {
		klog.Warning("Attempting to delete nodes from cluster due to scale down error and IgnoreError option")
		if err = c.deleteNodesFromCluster(filteredNodes); err != nil {
			return fmt.Errorf("failed to delete nodes from cluster, %v", err)
		}
	}

	// 更新 nodeInfo
	nodesInfoList, err := clusterruntime.GetNodesInfoList(c.infraDriver, all, nodeconfig.DeleteNode)
	if err != nil {
		return fmt.Errorf("failed to get nodes info list: %v", err)
	}

	if err = nodeconfig.ManageNodeConfigmapFuncMap[nodeconfig.DeleteNode](nodesInfoList); err != nil {
		return fmt.Errorf("failed to manage node configmap: %v", err)
	}

	// 更新集群状态和保存配置
	cluster.Status.Phase = ctv1beta1.ClusterSuccess
	cluster.Status.Conditions = append(cluster.Status.Conditions, ctv1beta1.NewSuccessScaleDownClusterCondition())
	c.clusterfile.SetCluster(cluster)

	confPath := clusterruntime.GetClusterConfPath(c.imgSpec.ImageExtension.Labels)
	return c.clusterfile.SaveAll(
		pkgcf.SaveOptions{
			SaveToCluster: true,
			ConfigPath:    confPath,
			ClusterInfo:   pkgcf.ClusterInfo{Masters: ms, Workers: ns, OptionFunc: []pkgcf.ClusterOptionFunc{pkgcf.RemoveNodeFromCluster}}},
	)
}

// deleteNodesFromCluster 删除集群中的节点,如果节点不存在则忽略
func (c ClusterInstaller) deleteNodesFromCluster(nodes []net.IP) error {
	cli, err := k8s.NewK8sClient()
	if err != nil {
		return fmt.Errorf("failed to create kubernetes client: %v", err)
	}

	// 获取所有节点列表
	nodeList, err := cli.ListNodes()
	if err != nil {
		return fmt.Errorf("failed to list nodes: %v", err)
	}

	// 遍历需要删除的节点IP
	for _, nodeIP := range nodes {
		// 在节点列表中查找对应的节点名称
		if name := c.nodeName(nodeList.Items, nodeIP.String()); len(name) != 0 {
			// 删除节点
			if err = cli.DeleteNode(name); err != nil {
				return fmt.Errorf("failed to delete node %s: %v", name, err)
			}
			klog.Infof("Successfully deleted node %s from cluster", name)
		}
	}

	return nil
}

func (c ClusterInstaller) nodeName(nodes []corev1.Node, ip string) string {
	for _, node := range nodes {
		for _, addr := range node.Status.Addresses {
			if addr.Type == corev1.NodeInternalIP && addr.Address == ip {
				return node.Name
			}
		}
	}
	return ""
}

func (c ClusterInstaller) Delete(options clusterDeleteOpts) error {
	klog.Infof("Start to delete cluster")
	var (
		clusterImageName      = c.infraDriver.GetClusterImageName()
		cluster               = c.clusterfile.GetCluster()
		pluginsFromFile       = c.clusterfile.GetPlugins()
		kubeadmConfigFromFile = c.clusterfile.GetKubeadmConfig()
	)

	clusterHostsPlatform, err := c.infraDriver.GetHostsPlatform(c.infraDriver.GetHostIPList())
	if err != nil {
		return err
	}

	imageMounter, err := imagedistributor.NewImageMounter(c.imgOperator, clusterHostsPlatform)
	if err != nil {
		return err
	}

	imageMountInfo, err := imageMounter.Mount(clusterImageName)
	if err != nil {
		return err
	}
	defer func() {
		if err = imageMounter.Umount(clusterImageName, imageMountInfo); err != nil {
			klog.Errorf("failed to umount cluster image [%s]: %v", clusterImageName, err)
		}
	}()

	distributor, err := imagedistributor.NewScpDistributor(imageMountInfo, c.infraDriver, nil, imagedistributor.DistributeOption{
		Prune: options.Prune,
	})
	if err != nil {
		return err
	}
	plugins, err := loadPluginsFromImage(imageMountInfo)
	if err != nil {
		return err
	}
	if pluginsFromFile != nil {
		plugins = append(plugins, pluginsFromFile...)
	}
	runtimeConfig := &clusterruntime.RuntimeConfig{
		Distributor:            distributor,
		Plugins:                plugins,
		ContainerRuntimeConfig: cluster.Spec.ContainerRuntime,
	}
	if kubeadmConfigFromFile != nil {
		runtimeConfig.KubeadmConfig = *kubeadmConfigFromFile
	}

	installer, err := clusterruntime.NewInstaller(c.infraDriver, *runtimeConfig,
		clusterruntime.GetClusterInstallInfo(c.imgSpec.ImageExtension.Labels, cluster.Spec.ContainerRuntime))
	if err != nil {
		return err
	}

	if err = installer.UnInstall(); err != nil {
		return err
	}
	// 删除本地文件，包括clusterfile, 还有kubepilot工作目录下的application.json文件
	if err = os.Remove(file.GetDefaultClusterfile()); err != nil {
		return err
	}
	// 删除application.json文件
	if err = os.Remove(file.GetDefaultApplicationFile()); err != nil {
		return err
	}
	// 删除kubeconfig文件
	if err = c.fs.RemoveAll(file.DefaultKubeConfigDir()); err != nil {
		return err
	}

	return nil
}

func NewClusterInstaller(cf pkgcf.Manager, imageEngine imageengine.ImageManager, imageSpec *etv1beta1.ImageSpec) (*ClusterInstaller, error) {
	cluster := cf.GetCluster()

	// merge image extension with cluster
	mergedWithExt := pkgcf.MergeClusterWithImageExtension(&cluster, imageSpec.ImageExtension)

	cf.SetCluster(*mergedWithExt)
	infraDriver, err := infradriver.NewInfraDriver(mergedWithExt)
	if err != nil {
		return nil, err
	}

	return &ClusterInstaller{
		imgOperator: imageEngine,
		imgSpec:     imageSpec,
		infraDriver: infraDriver,
		clusterfile: cf,
		fs:          file.NewFileSystem(),
	}, nil
}

// resetCluster 设置clusterfile status
func (c ClusterInstaller) resetCluster(condition ctv1beta1.ClusterCondition, phase ctv1beta1.ClusterPhase) {
	klog.V(5).Info("update clusterfile status")
	cf := c.clusterfile.GetCluster()
	cf.Status.Phase = phase
	cf.Status.Conditions = append(cf.Status.Conditions, condition)
	c.clusterfile.SetCluster(cf)
}

// loadPluginsFromImage 从集群镜像加载出相关的插件
func loadPluginsFromImage(imageMountInfo []imagedistributor.ClusterImageMountInfo) (plugins []pgv1beta1.Plugin, err error) {
	for _, info := range imageMountInfo {
		plugins, err = plugin.LoadPluginsFromFile(filepath.Join(info.MountDir, constants.Plugins))
		if err != nil {
			return
		}
	}

	return plugins, nil
}

// loadToRegistry 将容器镜像加载到本地registry
func loadToRegistry(infraDriver infradriver.InfraDriver, distributor imagedistributor.Distributor) error {
	regConfig := infraDriver.GetClusterRegistry()
	// todo only support load image to local registry at present
	if regConfig.LocalRegistry == nil {
		klog.Warning("The cluster uses a remote repository, so there is no load images to registry")
		return nil
	}

	deployHosts := infraDriver.GetHostIPListByRole(consts.MASTER)
	if len(deployHosts) < 1 {
		return errors.New("Local registry host is empty")
	}
	master0 := deployHosts[0]

	klog.Infof("start to apply with mode (%s)", constants.LoadImage)
	if !*regConfig.LocalRegistry.HA {
		deployHosts = []net.IP{master0}
	}

	if err := distributor.DistributeRegistry(deployHosts, infraDriver.GetRegistryDataDir()); err != nil {
		return err
	}
	klog.Infof("Load cluster image success")

	return nil
}

func filterPlugins(imageName string, plugins []pgv1beta1.Plugin) (pluginList []pgv1beta1.Plugin, err error) {
	name, _, err := imageutils.ParseComponentFromImageName(imageName)
	if err != nil {
		return nil, err
	}
	for _, p := range plugins {
		if strings.Contains(p.GetName(), name) {
			pluginList = append(pluginList, p)
		}
	}
	return pluginList, nil
}
