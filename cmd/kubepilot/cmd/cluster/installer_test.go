package cluster

import (
	"net"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/client/k8s"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
)

func TestClusterInstaller_filterNodesFromK8s(t *testing.T) {
	// 准备测试数据
	testNodes := &corev1.NodeList{
		Items: []corev1.Node{
			{
				ObjectMeta: metav1.ObjectMeta{
					Name: "master-1",
					Labels: map[string]string{
						constants.NodeRoleControlPlane: "",
					},
				},
				Status: corev1.NodeStatus{
					Addresses: []corev1.NodeAddress{
						{
							Type:    corev1.NodeInternalIP,
							Address: "***********",
						},
					},
				},
			},
			{
				ObjectMeta: metav1.ObjectMeta{
					Name: "worker-1",
				},
				Status: corev1.NodeStatus{
					Addresses: []corev1.NodeAddress{
						{
							Type:    corev1.NodeInternalIP,
							Address: "***********",
						},
					},
				},
			},
		},
	}

	tests := []struct {
		name          string
		nodes         *corev1.NodeList
		inputMasters  []net.IP
		inputNodes    []net.IP
		expectMasters []string
		expectNodes   []string
		expectError   bool
	}{
		{
			name:  "normal case",
			nodes: testNodes,
			inputMasters: []net.IP{
				net.ParseIP("***********"),
				net.ParseIP("***********"),
			},
			inputNodes: []net.IP{
				net.ParseIP("***********"),
				net.ParseIP("***********"),
			},
			expectMasters: []string{"***********"},
			expectNodes:   []string{"***********"},
			expectError:   false,
		},
		{
			name:          "empty input",
			nodes:         testNodes,
			inputMasters:  []net.IP{},
			inputNodes:    []net.IP{},
			expectMasters: []string{},
			expectNodes:   []string{},
			expectError:   false,
		},
		{
			name: "no nodes in cluster",
			nodes: &corev1.NodeList{
				Items: []corev1.Node{},
			},
			inputMasters:  []net.IP{net.ParseIP("***********")},
			inputNodes:    []net.IP{net.ParseIP("***********")},
			expectMasters: []string{},
			expectNodes:   []string{},
			expectError:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			installer := &ClusterInstaller{}

			patches := gomonkey.NewPatches()
			defer patches.Reset()

			patches.ApplyFunc(k8s.NewK8sClient, func() (*k8s.Client, error) {
				return &k8s.Client{}, nil
			})

			patches.ApplyMethodFunc((*k8s.Client)(nil), "ListNodes", func() (*corev1.NodeList, error) {
				return tt.nodes, nil
			})

			filteredMs, filteredNs, err := installer.filterNodesFromK8s(tt.inputMasters, tt.inputNodes)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Len(t, filteredMs, len(tt.expectMasters))
			assert.Len(t, filteredNs, len(tt.expectNodes))

			if len(tt.expectMasters) > 0 {
				for i, ip := range filteredMs {
					assert.Equal(t, tt.expectMasters[i], ip.String())
				}
			}

			if len(tt.expectNodes) > 0 {
				for i, ip := range filteredNs {
					assert.Equal(t, tt.expectNodes[i], ip.String())
				}
			}
		})
	}
}
