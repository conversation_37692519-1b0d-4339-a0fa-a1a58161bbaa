package cluster

import (
	"errors"
	"fmt"
	"io"
	"os"

	"github.com/spf13/cobra"
	pkgcluster "gitlab.bingosoft.net/bingokube/kubepilot/pkg/clusterfile"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"sigs.k8s.io/yaml"
)

var (
	exampleGen = `
# 使用默认配置生成最基本的单master节点的Clusterfile文件:
  kubepilot gen kubernetes:v1.0 -m 10.16.203.183 -u $username -p $pwd

# 生成包含多个业务组件的集群的Clusterfile文件(注意: 先输入的业务组件先安装)，并指定多个master和worker节点:
  kubepilot gen kubernetes:v1.0 -c helm:v3.8.0, kube-nvs:v1.5.0, kubeverse:v1.4.3 -m 1************,*************,*************
-n *************,************* -u $username -p $pwd

# 指定ssh服务端口(默认是22)
  所有的节点使用相同的端口:
  kubepilot gen kubernetes:v1.0 -m 1************,*************,*************
-n *************,************* --port 23 -u $username -p $pwd
  
  所有的节点使用不同的端口:
  kubepilot gen kubernetes:v1.0 -m 1************:23,*************:24,*************:25
-n *************:26,*************:27 -u $username -p $pwd
`
	longGenDescription = `
  该命令主要是基于 [用户输入的参数+默认参数] 生成Clusterfile文件, kubepilot通过该文件可以创建一个集群(command: kubepilot run -f Clusterfile)
`
)

// newGenCmd 生成clusterfile
func newGenCmd() *cobra.Command {
	f := &commands.ClusterfileGenFlags{}
	cmd := &cobra.Command{
		Use:     "gen",
		Short:   "生成Clusterfile文件",
		Long:    longGenDescription,
		Example: exampleGen,
		Args:    cobra.MinimumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			data, err := runGen(cmd, args[0], f)
			if err != nil {
				return err
			}
			var writer io.WriteCloser
			switch f.Output {
			case "", "stdout":
				writer = os.Stdout
			default:
				if writer, err = os.Create(f.Output); err != nil {
					return err
				}
			}
			defer writer.Close()
			_, err = fmt.Fprintln(writer, string(data))

			return err
		},
	}
	fs := cmd.Flags()
	f.InitGenFlags(fs)

	return cmd
}

func runGen(cmd *cobra.Command, img string, fs *commands.ClusterfileGenFlags) ([]byte, error) {
	// 第一个image类型必须是k8s的集群镜像
	if len(img) == 0 {
		return nil, errors.New("k8s base image is empty")
	}
	// 判断image类型是否为k8s集群镜像
	info, err := pkgcluster.GetImageInfo(img)
	if err != nil {
		return nil, err
	}
	if !info.IsRootfs() {
		return nil, fmt.Errorf("the first image %s is not a rootfs type image", img)
	}
	// 未填写master的IP地址，则以当前服务器IP为master
	if len(fs.Masters) == 0 {
		defaultIP, err := unet.GetLocalDefaultIP()
		if err != nil {
			return nil, err
		}
		fs.Masters = defaultIP
	}
	// 初始化cluster
	cluster, err := pkgcluster.BuildClusterStruct(img, fs.Convert())
	if err != nil {
		return nil, err
	}

	data, err := yaml.Marshal(cluster)
	if err != nil {
		return nil, err
	}

	return data, nil
}
