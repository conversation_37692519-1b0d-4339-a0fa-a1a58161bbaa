package cluster

import (
	"testing"

	"github.com/stretchr/testify/assert"
	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
)

func TestAddSkipPhasesToEnv(t *testing.T) {
	tests := []struct {
		name        string
		existingEnv []string
		phases      []string
		expectedEnv []string
	}{
		{
			name:        "添加新的跳过阶段",
			existingEnv: []string{"existingVar=value"},
			phases:      []string{"container-runtime"},
			expectedEnv: []string{
				"existingVar=value",
				"skipContainerRuntime='true'",
			},
		},
		{
			name: "更新已存在的跳过阶段",
			existingEnv: []string{
				"existingVar=value",
				"skipContainerRuntime='false'",
			},
			phases: []string{"container-runtime"},
			expectedEnv: []string{
				"existingVar=value",
				"skipContainerRuntime='true'",
			},
		},
		{
			name:        "添加多个跳过阶段",
			existingEnv: []string{"existingVar=value"},
			phases:      []string{"container-runtime", "other-phase"},
			expectedEnv: []string{
				"existingVar=value",
				"skipContainerRuntime='true'",
				"skipOtherPhase='true'",
			},
		},
		{
			name:        "空phases不改变环境变量",
			existingEnv: []string{"existingVar=value"},
			phases:      []string{},
			expectedEnv: []string{"existingVar=value"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cluster := &ctv1beta1.Cluster{
				Spec: ctv1beta1.ClusterSpec{
					Env: tt.existingEnv,
				},
			}

			addSkipPhasesToEnv(cluster, tt.phases)

			assert.ElementsMatch(t, tt.expectedEnv, cluster.Spec.Env,
				"环境变量不匹配")
		})
	}
}

func TestKebabToCamelCase(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{
			input:    "container-runtime",
			expected: "ContainerRuntime",
		},
		{
			input:    "other-phase",
			expected: "OtherPhase",
		},
		{
			input:    "single",
			expected: "Single",
		},
		{
			input:    "multiple-word-test",
			expected: "MultipleWordTest",
		},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := kebabCaseToPascalCase(tt.input)
			assert.Equal(t, tt.expected, result,
				"kebab to camel case 转换结果不匹配")
		})
	}
}
