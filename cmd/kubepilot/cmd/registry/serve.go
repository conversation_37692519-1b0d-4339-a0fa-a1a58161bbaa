package registry

import (
	"fmt"
	"os"

	"github.com/distribution/distribution/v3/configuration"
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/registry"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry/handler"
)

var (
	exampleRegistryServeCmd = `
# 启动一个临时的仓库
  kubepilot registry serve ./tmp/registry --pid-file ./registry.pid --port 35000
`
)

func newRegistryServeCommand() *cobra.Command {
	f := registry.RegistryServeOpts{}
	cmd := &cobra.Command{
		Use:     "serve",
		Example: exampleRegistryServeCmd,
		Short:   "启动一个 Docker Registry 镜像服务",
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			config, err := handler.NewConfig(args[0], f.Port)
			if err != nil {
				return err
			}

			if f.PidFile != "" {
				if err = os.WriteFile(f.PidFile, []byte(fmt.Sprintf("%d", os.Getpid())), 0600); err != nil {
					return fmt.Errorf("failed to write pid file: %w", err)
				}
			}
			config.Log.Level = configuration.Loglevel(f.LogLevel)
			config.Log.AccessLog.Disabled = f.DisableLogging
			return handler.Run(cmd.Context(), config)
		},
	}
	fs := cmd.Flags()
	f.InitRegistryServeFlags(fs)
	return cmd
}
