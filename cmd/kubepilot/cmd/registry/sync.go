package registry

import (
	"context"
	"time"

	imagecopy "github.com/containers/image/v5/copy"
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/registry"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry/sync"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/http"
	"golang.org/x/sync/errgroup"
)

var (
	exampleRegistrySyncCmd = `
  # 从 registry1 同步到 registry2
  kubepilot registry sync 10.16.203.184:5000 10.16.203.128:5000
`
)

func NewRegistrySyncCommand() *cobra.Command {
	opts := registry.RegistrySyncOpts{}
	cmd := &cobra.Command{
		Use:     "sync SOURCE_REGISTRY DST_REGISTRY",
		Short:   "从一个镜像仓库同步所有镜像到另一个镜像仓库",
		Args:    cobra.ExactArgs(2),
		Example: exampleRegistrySyncCmd,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runSync(args[0], args[1], opts)
		},
	}
	fs := cmd.Flags()
	fs.SetInterspersed(false)
	opts.RegistrySyncFlags(fs)
	return cmd
}

func runSync(source, dst string, opts registry.RegistrySyncOpts) error {
	imageListSelection := imagecopy.CopyAllImages

	// 解析 source 和 dst 的地址
	srcRegistry := sync.ParseRegistryAddress(source)
	dstRegistry := sync.ParseRegistryAddress(dst)

	// 创建上下文并设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// 检查 source 和 dst 是否可达
	if err := checkEndpointsAlive(ctx, srcRegistry, dstRegistry); err != nil {
		return err
	}

	syncOpts := &sync.Options{
		SystemContext: opts.NewSystemContext(),
		Source:        srcRegistry,
		Target:        dst,
		SelectionOptions: []imagecopy.ImageListSelection{
			imageListSelection,
		},
		OmitError: true,
	}

	return sync.Sync(context.Background(), syncOpts)
}

func checkEndpointsAlive(ctx context.Context, endpoints ...string) error {
	eg, ctx := errgroup.WithContext(ctx)
	for _, endpoint := range endpoints {
		ep := endpoint // 避免闭包问题
		eg.Go(func() error {
			return http.WaitUntilEndpointAlive(ctx, ep)
		})
	}
	return eg.Wait()
}
