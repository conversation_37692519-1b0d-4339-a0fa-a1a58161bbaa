package registry

import (
	"context"
	"errors"
	"fmt"

	"github.com/docker/docker/api/types"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/registry"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"k8s.io/klog/v2"

	v1 "github.com/opencontainers/image-spec/specs-go/v1"
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry/save"
)

var (
	exampleRegistrySaveCmd = `
# 根据镜像清单文件保存镜像到本地目录
  kubepilot registry save --local-dir=./registry --file=./imageList

# 将指定镜像保存到本地目录
  kubepilot registry save --local-dir=./registry --images=registry.bingosoft.net/bingokube/opbox:v1.1
`
)

func NewRegistryImageSaveCmd() *cobra.Command {
	f := registry.RegistrySaveOpts{}
	var auth map[string]types.AuthConfig
	cmd := &cobra.Command{
		Use:     "save",
		Short:   "将镜像保存到本地目录",
		Example: exampleRegistrySaveCmd,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(f.Images) > 0 {
				imageSaver := save.NewImageSaver(f.MaxPullProcs, auth)
				outImages, err := imageSaver.SaveImages(context.Background(), f.Images, f.LocalDir, v1.Platform{OS: "linux", Architecture: f.Arch})
				if err != nil {
					return err
				}
				klog.Infof("images pulled: %+v", outImages)
			}
			return nil
		},
		PreRunE: func(cmd *cobra.Command, args []string) error {
			if len(f.Images) == 0 && len(f.File) == 0 {
				return errors.New("'--images' or '--file' cannot be empty at the same time")
			}

			if len(f.Images) == 0 && len(f.File) > 0 {
				lines, err := file.GrepNonCommentLines(f.File)
				if err != nil {
					return fmt.Errorf("failed to read file %s: %v", f.File, err)
				}
				f.Images = lines
			}

			return nil
		},
	}
	fs := cmd.Flags()
	fs.SetInterspersed(false)
	f.InitRegistrySaveFlags(fs)
	return cmd
}
