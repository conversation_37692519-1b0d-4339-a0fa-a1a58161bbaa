package image

import (
	"encoding/json"
	"fmt"

	"github.com/containers/common/pkg/auth"
	"github.com/opencontainers/go-digest"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"k8s.io/klog/v2"
)

var (
	longManifestDescription = `
  manifest命令用于对集群镜像manifest进行管理，包括创建、删除、推送等
`

	exampleManifest = `
# 创建 manifest:
  kubepilot manifest create clusterImg_name:tag

# 添加一个amd架构镜像到 manifest:
  kubepilot manifest add clusterImg_name:tag clusterImg_name:tag-amd64 --arch amd64

# 查看 manifest 详细信息:
  kubepilot manifest inspect clusterImg_name:tag

# 将 manifest 及其镜像推送到仓库:
  kubepilot manifest push --all clusterImg_name:tag

# 删除 manifest:
  kubepilot manifest delete clusterImg_name:tag

# 将 manifest 中的一个镜像移除
  kubepilot manifest remove clusterImg_name:tag
`
)

// newManifestCmd 制作集群镜像manifest
func newManifestCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "manifest",
		Short:   "manifest 管理",
		Long:    longManifestDescription,
		Example: exampleManifest,
	}

	cmd.AddCommand(manifestCreateCommand(), manifestAddCommand(),
		manifestPushCommand(), manifestInspectCommand(), manifestDeleteCommand(),
		manifestRemoveCommand())

	return cmd
}

var (
	longManifestCreateDescription = `
  该命令用于用于创建集群镜像 manifest
`

	exampleManifestCreate = `
# 创建 manifest:
  kubepilot manifest create clusterImg_name:tag
`
)

// manifestCreateCommand 创建manifest
func manifestCreateCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "create",
		Short:   "创建一个 manifest",
		Example: exampleManifestCreate,
		Args:    cobra.MinimumNArgs(1),
		Long:    longManifestCreateDescription,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) == 0 {
				return errors.New("at least a name must be specified for the manifest list")
			}
			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}

			id, err := e.CreateManifest(args[0], &image.ManifestCreateOpts{})
			if err != nil {
				return err
			}

			klog.Infof("successfully create manifest %s with ID %s", args[0], id)
			return nil
		},
	}

	return cmd
}

var (
	longManifestAddDescription = `
  该命令用于用于将一个或多个镜像添加到 manifest
`

	exampleManifestAdd = `
# 添加一个amd架构镜像到 manifest:
  kubepilot manifest add clusterImg_name:tag clusterImg_name:tag-amd64 --arch amd64

`
)

// manifestAddCommand 添加一个或多个镜像到 manifest
func manifestAddCommand() *cobra.Command {
	mf := &image.ManifestAddOpts{}
	cmd := &cobra.Command{
		Use:     "add",
		Short:   "添加一个或多个镜像到 manifest",
		Example: exampleManifestAdd,
		Args:    cobra.MinimumNArgs(1),
		Long:    longManifestAddDescription,
		RunE: func(cmd *cobra.Command, args []string) error {
			var (
				manifestName = mf.TargetName
				imagesToAdd  = args
			)

			// if not set `-t` flag , assume the first one is the manifestName,others is the images need to be added to.
			if manifestName == "" {
				manifestName = args[0]
				imagesToAdd = args[1:]
			}

			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}

			return e.AddToManifest(manifestName, imagesToAdd, mf)
		},
	}

	flags := cmd.Flags()
	mf.ManifestAddFlags(flags)

	return cmd
}

var (
	longManifestRemoveDescription = `
  该命令用于用于将一个或多个镜像从 manifest 中移除
`

	exampleManifestRemove = `
# 将 manifest 中的一个镜像移除
  kubepilot manifest remove clusterImg_name:tag
`
)

// manifestRemoveCommand 将一个或多个镜像从 manifest 中移除
func manifestRemoveCommand() *cobra.Command {
	mf := &image.ManifestRemoveOpts{}
	cmd := &cobra.Command{
		Use:     "remove",
		Short:   "将镜像从 manifest 中移除",
		Long:    longManifestRemoveDescription,
		Example: exampleManifestRemove,
		Args:    cobra.MinimumNArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			var (
				name           string
				instanceDigest digest.Digest
			)

			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}

			switch len(args) {
			case 0, 1:
				return errors.New("at least a list image and one or more instance digests must be specified ")
			case 2:
				name = args[0]
				if name == "" {
					return fmt.Errorf(`invalid image name "%s" `, args[0])
				}
				instanceSpec := args[1]
				if instanceSpec == "" {
					return fmt.Errorf(`invalid instance "%s" `, args[1])
				}
				d, err := digest.Parse(instanceSpec)
				if err != nil {
					return fmt.Errorf(`invalid instance "%s": %v `, args[1], err)
				}
				instanceDigest = d
			default:
				return errors.New("at least two arguments are necessary: list and digest of instance to remove from list ")
			}

			return e.RemoveFromManifest(name, instanceDigest, mf)
		},
	}

	return cmd
}

var (
	longManifestInspectDescription = `
  该命令用于用于查看 manifest 详细信息
`

	exampleManifestInspect = `
# 查看 manifest 详细信息:
  kubepilot manifest inspect clusterImg_name:tag
`
)

// manifestInspectCommand 查看 manifest 详细信息
func manifestInspectCommand() *cobra.Command {
	mf := &image.ManifestInspectOpts{}
	cmd := &cobra.Command{
		Use:     "inspect",
		Short:   "查看 manifest 详细信息",
		Long:    longManifestInspectDescription,
		Example: exampleManifestInspect,
		Args:    cobra.MinimumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			var (
				name string
			)
			switch len(args) {
			case 0:
				return errors.New("at least a source list ID must be specified")
			case 1:
				name = args[0]
				if name == "" {
					return fmt.Errorf(`invalid manifest name "%s" `, name)
				}
			default:
				return errors.New("only one argument is necessary for inspect: an manifest name")
			}

			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}

			schema2List, err := e.InspectManifest(name, mf)
			if err != nil {
				return err
			}

			b, err := json.MarshalIndent(schema2List, "", "    ")
			if err != nil {
				return err
			}

			fmt.Println(string(b))
			return nil
		},
	}
	return cmd
}

var (
	longManifestDeleteDescription = `
  该命令用于用于删除本地的一个或多个 manifest 
`

	exampleManifestDelete = `
# 删除 manifest:
  kubepilot manifest delete clusterImg_name:tag
`
)

// manifestDeleteCommand 删除本地的一个或多个 manifest
func manifestDeleteCommand() *cobra.Command {
	mf := &image.ManifestDeleteOpts{}
	deleteCommand := &cobra.Command{
		Use:     "delete",
		Short:   "删除 manifest",
		Long:    longManifestDeleteDescription,
		Example: exampleManifestDelete,
		Args:    cobra.MinimumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}

			return e.DeleteManifests(args, mf)
		},
	}
	return deleteCommand
}

var (
	longManifestPushDescription = `
  该命令用于用于将本地 manifest 推送到镜像仓库
`

	exampleManifestPush = `
# 将 manifest 及其关联的镜像推送到仓库:
  kubepilot manifest push --all clusterImg_name:tag
`
)

// manifestPushCommand 将本地 manifest 推送到镜像仓库
func manifestPushCommand() *cobra.Command {
	mf := &image.PushOpts{}
	cmd := &cobra.Command{
		Use:     "push",
		Short:   "推送manifest到镜像仓库",
		Example: exampleManifestPush,
		Args:    cobra.MinimumNArgs(1),
		Long:    longManifestPushDescription,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := auth.CheckAuthFile(mf.Authfile); err != nil {
				return err
			}

			var (
				name, destSpec string
			)

			switch len(args) {
			case 0:
				return errors.New("at least a source list ID must be specified ")
			case 1:
				name = args[0]
				destSpec = args[0]
			case 2:
				name = args[0]
				destSpec = args[1]
				if name == "" {
					return fmt.Errorf(`invalid manifest name "%s"`, name)
				}
				if destSpec == "" {
					return fmt.Errorf(`invalid image name "%s"`, destSpec)
				}
			default:
				return errors.New("need one Or two arguments are necessary to push: source and destination ")
			}

			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}

			return e.PushManifest(name, destSpec, mf)
		},
	}

	flags := cmd.Flags()
	mf.InitPushFlags(flags)

	return cmd
}
