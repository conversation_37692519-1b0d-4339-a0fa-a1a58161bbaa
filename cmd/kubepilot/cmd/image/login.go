package image

import (
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"k8s.io/klog/v2"
)

var (
	examleLoginCmd = `
# 登录registry服务
  kubepilot login registry.kube.io -u root -p 123456 
`
	longLoginDescription = `
  该命令是用户用来登录到regsitry中
  kubepilot login [registry domain] -u $user -p $password
`
)

func newLoginCmd() *cobra.Command {
	opts := &image.LoginOpts{}
	cmd := &cobra.Command{
		Use:     "login",
		Short:   "登录registry仓库",
		Long:    longLoginDescription,
		Example: examleLoginCmd,
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}
			opts.Domain = args[0]

			return e.<PERSON>gin(opts)
		},
		PostRun: func(cmd *cobra.Command, args []string) {
			klog.Info("Login successful~")
		},
	}
	fs := cmd.Flags()
	opts.InitLoginFlags(fs)
	if err := cmd.MarkFlagRequired("username"); err != nil {
		klog.Fatalf("failed to init flags: %v", err)
	}
	if err := cmd.MarkFlagRequired("passwd"); err != nil {
		klog.Fatalf("failed to init flag: %v", err)
	}

	return cmd
}
