package image

import (
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imge "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
)

var (
	exampleRmiCmd = `
# 删除集群镜像:
  kubepilot rmi registry.bingosoft.net/bingokube/nginx:v1.0
  kubepilot rmi $image_id

# 清除悬空镜像:
  kubepilot rmi --prune
  kubepilot rmi -p

# 强制删除某个集群镜像:
  kubepilot rmi registry.bingosoft.net/bingokube/nginx:v1.0 --force
  kubepilot rmi image_id --force
`
)

// newRmiCmd 删除镜像操作
func newRmiCmd() *cobra.Command {
	f := &image.RemoveImageOpts{}
	cmd := &cobra.Command{
		Use:     "rmi",
		Short:   "删除集群镜像",
		Example: exampleRmiCmd,
		Args:    cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			f.ImageNamesOrIDs = args
			e, err := imge.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}
			return e.RemoveImage(f)
		},
	}
	fs := cmd.Flags()
	f.InitRmiFlags(fs)

	return cmd
}
