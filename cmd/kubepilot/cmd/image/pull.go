package image

import (
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"k8s.io/klog/v2"
)

var (
	examplePullCmd = `
# 拉取集群镜像:
  kubepilot pull bingokube.bingosoft.net/bingokube/kubernetes:v1.22.22
`
	longPullCmdDescription = `
  该命令用于从registry拉取指定的集群镜像,用法类似docker pull.
  kubepilot pull [image name/id]
`
)

// newPullCmd 拉取集群镜像
func newPullCmd() *cobra.Command {
	opts := &image.PullOpts{}
	cmd := &cobra.Command{
		Use:     "pull",
		Short:   "从registry拉取集群镜像",
		Long:    longPullCmdDescription,
		Example: examplePullCmd,
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}
			opts.Image = args[0]
			imgId, err := e.Pull(opts)
			if err != nil {
				return fmt.Errorf("failed to pull image: %v", err)
			}
			klog.Infof("successful pull image [%s] with image id %s", opts.Image, imgId)

			return nil
		},
	}
	fs := cmd.Flags()
	opts.InitPullFlags(fs)

	return cmd
}
