package image

import (
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
)

var (
	exampleImagesCmd = `
# 集群镜像列表:
  kubepilot images
`
	longImagesDescription = `
  该命令用于显示所有集群镜像，类似于docker images.
`
)

// newImagesCmd 展示镜像信息
func newImagesCmd() *cobra.Command {
	opts := &image.ImagesOpts{}
	cmd := &cobra.Command{
		Use:     "images",
		Short:   "查看所有的集群镜像",
		Long:    longImagesDescription,
		Example: exampleImagesCmd,
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}
			return e.Images(opts)
		},
	}
	fs := cmd.Flags()
	opts.InitImagesFlags(fs)

	return cmd
}
