package image

import (
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"k8s.io/klog/v2"
)

var (
	exampleLoadCmd = `
# 加载特定的tar文件为集群镜像
  kubepilot load -i demo.tar
`
	longLoadCmdDescription = `
  该命令是从用户输入的tar文件中加载集群镜像保存到registry中.
  kubepilot load -i [xxx.tar]
`
)

// newLoadCmd 加载集群镜像
func newLoadCmd() *cobra.Command {
	opts := &image.LoadOpts{}
	cmd := &cobra.Command{
		Use:     "load",
		Short:   "从tar压缩文件加载集群镜像",
		Long:    longLoadCmdDescription,
		Example: exampleLoadCmd,
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			engine, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}

			if err = engine.Load(opts); err != nil {
				return err
			}

			klog.Infof("Load %s to cluster image storage", opts.Input)
			return nil
		},
	}

	fs := cmd.Flags()
	opts.InitLoadFlags(fs)
	if err := cmd.MarkFlagRequired("input"); err != nil {
		klog.Fatalf("failed to init Load flags: %v", err)
	}
	return cmd
}
