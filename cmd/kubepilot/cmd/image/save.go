package image

import (
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"k8s.io/klog/v2"
)

var (
	exampleSaveCmd = `
# 保存集群镜像到tar文件 
  kubepilot save -o xxx.tar registry.bingosoft.net/bingokube/nginx:v1.0.0
`
	longSaveCmdDescription = `
  该命令用于导出指定集群镜像为tar文件,类似docker save.
  kubepilot save -o [output file name] [image name/id]
`
)

// newSaveCmd 导出/保存镜像
func newSaveCmd() *cobra.Command {
	opts := &image.SaveOpts{}
	cmd := &cobra.Command{
		Use:     "save",
		Short:   "导出集群镜像到tar文件",
		Example: exampleSaveCmd,
		Long:    longSaveCmdDescription,
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}
			opts.ImageNameOrID = args[0]

			return e.Save(opts)
		},
	}
	fs := cmd.Flags()
	opts.InitSaveFlags(fs)
	if err := cmd.MarkFlagRequired("output"); err != nil {
		klog.Fatalf("failed init flags: %v", err)
	}

	return cmd
}
