package image

import (
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
)

func newTagCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "tag",
		Short:   "更改集群镜像的tag",
		Example: "kubepilot tag $old_image_name $new_image_name",
		Args:    cobra.MinimumNArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			opts := image.TagOpts{
				ImageNameOrID: args[0],
				Tags:          args[1:],
			}
			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}

			return e.Tag(&opts)
		},
	}

	return cmd
}
