package image

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"regexp"
	"text/template"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"golang.org/x/term"
)

var (
	exampleInspectCmd = `
# 查看集群镜像的元数据:
  kubepilot inspect imageName/imageId.
  kubepilot inspect --format '{{.OCIv1.Config.Env}}' imageName/imageId.
`
	longInspectDescription = `
  该命令主要是用于获取集群镜像的元数据.用法类似docker inspect.
`
)

// newInspectCmd 查看镜像内容
func newInspectCmd() *cobra.Command {
	opts := &image.InspectOpts{}
	cmd := &cobra.Command{
		Use:     "inspect",
		Short:   "查看集群镜像信息",
		Long:    longInspectDescription,
		Example: exampleInspectCmd,
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}
			// input image info
			opts.ImageNameOrID = args[0]
			imageSpec, err := e.Inspect(opts)
			if err != nil {
				return err
			}
			if len(opts.Format) == 0 {
				// 标准输出
				encoder := json.NewEncoder(os.Stdout)
				encoder.SetIndent("", "    ") // 4个字符
				if term.IsTerminal(int(os.Stdout.Fd())) {
					encoder.SetEscapeHTML(false)
				}
				return encoder.Encode(imageSpec)
			}
			format := opts.Format
			matched, err := regexp.MatchString("{{.*}}", format) // 通配符
			if err != nil {
				return err
			}
			if !matched {
				return errors.New("no content was matched by the format provided")
			}
			tem, err := template.New("format").Parse(format)
			if err != nil {
				return fmt.Errorf("unable parse format template: %v", err)
			}
			if err = tem.Execute(os.Stdout, imageSpec); err != nil {
				return err
			}
			if term.IsTerminal(int(os.Stdout.Fd())) {
				// 换行
				fmt.Println()
			}
			return nil
		},
	}
	fs := cmd.Flags()
	opts.InitInspectFlags(fs)

	return cmd
}
