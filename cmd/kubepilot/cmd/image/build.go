package image

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"

	"github.com/containerd/containerd/platforms"
	"github.com/containers/buildah/define"
	"github.com/containers/buildah/pkg/cli"
	"github.com/containers/buildah/pkg/parse"
	disreference "github.com/docker/distribution/reference"
	v1 "github.com/opencontainers/image-spec/specs-go/v1"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	imgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	buildimg "gitlab.bingosoft.net/bingokube/kubepilot/pkg/build/image"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/build/kubefile/utils"
	app "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application"
	appVersion "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application/version"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	entv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	imge "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine/buildah"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/strs"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/version"
	"k8s.io/apimachinery/pkg/util/json"
	"k8s.io/klog/v2"
)

var (
	longBuildDescription = `
  build命令用于从指定的Kubefile文件生成集群镜像，并管理指定的Kubefile和输入内容构建上下文，并构建一个全新的集群镜像.
`

	exampleBuild = `
# 简单的构建:
  kubepilot build -f Kubefile -t clusterImg_name:tag .

# 携带一些参数进行构建:
  kubepilot build -f Kubefile -t clusterImg_name:tag --args kye1=value1, key2=value2 

# 为脚本[init.sh]传参
  kubepilot build -f Kubefile -e "k1=v1,k2=v2,..." -t clusterImg_name:tag .

# 无缓存构建:
  kubepilot build -f Kubefile -t clusterImg_name:tag --no-cache

# 构建指定类型的集群镜像,目前有k8s和app两种(默认app):
  kubepilot build -f Kubefile -t clusterImg_name:tag --type=k8s
  kubepilot build -f Kubefile -t clusterImg_name:tag --type=app

# 构建多架构的集群镜像:
  kubepilot build -f Kubefile -t clusterImg_name:tag --platform linux/amd64,linux/arm64
`
)

var buildArgs = image.BuildOpts{}

// newBuildCmd 构建集群镜像
func newBuildCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "build",
		Short:   "基于Kubefile文件构建集群镜像",
		Long:    longBuildDescription,
		Example: exampleBuild,
		Args:    cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				// 命令`kubepilot build -t xxx:v1 .` 中这个'.'就是上下文
				buildArgs.ContextDir = args[0]
			}
			return build()
		},
	}
	flags := cmd.Flags()
	// init build cmd flags
	buildArgs.BuildFlags(flags)
	if err := cmd.MarkFlagRequired("tag"); err != nil {
		klog.Fatalf("The --tag should be specified!")
	}
	return cmd
}

func build() error {
	var (
		err error
		pf  v1.Platform
	)
	// init the engine what used to build image
	e, err := imge.NewImgEngine(image.EngineGlobalConfig{})
	if err != nil {
		return fmt.Errorf("failed to init image Engine: %v", err)
	}

	if len(buildArgs.Platforms) == 1 {
		// build single platform cluster image
		pf, err = platforms.Parse(buildArgs.Platforms[0])
		if err != nil {
			return err
		}
		if match := platforms.Default().Match(pf); match {
			return buildOneArchClusterImage(e, buildArgs.Tag, "", buildArgs.Platforms[0])
		}
	}
	return buildMultiArchClusterImage(e)
}

// buildMultiArchClusterImage 构建多架构的集群镜像
func buildMultiArchClusterImage(e imge.ImageManager) error {
	var (
		// use buildFlags.Tag as manifest name for multi arch build
		manifest = buildArgs.Tag
	)

	// create manifest firstly
	_, err := e.CreateManifest(manifest, &image.ManifestCreateOpts{})
	if err != nil {
		return fmt.Errorf("failed to create manifest for multi platform build, err: %w", err)
	}

	// build cluster image for platform
	for _, p := range buildArgs.Platforms {
		if err = buildOneArchClusterImage(e, "", manifest, p); err != nil {
			// clean manifest
			if err = e.DeleteManifests([]string{manifest}, &image.ManifestDeleteOpts{}); err != nil {
				klog.Warningf("failed to delete manifests int build single image: %v", err)
			}
			return fmt.Errorf("failed to build image with platform(%s): %w", p, err)
		}
	}

	return nil
}

// buildOneArchClusterImage 构建单一架构的集群镜像
func buildOneArchClusterImage(e imge.ImageManager, imgName, manifest, platform string) error {
	klog.V(5).Infof("start build cluster image [%s] for platform [%s]", imgName, platform)
	// 初始化kubefile的解析器
	decoder := utils.NewKubefileDecoder(app.AppRootRelPath, buildArgs, e, platform)
	// 解析kubefile data
	result, err := decodeKubefile(buildArgs, decoder)
	if err != nil {
		return err
	}
	klog.V(5).Infof("The result of kubefile [%+v]", result)
	defer func() {
		if err = result.CleanLegacyContext(); err != nil {
			klog.Warningf("failed to clean leagacy context in build cluster image: %v", err)
		}
	}()
	// save the parsed dockerfile to a temporary file
	// and give it to buildFlags(buildFlags.Kubefile = dockerfilePath)
	dockerfilePath, err := saveDockerfileAsTempfile(result.Dockerfile)
	if err != nil {
		return errors.Wrap(err, "failed to save docker file as tempfile")
	}
	defer func() {
		_ = os.Remove(dockerfilePath)
	}()
	buildArgs.DockerFilePath = dockerfilePath

	// set the image extension to oci image annotation
	imageExtension := buildImageExtensionOnResult(result, buildArgs.ImageType)
	// 该json package使用的是:k8s.io/apimachinery/pkg/util/json
	data, err := json.Marshal(imageExtension)
	if err != nil {
		return errors.Wrap(err, "failed to marshal image extension")
	}

	// add annotations to image. store some kubepilot specific information
	buildArgs.Annotations = append(buildArgs.Annotations, fmt.Sprintf("%s=%s", entv1beta1.KubepilotImageExtension, string(data)))
	buildArgs.Platforms = []string{platform}

	// if it is lite mode, just build it and return
	if buildArgs.BuildMode == image.WithLiteMode {
		// build single platform image
		if manifest == "" {
			if _, err = e.Build(&buildArgs); err != nil {
				return fmt.Errorf("failed to build cluster image with lite mode: %+v", err)
			}

			return nil
		}

		// build multi-platform image
		buildArgs.Tag = ""
		buildArgs.NoCache = true
		// imgid: storage image id
		imgId, err := e.Build(&buildArgs)
		if err != nil {
			return fmt.Errorf("failed to build cluster image with all mode: %+v", err)
		}

		return e.AddToManifest(manifest, []string{imgId}, &image.ManifestAddOpts{})
	}

	var (
		// use temp tag to do temp image build, because after build,
		// we need to download some container data loaded from rootfs to it.
		tempTag = imgName + strs.GetRandomString(8)
	)

	buildArgs.Tag = tempTag
	imgId, err := e.Build(&buildArgs)
	if err != nil {
		return errors.Errorf("error in building image, %v", err)
	}
	defer func() {
		// clean tmp build image
		for _, m := range []string{imgId, tempTag} {
			// the above image is intermediate image, we need to remove it when the build ends.
			if err = e.RemoveImage(&image.RemoveImageOpts{
				ImageNamesOrIDs: []string{m},
				Force:           true,
			}); err != nil {
				klog.Warningf("failed to remove image [%s], you need to remove it manually: %v", m, err)
			}
		}
	}()

	_os, arch, variant, err := parse.Platform(platform)
	if err != nil {
		return errors.Wrap(err, "failed to parse platform")
	}
	pform := &imgv1beta1.Platform{OS: _os, Architecture: arch, Variant: variant}
	containerID, containerImageList, err := applyImagesWithSinglePlatform(e, imgId, pform)
	if err != nil {
		return err
	}

	if err = updateContainerImageListAnnotations(e, containerID, containerImageList); err != nil {
		return err
	}

	return commitContainer(e, &commitContainerOpts{
		containerID: containerID,
		tag:         imgName,
		manifest:    manifest,
		platform:    *pform,
	})
}

// decodeKubefile decode the Kubefile to build Cluster image
func decodeKubefile(buildArgs image.BuildOpts, decoder *utils.KubefileDecoder) (*utils.KubefileResult, error) {
	var (
		contextDir = buildArgs.ContextDir
		f          = buildArgs.Kubefile
	)

	kubefile, err := findKubefile(contextDir, f)
	if err != nil {
		return nil, err
	}
	dir := filepath.Dir(kubefile)
	initScriptPath := filepath.Join(dir, "init.sh")
	if _, err = os.Stat(initScriptPath); err == nil {
		if err = executeInitScript(initScriptPath, buildArgs.ScriptsEnv); err != nil {
			return nil, fmt.Errorf("failed to execute init script: %v", err)
		}
	}

	kf, err := os.Open(filepath.Clean(kubefile))
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = kf.Close()
	}()

	res, err := decoder.ParseKubefile(kf)
	if err != nil {
		return nil, err
	}

	return res, nil
}

// executeInitScript 执行init.sh脚本
func executeInitScript(scriptPath string, env []string) error {
	klog.V(5).Infof("start excuting init script [%s]", scriptPath)
	cmd := exec.Command("/bin/bash", append([]string{scriptPath}, env...)...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

// findKubefile fined the context file
func findKubefile(contextDir, file string) (string, error) {
	var (
		kubefile = file
		err      error
	)

	ctxDir, err := convertContextDir(contextDir)
	if err != nil {
		return "", err
	}

	if len(kubefile) == 0 {
		kubefile, err = buildah.FindKubefile(ctxDir)
		if err != nil {
			return "", err
		}
	}
	return kubefile, nil
}

// convertContextDir covert the dir for context
func convertContextDir(cxtDir string) (string, error) {
	var (
		contextDir = cxtDir
		err        error
	)
	if len(contextDir) == 0 {
		contextDir, err = os.Getwd()
		if err != nil {
			return "", err
		}
	} else {
		// It was local.  Use it as is.
		contextDir, err = filepath.Abs(contextDir)
		if err != nil {
			return "", err
		}
	}

	return contextDir, nil
}

// saveDockerfileAsTempfile 在临时文件中保存dockerfile文件信息，用完后会被清除
func saveDockerfileAsTempfile(dockerFileContent string) (string, error) {
	f, err := os.CreateTemp("/tmp", "kubepilot-dockerfile")
	if err != nil {
		return "", err
	}

	defer func() {
		if err != nil {
			_ = os.Remove(f.Name())
		}
	}()

	_, err = f.WriteString(dockerFileContent)
	if err != nil {
		return "", err
	}

	return f.Name(), nil
}

// buildImageExtensionOnResult 基于kubefile解析出来的结果对镜像拓展信息进行初始化
func buildImageExtensionOnResult(result *utils.KubefileResult, imageType string) *entv1beta1.ImageExtension {
	extension := &entv1beta1.ImageExtension{
		Type:          imageType,
		Applications:  []appVersion.Interface{},
		Launch:        entv1beta1.Launch{},
		SchemaVersion: entv1beta1.ImageSpecSchemaVersionV1Beta1,
		Env:           result.GlobalEnv,
		BuildClient: entv1beta1.BuildClient{
			KubepilotVersion: version.Get().GitVersion,
			BuildahVersion:   define.Version,
		},
	}

	for _, app := range result.Applications {
		extension.Applications = append(extension.Applications, app)
	}
	extension.Launch.Cmds = result.RawCmds
	extension.Launch.AppNames = result.LaunchedAppNames

	return extension
}

// applyImagesWithSinglePlatform 构建单架构镜像
func applyImagesWithSinglePlatform(e imge.ImageManager, imageID string, platform *imgv1beta1.Platform) (string, []*entv1beta1.ContainerImage, error) {
	containerID, containerImageList, err := applyRegistryToImage(e, imageID, *platform)
	if err != nil {
		return "", nil, errors.Wrap(err, "error in apply registry data into image")
	}

	return containerID, containerImageList, nil
}

func applyRegistryToImage(e imge.ImageManager, imageID string, platform imgv1beta1.Platform) (string, []*entv1beta1.ContainerImage, error) {
	var containerImageList []*entv1beta1.ContainerImage

	_os, arch, variant := platform.OS, platform.Architecture, platform.Variant
	// this temporary file is used to execute image pull, and save it to /registry.
	// engine.BuildRootfs will generate an image rootfs, and link the rootfs to temporary dir(temp kubepilot rootfs).
	tmpDir, err := os.MkdirTemp("", "kubepilot")
	if err != nil {
		return "", nil, err
	}

	defer func() {
		if err = os.RemoveAll(tmpDir); err != nil {
			klog.Warningf("failed to rm link dir to rootfs: %v : %v", tmpDir, err)
		}
	}()

	tmpDirForLink := filepath.Join(tmpDir, "tmp-rootfs")
	containerID, err := e.CreateWorkingContainer(&image.BuildRootfsOpts{
		ImageNameOrID: imageID,
		DestDir:       tmpDirForLink,
	})
	if err != nil {
		return "", nil, errors.Wrapf(err, "failed to create working container, imageid: %s", imageID)
	}

	// 基于 manifest文件夹下的`imageList` 文件的内容下载docker镜像列表
	if buildArgs.ImageList != "" && file.IsFileExist(buildArgs.ImageList) {
		images, err := rw.NewFileReader(buildArgs.ImageList).ReadLines()
		if err != nil {
			return "", nil, err
		}
		for _, m := range images {
			klog.V(5).Infof("get container image(%s) with platform(%s) from build flag image list", m, platform.ToString())
			imgName, err := disreference.ParseNormalizedNamed(m)
			if err != nil {
				return "", nil, err
			}
			containerImageList = append(containerImageList, &entv1beta1.ContainerImage{
				Image:    imgName.String(),
				AppName:  "",
				Platform: &platform,
			})
		}
	}

	klog.V(5).Infof("success to parse images from [%s]", buildArgs.ImageList)

	// 自动解析docker镜像并将它们存储在集群镜像的临时文件夹`registry`中
	parsedContainerImageList, err := buildimg.ParseContainerImageList(tmpDirForLink)
	if err != nil {
		return "", nil, errors.Wrap(err, "failed to parse container image list")
	}
	for _, m := range parsedContainerImageList {
		klog.V(5).Infof("get container image(%s) with platform(%s) from build context", m.Image, platform.ToString())
		containerImageList = append(containerImageList, &entv1beta1.ContainerImage{
			Image:    m.Image,
			AppName:  m.AppName,
			Platform: &platform,
		})
	}

	klog.V(5).Infof("success to parse container images from [%s]", tmpDirForLink)

	// 需要忽略的镜像列表
	if buildArgs.IgnoredImageList != "" && file.IsFileExist(buildArgs.IgnoredImageList) {
		ignoredImageList, err := rw.NewFileReader(buildArgs.IgnoredImageList).ReadLines()
		if err != nil {
			return "", nil, err
		}

		imageList := containerImageList
		containerImageList = nil
		for _, list := range imageList {
			if !strs.IsInSlice(list.Image, ignoredImageList) {
				containerImageList = append(containerImageList, list)
			}
		}
	}

	registry := buildimg.NewRegistry(imgv1beta1.Platform{
		Architecture: arch,
		OS:           _os,
		Variant:      variant,
	})
	klog.V(5).Infof("start to pull and save images [%+v]", containerImageList)
	if err = registry.SaveImages(tmpDirForLink, entv1beta1.GetImageSliceFromContainerImageList(containerImageList)); err != nil {
		return "", nil, errors.Wrap(err, "failed to download container images")
	}

	// download container image from `imageListWithAuth.yaml`
	if buildArgs.ImageListWithAuth != "" && file.IsFileExist(buildArgs.ImageListWithAuth) {
		// pares middleware file: imageListWithAuth.yaml
		var imageSectionList []buildimg.ImgSection
		if err = file.UnmarshalFile(buildArgs.ImageListWithAuth, &imageSectionList); err != nil {
			return "", nil, err
		}
		for _, imageSection := range imageSectionList {
			for _, m := range imageSection.Images {
				klog.V(5).Infof("Get container image (%s) with platform (%s) from build flag image list", m, platform.ToString())
				containerImageList = append(containerImageList, &entv1beta1.ContainerImage{
					Image:    m,
					AppName:  "",
					Platform: &platform,
				})
			}
		}
		if err = buildimg.NewMiddlewarePuller(imgv1beta1.Platform{
			Architecture: arch,
			OS:           _os,
			Variant:      variant,
		}).PullWithImageSection(tmpDirForLink, imageSectionList); err != nil {
			return "", nil, err
		}
	}

	return containerID, containerImageList, nil
}

type commitContainerOpts struct {
	containerID string
	tag         string
	manifest    string
	platform    imgv1beta1.Platform
}

func updateContainerImageListAnnotations(e imge.ImageManager, containerID string, containerImageList []*entv1beta1.ContainerImage) error {
	klog.V(5).Infof("succcss to get containerImageList: %v", containerImageList)
	if len(containerImageList) == 0 {
		return nil
	}
	containerImageListJSON, err := json.Marshal(containerImageList)
	if err != nil {
		return errors.Wrap(err, "failed to marshal container image list")
	}
	containerImageListAnnotations := fmt.Sprintf("%s=%s", entv1beta1.KubepilotImageContainerImageList, string(containerImageListJSON))
	if err = e.Config(&image.ConfigOpts{
		ContainerID: containerID,
		Annotations: []string{containerImageListAnnotations},
	}); err != nil {
		return errors.Wrapf(err, "failed to List config of container images")
	}
	return nil
}

func commitContainer(e imge.ImageManager, opts *commitContainerOpts) error {
	tag := opts.tag
	manifest := opts.manifest
	containerID := opts.containerID
	platform := opts.platform
	id, err := e.Commit(&image.CommitOpts{
		Format:      cli.DefaultFormat(),
		Rm:          true,
		ContainerID: containerID,
		Image:       tag,
		Manifest:    manifest,
	})

	if err != nil {
		return fmt.Errorf("failed to commit image tag: %s, manifest: %s,err: %v", tag, manifest, err)
	}

	if len(manifest) > 0 {
		klog.Infof("image(%s) committed to manifest %s, id: %s", platform.ToString(), manifest, id)
	} else {
		klog.Infof("image(%s) named as %s, id: %s", platform.ToString(), tag, id)
	}

	return nil
}
