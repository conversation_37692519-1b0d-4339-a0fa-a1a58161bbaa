package image

import (
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
)

var (
	examplePushCmd = `
# 推送集群镜像:
  kubepilot push bingokube.bingosoft.net/bingokube/kubernetes:v1.22.22
`
	longPushCmdDescription = `
  该命令是用于推送集群镜像到指定的registry中,用法类似docker push.
  kubepilot push [image name/id]
`
)

// newPushCmd 推送集群镜像
func newPushCmd() *cobra.Command {
	opts := &image.PushOpts{}
	cmd := &cobra.Command{
		Use:     "push",
		Short:   "推送集群镜像到registry",
		Long:    longPushCmdDescription,
		Example: examplePushCmd,
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
			if err != nil {
				return err
			}
			opts.Image = args[0]

			return e.Push(opts)
		},
	}
	fs := cmd.Flags()
	opts.InitPushFlags(fs)

	return cmd
}
