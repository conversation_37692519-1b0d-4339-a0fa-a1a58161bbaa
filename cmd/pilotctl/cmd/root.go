package cmd

import (
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/version"
	"k8s.io/klog/v2"
)

var rootCmd = &cobra.Command{
	Use:   "pilotctl",
	Short: "该客户端主要用于执行certs或者ipvs等命令,目前应用kubepilot中.",
	Run: func(cmd *cobra.Command, args []string) {
		runHelp(cmd, args)
	},
	SilenceUsage:  true,
	SilenceErrors: true,
}

// Execute adds all sub commands into the root command and sets flags appropriately.
// This is called by main func. It only needs to happen once to the rootCmd
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		klog.Exitf("[pilotctl:%s]: %v", version.GetSingleVersion(), err)
	}
}

func init() {
	cobra.OnInitialize()
	rootCmd.AddCommand(newIpvsCmd(), newCertsCmd(), newRouteCmd())
	rootCmd.DisableAutoGenTag = true
}

func runHelp(cmd *cobra.Command, args []string) {
	_ = cmd.Help()
}
