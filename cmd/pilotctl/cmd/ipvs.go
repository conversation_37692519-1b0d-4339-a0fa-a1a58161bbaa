package cmd

import (
	"github.com/labring/lvscare/care"
	"github.com/spf13/cobra"
	"k8s.io/klog/v2"
)

var (
	exampleIpvs = `
# 创建ipvs规则:
  pilotctl ipvs --vs *************:6443 --rs *************:6443 --rs *************:6443 --health-path /healthz --health-schem https --run-once
# 清除ipvs规则:
  pilotctl ipvs clean
`
	longIPVSDescription = `
  该命令用于操作ipvs规则.
  kubepilot ipvs --vs $[virtual server ip address] --rs $[real server ip address] --rs $[real server ip address] --health-path /healthz --health-schem $scheme-type --run-once
`
)

var lvs care.LvsCare

// newIpvsCmd init ipvs cmd
func newIpvsCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "ipvs",
		Short:   "创建ipvs规则或者操作已存在的ipvs规则",
		Example: exampleIpvs,
		Long:    longIPVSDescription,
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			klog.V(5).Infof("The ipvs flags: [%+v]", lvs)
			lvs.VsAndRsCare()

			return nil
		},
	}
	fs := cmd.Flags()
	fs.BoolVar(&lvs.RunOnce, "run-once", false, "是否只运行一次(默认false)")
	fs.BoolVarP(&lvs.Clean, "clean", "c", true, "添加节点IP到ipvs规则前是否清楚ipvs规则(默认为true),若没有rule则不会做任何事")
	fs.StringVar(&lvs.VirtualServer, "vs", "", "虚拟IP,如*************:6443(注意->端口还是6443)")
	fs.StringSliceVar(&lvs.RealServer, "rs", []string{}, "真实master服务器节点IP")
	fs.StringVar(&lvs.HealthPath, "health-path", "/healthz", "用来做健康检查的路由地址")
	fs.StringVar(&lvs.HealthSchem, "health-schem", "https", "健康检查使用的scheme")
	fs.Int32Var(&lvs.Interval, "interval", 5, "健康检查的时间间隔,单位是秒")

	return cmd
}
