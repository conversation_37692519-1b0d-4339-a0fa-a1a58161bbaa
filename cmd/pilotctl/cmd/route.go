package cmd

import (
	"fmt"
	"net"

	"github.com/spf13/cobra"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
)

func newRouteCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "route",
		Short: "该命令用于对路由主机或网关进行操作",
	}
	cmd.AddCommand(newCheckCmd(), newAddCmd(), newDelCmd(), newVersionCmd())

	return cmd
}

func newCheckCmd() *cobra.Command {
	var host string
	cmd := &cobra.Command{
		Use:     "check",
		Short:   "该命令检查ip是否来自默认网卡",
		Example: "pilotctl route check --host *************",
		RunE: func(cmd *cobra.Command, args []string) error {
			h := net.ParseIP(host)
			if h == nil {
				return fmt.Errorf("input host (%s) is invalid", host)
			}
			return unet.CheckIsDefaultRoute(h)
		},
	}
	cmd.Flags().StringVar(&host, "host", "", "检查该IP是否来自默认的网卡")

	return cmd
}

func newAddCmd() *cobra.Command {
	var (
		host, gateway string
	)
	cmd := &cobra.Command{
		Use:     "add",
		Short:   "添加host地址到指定的网关",
		Example: "pilotctl route add --host ************* --gateway ********",
		RunE: func(cmd *cobra.Command, args []string) error {
			h := net.ParseIP(host)
			if h == nil {
				return fmt.Errorf("input host (%s) is invalid", host)
			}
			gw := net.ParseIP(gateway)
			if gw == nil {
				return fmt.Errorf("input gateway (%s) is invalid", gateway)
			}
			r := unet.NewRouter(h, gw)
			return r.SetRoute()
		},
	}
	cmd.Flags().StringVar(&host, "host", "", "指定host地址")
	cmd.Flags().StringVar(&gateway, "gateway", "", "指定gateway地址")

	return cmd
}

func newDelCmd() *cobra.Command {
	var (
		host, gateway string
	)
	cmd := &cobra.Command{
		Use:     "del",
		Short:   "从网关删除指定的host地址",
		Example: "pilotctl route del --host ************* --gateway ********",
		RunE: func(cmd *cobra.Command, args []string) error {
			h := net.ParseIP(host)
			if h == nil {
				return fmt.Errorf("input host (%s) is invalid", host)
			}
			gw := net.ParseIP(gateway)
			if gw == nil {
				return fmt.Errorf("input gateway (%s) is invalid", gateway)
			}
			r := unet.NewRouter(h, gw)
			return r.DelRoute()
		},
	}
	cmd.Flags().StringVar(&host, "host", "", "指定host地址")
	cmd.Flags().StringVar(&gateway, "gateway", "", "指定gateway地址")

	return cmd
}
