package cmd

import (
	"fmt"
	"net"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/certs"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	"k8s.io/klog/v2"
)

func newCertsCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "certs",
		Short: "处理集群证书相关的命令,类似kubeadm certs",
	}
	// add sub command
	cmd.AddCommand(newCertGenerate(), newCertUpdate())
	return cmd
}

var (
	exampleGen = `
# 生成集群证书
  pilotctl certs gen --node-ip ************* --node-name master0 --dns-domain bingokube.bingosoft.net --alt-names bingokube.local --service-didr ***********/24
`
	longGenDescription = `
  此子命令主要用于生成与kubernetes集群相关的证书，偏向于从无到有.
`
)

func newCertGenerate() *cobra.Command {
	f := &commands.CertGenFlags{}
	cmd := &cobra.Command{
		Use:     "gen",
		Short:   "生成集群相关证书",
		Example: exampleGen,
		Long:    longGenDescription,
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			ip := net.ParseIP(f.NodeIP)
			if ip == nil {
				return fmt.Errorf("the node ip [%s] is not a valid IP format", f.NodeIP)
			}
			return certs.GenerateAllKubernetesCerts(f.CertPath, f.CertEtcdPath, f.NodeName, f.ServiceCIDR, f.DNSDomain, f.AltNames, ip)
		},
	}
	fs := cmd.Flags()
	f.InitCertGenFlags(fs)

	return cmd
}

var (
	exampleUpdate = `
# 更新k8s API服务器证书:
  pilotctl certs update --alt-names bingokube.local
`
	longUpdateDescription = `
  该子命令主要用于更新与kubernetes api服务器相关的证书，偏向于将IP或DNS域更新至已有的证书.
`
)

func newCertUpdate() *cobra.Command {
	f := &commands.CertUpdateFlags{}
	cmd := &cobra.Command{
		Use:     "update",
		Short:   "更新k8s api服务器证书的IP或DNS域",
		Example: exampleUpdate,
		Long:    longUpdateDescription,
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(f.AltNames) == 0 {
				return fmt.Errorf("IP address or DNS domain is empty")
			}
			return certs.UpdateAPIServerCertSans(certs.KubeDefaultCertPath, f.AltNames)
		},
	}
	fs := cmd.Flags()
	f.InitCertUpdateFlags(fs)
	if err := cmd.MarkFlagRequired("alt-names"); err != nil {
		klog.Fatalf("failed to init cert update flags: %v", err)
	}

	return cmd
}
