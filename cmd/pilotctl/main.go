package main

import (
	"github.com/containers/buildah"
	"gitlab.bingosoft.net/bingokube/kubepilot/cmd/pilotctl/cmd"
	"k8s.io/klog/v2"
)

func main() {
	// 这段代码是在检查当前的进程是否是一个由 InitReexec 函数创建的重新执行（reexec）的进程。
	// 如果是，InitReexec() 函数会返回 true，并执行容器的运行时程序，然后结束当前进程。
	// 如果不是，函数会返回 false，主程序会正常执行.
	if buildah.InitReexec() {
		klog.Info("The process is a reexec process created by the InitReexec function")
		return
	}

	cmd.Execute()
}
