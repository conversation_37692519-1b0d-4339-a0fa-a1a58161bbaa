### Go template
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
/output*/
/_output*/
/_output

# Dependency directories (remove the comment below to include it)
/vendor/
/vendor*/
vendor/*
/vendor

# Files generated by JetBrains IDEs, e.g. IntelliJ IDEA
.idea/
*.iml
.run/

# Vscode files
.vscode
**/testdata/