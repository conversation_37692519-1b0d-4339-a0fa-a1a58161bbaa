//go:build tools
// +build tools

/*
Copyright 2019 The Kubernetes Authors.
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Copied from https://github.com/kubernetes/sample-controller/blob/master/hack/tools.go

// This package imports things required by build scripts, to force `go mod` to see them as dependencies
// 用来引入k8s.io/code-generator依赖
package code_generator

import (
	_ "k8s.io/code-generator"
	_ "k8s.io/klog/hack/tools/logcheck"
	_ "k8s.io/kube-openapi/cmd/openapi-gen"
)
