#!/usr/bin/env bash

NAME=""
VERSION=""

# 运行参数配置
for arg in $@
do
	case $arg in
		--NAME=*)
		NAME="${arg#*=}"
		shift
		;;
	  --VERSION=*)
		VERSION="${arg#*=}"
		shift
		;;
		*)
		;;
	esac
done

# 获取项目目录
SCRIPT_ROOT=$(pwd)
echo ${SCRIPT_ROOT}
# 工程名称
PROJECT_NAME="kubepilot"
# informer、clientset、lister等工具生成的位置
CLIENT_PKG="clients"
# apis目录
APIS_PKG="pkg/apis"
# deploys目录
DEPLOY_PKG="deploys"
# boilerplate文件
BOILERPLATE_PKG="/hack/code-generator/boilerplate/boilerplate.generatego.txt"

# 代码生成项目
CODEGEN_PKG="hack/code-generator/code-generator"
# 项目包前置
PROJECT_PKG_PREFIX="gitlab.bingosoft.net\/bingokube\/"

OLD_MODULE="module gitlab.bingosoft.net\/bingokube\/kubepilot"
NEW_MODULE="module kubepilot"

# 日志输出
function log() {
    type="info"
    if [ "$2" != "" ]; then
      type="$2"
    fi
    echo "[$type] $1"
}
# 参数校验
function verify_parameter() {
    if [ "$NAME" == "" ]; then
        log "NAME can not be empty" "error"
        exit 1
    fi

    if [ "$VERSION" == "" ]; then
        log "VERSION can not be empty" "error"
        exit 1
    fi
}
# 环境准备
function prepare() {
      # 校验GOROOT
      if [ -z "$GOROOT" ]; then
        log "GOROOT is not set" "error"
         exit 1
      else
        if [ "$(ls -A $GOROOT)" ]; then
           log "GOROOT directory is not empty"
        else
           log "GOROOT directory is empty" "error"
           exit 1
        fi
      fi
     # 校验GOBIN
     if [ -z "$GOBIN" ]; then
       log "GOBIN is not set" "error"
        exit 1
     else
       if [ "$(ls -A $GOBIN)" ]; then
          log "GOBIN directory is not empty"
       else
          log "GOBIN directory is empty" "error"
           exit 1
       fi
     fi
     # 校验GOPATH
      if [ -z "$GOPATH" ]; then
        log "GOPATH is not set" "error"
         exit 1
      else
        if [ "$(ls -A $GOPATH)" ]; then
           log "GOPATH directory is not empty"
        else
           log "GOPATH directory is empty" "error"
            exit 1
        fi
      fi
    # 安装代码生成二进制到GOBIN下
    go install sigs.k8s.io/controller-tools/cmd/controller-gen@v0.6.2
    # 修改go.mod配置
    sed -i.bak "s/${OLD_MODULE}/${NEW_MODULE}/g" "${SCRIPT_ROOT}/go.mod"
    rm "${SCRIPT_ROOT}/go.mod.bak"

}
# 回滚
function rollback() {
    log "rollback go.mod"
    sed -i.bak "s/${NEW_MODULE}/${OLD_MODULE}/g" "${SCRIPT_ROOT}/go.mod"
    rm "${SCRIPT_ROOT}/go.mod.bak"
}

# 生成代码
function generate_code() {
    # 生成deepcopy,client,informer,lister
    log "code gen [${NAME}/${VERSION}] deepcopy,client,informer,lister"
    bash "${SCRIPT_ROOT}/${CODEGEN_PKG}"/generate-groups.sh \
      "deepcopy" \
      ${PROJECT_NAME}/${CLIENT_PKG}/${NAME}/${VERSION} \
      ${PROJECT_NAME}/${APIS_PKG} \
      ${NAME}:${VERSION} \
      --go-header-file "${SCRIPT_ROOT}"${BOILERPLATE_PKG} \
      --output-base ${SCRIPT_ROOT}/../
    # 直接判断上一个命令的退出状态码
    if [ $? -ne 0 ]; then
      rollback
    fi

}


verify_parameter
prepare
generate_code
rollback




