# ==============================================================================
# Makefile helper functions for helm chart
#

COMMANDS ?= $(filter-out %.md, $(wildcard ${ROOT_DIR}/cmd/*))
HELMS ?= $(foreach cmd,${COMMANDS},$(notdir ${cmd}))

CHART_DIR ?= $(wildcard ${ROOT_DIR}/helm)
PROJECT_NAME ?= $(shell basename `git rev-parse --show-toplevel`)
CHART_REPO_NAME ?= ${PROJECT_GROUP}
CHART_REPO ?= http://registry.bingosoft.net/chartrepo/${PROJECT_GROUP}
KUBE_CONFIG ?= ~/.kube/config
REMOTE_KUBE_CONFIG ?=
CHART_VERSION ?= $(shell cat ${ROOT_DIR}/version)
HELM_EXTRA_ARGS ?=

ifneq ($(REMOTE_KUBE_CONFIG), )
_ := $(shell wget http://10.202.37.11:81/bingokube/devops/kubeconfig/$(REMOTE_KUBE_CONFIG)/config -O _output/config)
KUBE_CONFIG = _output/config
endif

# 定义一个函数用于判断版本号是否符合 "vx.x.x" 格式
is_valid_version = $(shell echo "$(1)" | grep -E -q '^v[0-9]+\.[0-9]+\.[0-9]+$$'; echo $$?)

ifeq ($(call is_valid_version,$(VERSION)),0)
_HELM_BUILD_EXTRA_ARGS := --version $(VERSION)
endif

.PHONY: upgrade.build
upgrade.build: $(addprefix upgrade.build., $(addprefix $(PREFIX), $(HELMS)))

repo.verify:
	@if ! helm repo list | grep -q ${PROJECT_GROUP}; then \
		echo "Adding Helm repository ${PROJECT_GROUP}..."; \
		helm repo add ${PROJECT_GROUP} http://registry.bingosoft.net/chartrepo/${PROJECT_GROUP}; \
		helm repo update; \
	fi

.PHONY: upgrade.build.%
upgrade.build.%: repo.verify
	$(eval COMMAND := $(word 1,$(subst ., ,$*)))
	echo "===========> Upgrading app $(COMMAND):$(CHART_VERSION)"; \
	helm upgrade --install -n $(NAME_SPACE) $(COMMAND) ${PROJECT_GROUP}/$(COMMAND) --set image.pullPolicy=Always --kubeconfig $(KUBE_CONFIG) --reuse-values --recreate-pods --version $(CHART_VERSION) $(HELM_EXTRA_ARGS); \

.PHONY: uninstall.build
uninstall.build: $(addprefix uninstall.build., $(addprefix $(PREFIX), $(HELMS)))

.PHONY: uninstall.build.%
uninstall.build.%:
	$(eval COMMAND := $(word 1,$(subst ., ,$*)))
	@if [ -d "helm/$(COMMAND)" ]; then \
		echo "===========> Uninstalling app $(COMMAND)"; \
		helm uninstall -n $(NAME_SPACE) $(COMMAND) --kubeconfig $(KUBE_CONFIG); \
	else \
		echo "Helm chart $(COMMAND) does not exist."; \
	fi


.PHONY: install.build
install.build: $(addprefix install.build., $(addprefix $(PREFIX), $(HELMS)))

.PHONY: install.build.%
install.build.%:
	$(eval COMMAND := $(word 1,$(subst ., ,$*)))
	@if [ -d "helm/$(COMMAND)" ]; then \
		echo "===========> Installing app $(COMMAND)"; \
		helm install -n $(NAME_SPACE) $(COMMAND) $(CHART_REPO_NAME)/$(COMMAND) --kubeconfig $(KUBE_CONFIG); \
	else \
		echo "Helm chart $(COMMAND) does not exist."; \
	fi

.PHONY: install.build
install.build: $(addprefix install.build., $(addprefix $(PREFIX), $(HELMS)))


.PHONY: helm.build.%
helm.build.%:
	$(eval COMMAND := $(word 1,$(subst ., ,$*)))
	@if [ -d "helm/$(COMMAND)" ]; then \
		echo "===========> Push chart $(COMMAND)"; \
		helm repo list | grep -q $(CHART_REPO_NAME) 2>/dev/null || helm repo add $(CHART_REPO_NAME) $(CHART_REPO); \
        helm cm-push $(CHART_DIR)/$(COMMAND) $(CHART_REPO_NAME) -u admin -p Harbor12345 $(_HELM_BUILD_EXTRA_ARGS); \
	else \
		echo "Helm chart $(COMMAND) does not exist."; \
	fi


.PHONY: helm.build
helm.build: $(addprefix helm.build., $(addprefix $(PREFIX), $(HELMS)))