package clusterconfig

import (
	"encoding/base64"
	"testing"

	confv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/config/v1beta1"

	"github.com/stretchr/testify/assert"
)

func TestNewProcessorsAndRun(t *testing.T) {
	tests := []struct {
		name     string
		config   *confv1beta1.Config
		wantData string
		wantErr  bool
	}{
		{
			name: "value|toJson|toBase64|toSecret",
			config: &confv1beta1.Config{
				Spec: confv1beta1.ConfigSpec{
					Process: "value|toJson|toBase64|toSecret",
					Data: `
config:
  username: root
  passwd: xxx
`,
				},
			},
			wantData: "config: " + base64.StdEncoding.EncodeToString([]byte(`{"passwd":"xxx","username":"root"}`)) + "\n",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := NewProcessorsAndRun(tt.config); (err != nil) != tt.wantErr {
				t.Errorf("NewProcessorsAndRun() error = %v, wantErr %v", err, tt.wantErr)
			}

			assert.Equal(t, tt.wantData, tt.config.Spec.Data)
		})
	}
}
