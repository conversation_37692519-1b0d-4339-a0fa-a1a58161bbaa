package clusterconfig

import (
	"bytes"
	"fmt"
	stdos "os"
	"path/filepath"
	"strings"

	confv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/config/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
	"k8s.io/klog/v2"

	"github.com/imdario/mergo"
	"gopkg.in/yaml.v3"
	corev1 "k8s.io/api/core/v1"
	k8sYaml "sigs.k8s.io/yaml"
)

const (
	Merge = "merge"
)

type Interface interface {
	// Dump Configs from Clusterfile to the cluster rootfs
	Dump(configs []confv1beta1.Config) error
}

type Dumper struct {
	// rootPath typically is cluster image mounted base directory.
	rootPath string
}

func NewConfiguration(rootPath string) Interface {
	return &Dumper{
		rootPath: rootPath,
	}
}

func (c *Dumper) Dump(configs []confv1beta1.Config) error {
	if len(configs) == 0 {
		klog.V(3).Info("no config is found")
		return nil
	}

	if err := c.WriteFiles(configs); err != nil {
		return fmt.Errorf("failed to dump config files %v", err)
	}
	return nil
}

func (c *Dumper) WriteFiles(configs []confv1beta1.Config) error {
	for _, config := range configs {
		// #nosec
		if err := NewProcessorsAndRun(&config); err != nil {
			return err
		}

		configData := []byte(config.Spec.Data)
		path := config.Spec.Path
		if config.Spec.APPName != "" {
			path = filepath.Join(application.AppRootRelPath, config.Spec.APPName, path)
		}
		configPath := filepath.Join(c.rootPath, path)

		klog.V(5).Infof("dumping config:%+v\n on the target file", config)
		if !file.IsFileExist(configPath) {
			err := rw.NewCommonWriter(configPath).WriteFile(configData)
			if err != nil {
				return fmt.Errorf("failed to overwrite config file %s: %v", configPath, err)
			}
			continue
		}

		contents, err := stdos.ReadFile(filepath.Clean(configPath))
		if err != nil {
			return err
		}

		// todo: its strange to use config.Spec.Process to control config dump strategy.
		if strings.Contains(config.Spec.Process, toSecretProcessorName) {
			if configData, err = convertSecretYaml(contents, configData); err != nil {
				return fmt.Errorf("faild to convert to secret file: %v", err)
			}
		}
		// Only files in yaml format are supported.
		// if Strategy is "Merge" will deeply merge each yaml file section.
		// if not, overwrite the whole file content with config data.
		if config.Spec.Strategy == Merge {
			if configData, err = getMergeConfigData(contents, configData); err != nil {
				return err
			}
		}

		err = rw.NewCommonWriter(configPath).WriteFile(configData)
		if err != nil {
			return fmt.Errorf("failed to write config file %s: %v", configPath, err)
		}
	}
	return nil
}

// getMergeConfigData merge data to each section of given file with overriding.
// given file is must be yaml marshalled.
func getMergeConfigData(contents, data []byte) ([]byte, error) {
	var (
		configs    [][]byte
		srcDataMap = make(map[string]interface{})
	)

	err := yaml.Unmarshal(data, &srcDataMap)
	if err != nil {
		return nil, fmt.Errorf("failed to load config data: %v", err)
	}

	for _, rawCfgData := range bytes.Split(contents, []byte("---\n")) {
		destConfigMap := make(map[string]interface{})

		err = yaml.Unmarshal(rawCfgData, &destConfigMap)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal config data: %v", err)
		}

		err = mergo.Merge(&destConfigMap, &srcDataMap, mergo.WithOverride)
		if err != nil {
			return nil, fmt.Errorf("failed to merge config: %v", err)
		}

		cfg, err := yaml.Marshal(destConfigMap)
		if err != nil {
			return nil, err
		}

		configs = append(configs, cfg)
	}
	return bytes.Join(configs, []byte("---\n")), nil
}

func convertSecretYaml(contents, data []byte) ([]byte, error) {
	secret := corev1.Secret{}
	dataMap := make(map[string]string)
	if err := k8sYaml.Unmarshal(data, &dataMap); err != nil {
		return nil, err
	}

	if err := k8sYaml.Unmarshal(contents, &secret); err != nil {
		return nil, err
	}

	if secret.Data == nil {
		secret.Data = make(map[string][]byte)
	}
	// set secret data
	for k, v := range dataMap {
		v := []byte(v)
		secret.Data[k] = v
	}
	return k8sYaml.Marshal(secret)
}
