package docker

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/docker/cli/cli/config/types"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
)

type Item struct {
	Auth string `json:"auth"`
}

type Auth struct {
	Auths map[string]Item `json:"auths"`
}

func (d *Auth) Get(domain string) (string, string, error) {
	auth := d.Auths[domain].Auth
	if auth == "" {
		return "", "", fmt.Errorf("auth for %s doesn't exist", domain)
	}

	decode, err := base64.StdEncoding.DecodeString(auth)
	if err != nil {
		return "", "", err
	}
	i := bytes.IndexRune(decode, ':')

	if i == -1 {
		return "", "", fmt.Errorf("auth base64 has problem of format")
	}

	return string(decode[:i]), string(decode[i+1:]), nil
}

type AuthService struct {
	FilePath    string
	AuthContent Auth `json:"auths"`
}

func (s *AuthService) GetAuthByDomain(domain string) (types.AuthConfig, error) {
	defaultAuthConfig := types.AuthConfig{ServerAddress: domain}

	user, passwd, err := s.AuthContent.Get(domain)
	if err != nil {
		return defaultAuthConfig, err
	}

	return types.AuthConfig{
		Username:      user,
		Password:      passwd,
		ServerAddress: domain,
	}, nil
}

func NewAuthService() (AuthService, error) {
	var (
		authFile = file.DefaultRegistryAuthConfigDir()
		ac       = Auth{Auths: map[string]Item{}}
		das      = AuthService{FilePath: authFile, AuthContent: ac}
	)

	if !file.IsFileExist(authFile) {
		return das, nil
	}

	content, err := os.ReadFile(filepath.Clean(authFile))
	if err != nil {
		return das, fmt.Errorf("unable read auth file about %s: %v", authFile, err)
	}

	if err = json.Unmarshal(content, &ac); err != nil {
		return das, err
	}
	das.AuthContent = ac

	return das, nil
}
