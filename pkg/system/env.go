package system

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/containers/storage/pkg/homedir"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
)

type envConfig struct{}

var globalConfig *envConfig

func init() {
	globalConfig = &envConfig{}
}

// GetConfigValue eeturn value from Config Option
func GetConfigValue(key string) (string, error) {
	config, err := globalConfig.getValueOrDefault(key)
	if err != nil {
		return "", err
	}

	return config.DefaultValue, nil
}

// GetConfig return Config Option
func GetConfig(key string) (*ConfigOption, error) {
	return globalConfig.getValueOrDefault(key)
}

type ConfigOption struct {
	Key          string
	Description  string
	DefaultValue string
	OSEnv        string
}

var configOptions = []ConfigOption{
	{
		Key:          PromptConfigKey,
		Description:  "toggle interactive prompting in the terminal.",
		DefaultValue: "enabled",
	},
	{
		Key:          RuntimeRootConfigKey,
		Description:  "root directory for persistent runtime actions/configs.",
		DefaultValue: filepath.Join(homedir.Get(), fmt.Sprintf(".%s", constants.ClientName)),
	},
	{
		Key:          DataRootConfigKey,
		Description:  "cluster root directory for remote.",
		DefaultValue: filepath.Join("/var/lib", constants.ClientName),
	},
	// {
	// 	Key:          BuildahFormatConfigKey,
	// 	Description:  "`format` of the image manifest and metadata.",
	// 	DefaultValue: buildah.OCI,
	// 	OSEnv:        BuildahFormatConfigKey,
	// },
	{
		Key:         BuildahLogLevelConfigKey,
		Description: `the log level to be used in buildah modules, either "trace", "debug", "info", "warn", "error", "fatal", or "panic".`,
		OSEnv:       BuildahLogLevelConfigKey,
	},
	{
		Key:         ContainerStorageConfEnvKey,
		Description: "path of container storage config file, setting this env will override the default location",
		OSEnv:       ContainerStorageConfEnvKey,
	},
	{
		Key:          SyncWorkDirEnvKey,
		Description:  "whether to sync runtime root dir to all master nodes for backup purpose",
		DefaultValue: "true",
	},
}

const (
	PromptConfigKey            = "PROMPT"
	RuntimeRootConfigKey       = "RUNTIME_ROOT"
	DataRootConfigKey          = "DATA_ROOT"
	BuildahFormatConfigKey     = "BUILDAH_FORMAT"
	BuildahLogLevelConfigKey   = "BUILDAH_LOG_LEVEL"
	ContainerStorageConfEnvKey = "CONTAINERS_STORAGE_CONF"
	SyncWorkDirEnvKey          = "SYNC_WORKDIR"
)

func (*envConfig) getValueOrDefault(key string) (*ConfigOption, error) {
	for _, option := range configOptions {
		if option.Key == key {
			if option.OSEnv == "" {
				option.OSEnv = strings.ReplaceAll(strings.ToUpper(constants.ClientName+"_"+option.Key), "-", "_")
			}
			if value, ok := os.LookupEnv(option.OSEnv); ok {
				option.DefaultValue = value
			}
			return &option, nil
		}
	}
	return nil, fmt.Errorf("unable found config from %s", key)
}

func ConfigOptions() []ConfigOption {
	return configOptions
}
