package v1beta1

import (
	"net"
	"reflect"
	"testing"
)

func TestCluster_GetTargetsIPList(t *testing.T) {
	// 准备测试数据
	hosts := []Host{
		{
			IPS:   []net.IP{net.ParseIP("***********")},
			Roles: []string{"master"},
		},
		{
			IPS:   []net.IP{net.ParseIP("***********")},
			Roles: []string{"node"},
		},
		{
			IPS:   []net.IP{net.ParseIP("***********")},
			Roles: []string{"node"},
		},
	}
	cluster := &Cluster{
		Spec: ClusterSpec{Hosts: hosts},
	}

	tests := []struct {
		name     string
		ips      []string
		roles    []string
		expected []string
	}{
		{
			name:     "指定IP列表",
			ips:      []string{"***********", "***********"},
			roles:    nil,
			expected: []string{"***********", "***********"},
		},
		{
			name:     "指定角色列表",
			ips:      nil,
			roles:    []string{"node"},
			expected: []string{"***********", "***********"},
		},
		{
			name:     "两个参数都为空",
			ips:      nil,
			roles:    nil,
			expected: []string{"***********", "***********", "***********"},
		},
		{
			name:     "IP优先于角色",
			ips:      []string{"***********"},
			roles:    []string{"node"},
			expected: []string{"***********"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := cluster.GetTargetsIPList(tt.ips, tt.roles)

			// 将结果转换为字符串切片以便比较
			resultStrings := make([]string, len(result))
			for i, ip := range result {
				resultStrings[i] = ip.String()
			}

			// 比较结果
			if !reflect.DeepEqual(resultStrings, tt.expected) {
				t.Errorf("GetTargetsIPList() = %v, want %v", resultStrings, tt.expected)
			}
		})
	}
}
