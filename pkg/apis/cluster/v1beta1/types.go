package v1beta1

import (
	"net"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	corev1 "k8s.io/api/core/v1"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type SSH struct {
	Encrypted bool   `json:"encrypted,omitempty"`
	User      string `json:"user,omitempty"`
	Passwd    string `json:"passwd,omitempty"`
	Pk        string `json:"pk,omitempty"`
	PkPasswd  string `json:"pkPasswd,omitempty"`
	Port      string `json:"port,omitempty"`
}

// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// ClusterSpec defines the desired state of Cluster
type ClusterSpec struct {
	// desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file
	// Foo is an example field of Cluster. Edit Cluster_types.go to remove/update
	Image string `json:"image,omitempty"`
	// Why env not using map[string]string
	// Because some argument is list, like: CertSANS=127.0.0.1 CertSANS=localhost, if ENV is map, will merge those two values
	// but user want to config a list, using array we can convert it to {CertSANS:[127.0.0.1, localhost]}
	Env     []string `json:"env,omitempty"`
	CMDArgs []string `json:"cmd_args,omitempty"`
	CMD     []string `json:"cmd,omitempty"`
	// APPNames This field allows user to specify the app name they want to run launch.
	APPNames         []string               `json:"appNames,omitempty"`
	Hosts            []Host                 `json:"hosts,omitempty"`
	SSH              SSH                    `json:"ssh,omitempty"`
	ContainerRuntime ContainerRuntimeConfig `json:"containerRuntime,omitempty"`
	// HostAliases holds the mapping between IP and hostnames that will be injected as an entry in the
	// host's hosts file.
	HostAliases []HostAlias `json:"hostAliases,omitempty"`
	// Registry field contains configurations about local registry and remote registry.
	Registry Registry `json:"registry,omitempty"`

	// DataRoot set cluster rootfs directory path.
	// if not set, default value is "/var/lib/kubepilot/data"
	DataRoot string `json:"dataRoot,omitempty"`
	// Components component are installed sequentially
	Components []string `json:"components,omitempty"`
}

type ContainerRuntimeConfig struct {
	Type string `json:"type,omitempty"`
}

type Host struct {
	IPS   []net.IP `json:"ips,omitempty"`
	Roles []string `json:"roles,omitempty"`
	// overwrite SSH config
	SSH SSH `json:"ssh,omitempty"`
	// overwrite env
	Env    []string          `json:"env,omitempty"`
	Labels map[string]string `json:"labels,omitempty"`
	Taints []string          `json:"taints,omitempty"`
}

// HostAlias holds the mapping between IP and hostnames that will be injected as an entry in the
// pod's hosts file.
type HostAlias struct {
	// IP address of the host file entry.
	IP string `json:"ip,omitempty"`
	// Hostnames for the above IP address.
	Hostnames []string `json:"hostnames,omitempty"`
}

type Registry struct {
	// LocalRegistry is the kubepilot builtin registry configuration
	LocalRegistry *LocalRegistry `json:"localRegistry,omitempty"`
	// ExternalRegistry used to serve external registry service. do not support yet.
	ExternalRegistry *ExternalRegistry `json:"externalRegistry,omitempty"`
}

type RegistryConfig struct {
	Domain   string `json:"domain,omitempty"`
	Port     int    `json:"port,omitempty"`
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty"`
}

type ExternalRegistry struct {
	RegistryConfig
	RegistryVIP string `json:"registryVIP"`
}

type LocalRegistry struct {
	RegistryConfig
	// HA indicate that whether local registry will be deployed on all master nodes.
	// if LocalRegistry is not specified, default value is true.
	HA *bool `json:"ha,omitempty"`
	// Insecure indicated that whether the local registry is exposed in HTTPS.
	// if true kubepilot will not generate default ssl cert.
	Insecure *bool   `json:"insecure,omitempty"`
	Cert     TLSCert `json:"cert,omitempty"`
}

type TLSCert struct {
	SubjectAltName *SubjectAltName `json:"subjectAltName,omitempty"`
}

type SubjectAltName struct {
	DNSNames []string `json:"dnsNames,omitempty"`
	IPs      []string `json:"ips,omitempty"`
}

type ClusterPhase string

const (
	ClusterFailed  ClusterPhase = "ClusterFailed"
	ClusterSuccess ClusterPhase = "ClusterSuccess"
	ClusterProcess ClusterPhase = "ClusterProcess"
)

// ClusterStatus defines the observed state of Cluster
type ClusterStatus struct {
	Phase      ClusterPhase       `json:"phase,omitempty"`
	Conditions []ClusterCondition `json:"conditions,omitempty"`
}

type ClusterCondition struct {
	Type             ClusterConditionType   `json:"type"`
	Status           corev1.ConditionStatus `json:"status"`
	LastHearbeatTime metav1.Time            `json:"lastHearbeatTime,omitempty"`
	// +optional
	Reason string `json:"reason"`
	// +optional
	Message string `json:"message"`
}

type ClusterConditionType string
type CommandConditionType string

const (
	ClusterInstallSuccess   ClusterConditionType = "ClusterInstallSuccess"
	ClusterInstallFailed    ClusterConditionType = "ClusterInstallFailed"
	ClusterScaleUpSuccess   ClusterConditionType = "ClusterScaleUpSuccess"
	ClusterScaleUpFailed    ClusterConditionType = "ClusterScaleUpFailed"
	ClusterScaleDownSuccess ClusterConditionType = "ClusterScaleDownSuccess"
	ClusterScaleDownFailed  ClusterConditionType = "ClusterScaleDownFailed"
)

// NewSuccessInstallClusterCondition init success type
func NewSuccessInstallClusterCondition() ClusterCondition {
	return ClusterCondition{
		Type:             ClusterInstallSuccess,
		Status:           corev1.ConditionTrue,
		LastHearbeatTime: metav1.Now(),
		Reason:           "Ready",
		Message:          "Cluster install success",
	}
}

// NewFailedInstallClusterCondition init failed type
func NewFailedInstallClusterCondition(msg string) ClusterCondition {
	return ClusterCondition{
		Type:             ClusterInstallFailed,
		Status:           corev1.ConditionFalse,
		LastHearbeatTime: metav1.Now(),
		Reason:           "Failed",
		Message:          msg,
	}
}

// NewSuccessScaleUpClusterCondition init success type
func NewSuccessScaleUpClusterCondition() ClusterCondition {
	return ClusterCondition{
		Type:             ClusterScaleUpSuccess,
		Status:           corev1.ConditionTrue,
		LastHearbeatTime: metav1.Now(),
		Reason:           "Ready",
		Message:          "Cluster scale up success",
	}
}

// NewFailedScaleUpClusterCondition init failed type
func NewFailedScaleUpClusterCondition(msg string) ClusterCondition {
	return ClusterCondition{
		Type:             ClusterScaleUpFailed,
		Status:           corev1.ConditionFalse,
		LastHearbeatTime: metav1.Now(),
		Reason:           "Failed",
		Message:          msg,
	}
}

// NewSuccessScaleDownClusterCondition init success type
func NewSuccessScaleDownClusterCondition() ClusterCondition {
	return ClusterCondition{
		Type:             ClusterScaleDownSuccess,
		Status:           corev1.ConditionTrue,
		LastHearbeatTime: metav1.Now(),
		Reason:           "Ready",
		Message:          "Cluster scale down success",
	}
}

// NewFailedScaleDownClusterCondition init failed type
func NewFailedScaleDownClusterCondition(msg string) ClusterCondition {
	return ClusterCondition{
		Type:             ClusterScaleDownFailed,
		Status:           corev1.ConditionFalse,
		LastHearbeatTime: metav1.Now(),
		Reason:           "Failed",
		Message:          msg,
	}
}

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Cluster is the Schema for the clusters API
type Cluster struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ClusterSpec   `json:"spec,omitempty"`
	Status ClusterStatus `json:"status,omitempty"`
}

// GetTargetsIPList 根据提供的IP列表或角色标签获取目标节点的IP地址列表
func (in *Cluster) GetTargetsIPList(ips []string, roles []string) []net.IP {
	if len(ips) > 0 {
		var targets []net.IP
		for _, ip := range ips {
			targets = append(targets, net.ParseIP(ip))
		}
		return targets
	}
	if len(roles) == 0 {
		return in.GetAllIPList()
	}
	var targets []net.IP
	for i := range roles {
		targets = append(targets, in.GetIPSByRole(roles[i])...)
	}
	return targets
}

func (in *Cluster) GetMasterIPList() []net.IP {
	return in.GetIPSByRole(consts.MASTER)
}

func (in *Cluster) GetMasterIPStrList() (ipStrList []string) {
	ipList := in.GetIPSByRole(consts.MASTER)

	for _, ip := range ipList {
		ipStrList = append(ipStrList, ip.String())
	}

	return ipStrList
}

func (in *Cluster) GetNodeIPList() []net.IP {
	return in.GetIPSByRole(consts.NODE)
}

func (in *Cluster) GetAllIPList() []net.IP {
	return append(in.GetIPSByRole(consts.MASTER), in.GetIPSByRole(consts.NODE)...)
}

func (in *Cluster) GetMaster0IP() net.IP {
	masterIPList := in.GetIPSByRole(consts.MASTER)
	if len(masterIPList) == 0 {
		return nil
	}
	return masterIPList[0]
}

func (in *Cluster) GetIPSByRole(role string) []net.IP {
	var hosts []net.IP
	for _, host := range in.Spec.Hosts {
		for _, hostRole := range host.Roles {
			if role == hostRole {
				hosts = append(hosts, host.IPS...)
				continue
			}
		}
	}
	return hosts
}

func (in *Cluster) GetAnnotationsByKey(key string) string {
	return in.Annotations[key]
}

func (in *Cluster) SetAnnotations(key, value string) {
	if in.Annotations == nil {
		in.Annotations = make(map[string]string)
	}
	in.Annotations[key] = value
}

// +kubebuilder:object:root=true
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterList contains a list of Cluster
type ClusterList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Cluster `json:"items"`
}
