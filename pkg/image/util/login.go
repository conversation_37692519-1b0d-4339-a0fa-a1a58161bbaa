package util

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"k8s.io/klog/v2"

	"github.com/docker/distribution/registry/client/auth"
	"github.com/docker/distribution/registry/client/transport"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/dockerversion"
	dr "github.com/docker/docker/registry"
	"github.com/pkg/errors"
)

func Login(ctx context.Context, authConfig *types.AuthConfig) error {
	domain := authConfig.ServerAddress
	if !strings.HasPrefix(domain, "http://") && !strings.HasPrefix(domain, "https://") {
		domain = "https://" + domain
	}
	endpointURL, err := url.Parse(domain)
	if err != nil {
		return err
	}

	modifiers := dr.Headers(dockerversion.DockerUserAgent(ctx), nil)
	base := dr.<PERSON>ran<PERSON>(nil)
	base.TLSClientConfig.InsecureSkipVerify = os.Getenv("SKIP_TLS_VERIFY") == "true"
	if err := dr.ReadCertsDirectory(base.TLSClientConfig, filepath.Join(dr.CertsDir(), endpointURL.Host)); err != nil {
		return err
	}
	authTransport := transport.NewTransport(base, modifiers...)

	credentialAuthConfig := *authConfig
	creds := loginCredentialStore{
		authConfig: &credentialAuthConfig,
	}
	loginClient, err := authHTTPClient(endpointURL, authTransport, modifiers, creds, nil)
	if err != nil {
		return err
	}

	endpointStr := strings.TrimRight(endpointURL.String(), "/") + "/v2/"
	req, err := http.NewRequest("GET", endpointStr, nil)
	if err != nil {
		return err
	}

	resp, err := loginClient.Do(req)
	if err != nil {
		if strings.Contains(err.Error(), "x509") {
			return fmt.Errorf("%v, if you want to skip TLS verification, set the environment variable 'SKIP_TLS_VERIFY=true' ", err)
		}
		return err
	}
	defer func(Body io.ReadCloser) {
		if err = Body.Close(); err != nil {
			klog.Warningf("failed to close http reader")
		}
	}(resp.Body)

	if resp.StatusCode == http.StatusOK {
		return nil
	}

	// TODO(dmcgowan): Attempt to further interpret result, status code and error code string
	return errors.Errorf("login attempt to %s failed with status: %d %s", endpointStr, resp.StatusCode, http.StatusText(resp.StatusCode))
}

func authHTTPClient(endpoint *url.URL, authTransport http.RoundTripper, modifiers []transport.RequestModifier, creds auth.CredentialStore, scopes []auth.Scope) (*http.Client, error) {
	challengeManager, _, err := dr.PingV2Registry(endpoint, authTransport)
	if err != nil {
		return nil, err
	}

	tokenHandlerOptions := auth.TokenHandlerOptions{
		Transport:     authTransport,
		Credentials:   creds,
		OfflineAccess: true,
		ClientID:      dr.AuthClientID,
		Scopes:        scopes,
	}
	tokenHandler := auth.NewTokenHandlerWithOptions(tokenHandlerOptions)
	basicHandler := auth.NewBasicHandler(creds)
	modifiers = append(modifiers, auth.NewAuthorizer(challengeManager, tokenHandler, basicHandler))
	tr := transport.NewTransport(authTransport, modifiers...)

	return &http.Client{
		Transport: tr,
		Timeout:   15 * time.Second,
	}, nil
}

type loginCredentialStore struct {
	authConfig *types.AuthConfig
}

func (lcs loginCredentialStore) Basic(*url.URL) (string, string) {
	return lcs.authConfig.Username, lcs.authConfig.Password
}

func (lcs loginCredentialStore) RefreshToken(*url.URL, string) string {
	return lcs.authConfig.IdentityToken
}

func (lcs loginCredentialStore) SetRefreshToken(u *url.URL, service, token string) {
	lcs.authConfig.IdentityToken = token
}
