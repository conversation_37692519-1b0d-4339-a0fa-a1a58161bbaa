package util

import (
	"errors"
	"fmt"
	"reflect"
	"testing"
)

func TestParseComponentFromImageName(t *testing.T) {
	// 正常情况测试
	testCases := []struct {
		input       string
		expected    string
		expectedTag string
	}{
		{"gcr.io/g-convoy/hello-world:latest", "hello-world", "latest"},
		{"gcr.io/google.com/g-convoy/hello-world:latest", "hello-world", "latest"},
		{"gcr.io/project-id/with-nums:v2", "with-nums", "v2"},
		{"us.gcr.io/project-id/image:with.period.in.ta", "image", "with.period.in.ta"},
		{"gcr.io/project-id/image:w1th-alpha_num3ric.PLUScaps", "image", "w1th-alpha_num3ric.PLUScaps"},
		{"domain.with.port:9001/image:latest", "image", "latest"},
		{"myrepo/myimage", "myimage", "latest"},
		{"nginx", "nginx", "latest"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			expectedTag := tc.expectedTag
			expectedImage := tc.expected
			actualImage, actualTag, err := ParseComponentFromImageName(tc.input)
			if err != nil {
				if expectedTag == "" {
					t.Errorf("Expected no error for input '%s', but got: %v", tc.input, err)
				} else {
					t.Errorf("Expected error for input '%s', but got: %v", tc.input, err)
				}
			} else {
				if actualImage != expectedImage || actualTag != expectedTag {
					t.Errorf("For input '%s', expected image: '%s', tag: '%s', but got: image: '%s', tag: '%s'",
						tc.input, expectedImage, expectedTag, actualImage, actualTag)
				}
			}
		})
	}

	// 错误情况测试
	testCasesError := []struct {
		input    string
		expected error
	}{
		{"registry.bingosoft.net@/nginx:latest", errors.New("registries must be valid RFC 3986 URI authorities: registry.bingosoft.net@")},
		{"bingokube:bingosoft.net/bingokube/kubepilot:v1.0.0", errors.New("registries must be valid RFC 3986 URI authorities: bingokube:bingosoft.net")},
		{"gcr.io/project-id/bad_chars:c@n'tuse", errors.New("tag can only contain the characters `abcdefghijklmnopqrstuvwxyz0123456789_-.ABCDEFGHIJKLMNOPQRSTUVWXYZ`: c@n'tuse")},
		{"gcr.io/project-id/wrong-length:white space", errors.New("tag can only contain the characters `abcdefghijklmnopqrstuvwxyz0123456789_-.ABCDEFGHIJKLMNOPQRSTUVWXYZ`: white space")},
		{"gcr.io/project-id/too-many-chars:gcr.io/project-id/too-many-chars:thisisthetagthatneverendsitgoesonandonmyfriendsomepeoplestartedtaggingitnotknowingwhatitwasandtheyllcontinuetaggingitforeverjustbecausethisisthetagthatneverends", errors.New("tag must be between 1 and 128 characters in length: thisisthetagthatneverendsitgoesonandonmyfriendsomepeoplestartedtaggingitnotknowingwhatitwasandtheyllcontinuetaggingitforeverjustbecausethisisthetagthatneverends")},
		{"", errors.New("a repository name must be specified")},
		{"/", errors.New("repository must be between 2 and 255 characters in length: /")},
		{"//", errors.New("invalid image name: " + "//")},
	}

	for _, tc := range testCasesError {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			_, _, err := ParseComponentFromImageName(tc.input)
			if err == nil {
				t.Errorf("Expected error for input '%s', but got: no error", tc.input)
			} else {
				if !reflect.DeepEqual(err.Error(), tc.expected.Error()) {
					t.Errorf("For input '%s', expected error: %v, but got: %v", tc.input, tc.expected, err)
				}
			}
		})
	}
}
