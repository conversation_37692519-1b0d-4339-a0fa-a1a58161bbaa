package util

import (
	"fmt"
	"strings"

	registryname "github.com/google/go-containerregistry/pkg/name"
)

// ParseComponentFromImageName 从集群镜像名称中解析出组件名称和组件版本号
// 示例：镜像名称：registry.bingosoft.net/bingokube/cluster-image/cert-manager:v1.0.0, 组件名称cert-manager,版本号v1.0.0
func ParseComponentFromImageName(imageName string) (string, string, error) {
	tag, err := registryname.NewTag(imageName, registryname.WeakValidation)
	if err != nil {
		return "", "", err
	}
	strSlice := strings.Split(tag.RepositoryStr(), "/")
	if len(strSlice) < 1 || len(strSlice[len(strSlice)-1]) == 0 {
		return "", "", fmt.Errorf("invalid image name: %s", imageName)
	}
	return strSlice[len(strSlice)-1], tag.TagStr(), nil
}
