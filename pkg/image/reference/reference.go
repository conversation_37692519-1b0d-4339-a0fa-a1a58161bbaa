package reference

import (
	"errors"
	"strings"
)

type Named struct {
	domain  string // like ***.com, won't be empty
	raw     string // this name is going to be local tag name
	repo    string // k8s, bingokube.bingosoft.net/k8s
	repoTag string // bingokube.bingosoft.net/k8s:v1.22.22
	tag     string // v1.22.22
}

// ParseToNamed build a ImageNamed
func ParseToNamed(name string) (Named, error) {
	name = strings.TrimSpace(name)
	if err := validate(name); err != nil {
		return Named{}, err
	}

	var named Named
	named.raw = buildRaw(name)
	named.domain, named.repoTag = normalizeDomainRepoTag(name)
	named.repo, named.tag = buildRepoAndTag(named.repoTag)
	if strings.ToLower(named.repo) != named.repo {
		return named, errors.New("uppercase is not allowed in image name")
	}
	return named, nil
}

func (n Named) String() string {
	return n.Name()
}

func (n Named) Name() string {
	if n.domain == "" {
		return n.Repo()
	}
	return n.domain + "/" + n.Repo()
}

func (n Named) Domain() string {
	return n.domain
}

func (n Named) RepoTag() string {
	return n.repoTag
}

func (n Named) Raw() string {
	return n.raw
}

func (n Named) Repo() string {
	return n.repo
}

func (n Named) Tag() string {
	return n.tag
}

func (n Named) CompleteName() string {
	return n.domain + "/" + n.repoTag
}
