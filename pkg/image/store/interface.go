package store

import (
	"context"

	"github.com/docker/docker/pkg/progress"
	apiv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
)

// ImageStore It is not the same as docker save image,
// only used to parse the image int cluster image and save tmp `registry`
type ImageStore interface {
	StoreImages(imgs []string, dir string, platform apiv1beta1.Platform) error
}

type Section struct {
	Registry string             `json:"registry,omitempty"`
	Username string             `json:"username,omitempty"`
	Password string             `json:"password,omitempty"`
	Images   map[string][]Named `json:"images,omitempty"`
}

type ImageListWithAuth []Section

type DefaultImageSaver struct {
	ctx            context.Context
	domainToImages map[string][]Named
	progressOut    progress.Output
}

func NewImageSaver(ctx context.Context) ImageStore {
	if ctx == nil {
		ctx = context.Background()
	}
	return &DefaultImageSaver{
		ctx:            ctx,
		domainToImages: make(map[string][]Named),
	}
}
