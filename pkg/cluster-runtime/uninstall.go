package clusterruntime

import (
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	containerruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/container-runtime"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/plugin"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry"
)

func (i *Installer) UnInstall() error {
	masters := i.infraDriver.GetHostIPListByRole(consts.MASTER)
	master0 := masters[0]
	workers := getWorkerIPList(i.infraDriver)
	all := append(masters, workers...)

	_, err := SshCheck(i.infraDriver, all)
	if err != nil {
		return err
	}

	if err = i.handler.RunOnMaster0(master0, plugin.PreUnInstallCluster); err != nil {
		return err
	}

	if err = i.handler.RunOnHosts(plugin.PreCleanHost, all); err != nil {
		return err
	}

	kubeRuntimeInstaller, err := getClusterRuntimeInstaller(i.clusterRuntimeType, i.infraDriver,
		containerruntime.RuntimeOpts{}, registry.Info{}, i.KubeadmConfig)
	if err != nil {
		return err
	}

	if err = kubeRuntimeInstaller.Reset(); err != nil {
		return err
	}

	crInfo, err := i.containerRuntimeInstaller.GetInfo()
	if err != nil {
		return err
	}

	registryConfigurator, err := registry.NewConfigurator(nil, crInfo, i.regConfig, i.infraDriver, i.Distributor)
	if err != nil {
		return err
	}

	if err = registryConfigurator.UninstallFrom(masters, workers); err != nil {
		return err
	}

	if err = i.containerRuntimeInstaller.UnInstallFrom(all); err != nil {
		return err
	}

	if err = i.handler.RunOnHosts(plugin.PostCleanHost, all); err != nil {
		return err
	}

	if err = i.handler.RunOnMaster0(master0, plugin.PostUnInstallCluster); err != nil {
		return err
	}

	// delete HostAlias
	if err = i.infraDriver.DeleteClusterHostAliases(all); err != nil {
		return err
	}

	// 删除master节点的vip
	// 只有当 EnvEnableKubealived 不为 "false" 时才执行删除 VIP 的操作
	if vip, ok := i.infraDriver.GetClusterEnv()[constants.EnvIPvsVIPForIPv4]; ok {
		// 只有在启用了 Kubealived 的情况下才删除 VIP
		if enabled, _ := i.infraDriver.GetClusterEnv()[constants.EnvEnableKubealived]; enabled != "false" {
			if err = i.infraDriver.RemoveVIP(masters, vip); err != nil {
				return err
			}
		}
	}

	return i.Distributor.Restore(i.infraDriver.GetClusterBasePath(), all)
}
