# Copyright © 2022 Alibaba Group Holding Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

---
apiVersion: bingokube.bingosoft.net/v1beta1
kind: Plugin
metadata:
  name: pre-init-host2
spec:
  type: SHELL
  action: pre-init-host
  scope: master
  data: |
    echo "i am pre-init-host2 from rootfs"

---
apiVersion: bingokube.bingosoft.net/v1beta1
kind: Plugin
metadata:
  name: post-init-host2
spec:
  type: SHELL
  action: post-init-host
  scope: master
  data: |
    echo "i am post-init-host2 from rootfs"
