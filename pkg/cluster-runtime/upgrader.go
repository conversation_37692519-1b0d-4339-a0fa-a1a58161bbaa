package clusterruntime

import (
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/plugin"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry"
)

func (i *Installer) Upgrade() error {
	var (
		masters = i.infraDriver.GetHostIPListByRole(consts.MASTER)
		master0 = masters[0]
		workers = getWorkerIPList(i.infraDriver)
		all     = append(masters, workers...)
		rootfs  = i.infraDriver.GetClusterRootfsPath()
	)

	// distribute rootfs
	if err := i.Distributor.Distribute(all, rootfs); err != nil {
		return err
	}

	if err := i.handler.RunOnHosts(plugin.UpgradeHost, all); err != nil {
		return err
	}

	crInfo, err := i.containerRuntimeInstaller.GetInfo()
	if err != nil {
		return err
	}

	registryConfigurator, err := registry.NewConfigurator(masters, crInfo, i.regConfig, i.infraDriver, i.Distributor)
	if err != nil {
		return err
	}

	if err = registryConfigurator.InstallOn(masters, workers); err != nil {
		return err
	}

	return i.handler.RunOnMaster0(master0, plugin.UpgradeCluster)
}
