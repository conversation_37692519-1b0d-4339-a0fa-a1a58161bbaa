package clusterruntime

import (
	"fmt"
	"net"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	clustermanager "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/nodeconfig"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/plugin"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"k8s.io/klog/v2"
)

func (i *Installer) ScaleUp(newMasters, newWorkers []net.IP) (registry.Driver, clustermanager.Driver, error) {
	var (
		masters = i.infraDriver.GetHostIPListByRole(consts.MASTER)
		master0 = masters[0]
		// workers             = getWorkerIPList(i.infraDriver)
		all    = append(newMasters, newWorkers...)
		rootfs = i.infraDriver.GetClusterRootfsPath()
	)

	klog.V(5).Infof("check ssh client for all nodes to be added [%s]", all)
	if _, err := SshCheck(i.infraDriver, all); err != nil {
		return nil, nil, err
	}

	// 设置host alias
	if err := i.infraDriver.SetClusterHostAliases(all); err != nil {
		return nil, nil, err
	}

	if err := i.Distributor.Distribute(all, rootfs); err != nil {
		return nil, nil, err
	}
	klog.V(5).Infof("success distribute rootfs into nodes: %s", all)

	if err := i.handler.RunOnMaster0(master0, plugin.PreScaleUpCluster); err != nil {
		return nil, nil, err
	}

	if err := i.handler.RunOnHosts(plugin.PreInitHost, all); err != nil {
		return nil, nil, err
	}
	// 安装cri
	if err := i.containerRuntimeInstaller.InstallOn(all); err != nil {
		return nil, nil, err
	}
	klog.V(6).Info("success install container runtime")

	crInfo, err := i.containerRuntimeInstaller.GetInfo()
	if err != nil {
		return nil, nil, err
	}

	registryConfigurator, err := registry.NewConfigurator(unet.RemoveIPs(masters, newMasters), crInfo, i.regConfig, i.infraDriver, i.Distributor)
	if err != nil {
		return nil, nil, err
	}

	if err = registryConfigurator.InstallOn(newMasters, newWorkers); err != nil {
		return nil, nil, err
	}

	registryDriver, err := registryConfigurator.GetDriver()
	if err != nil {
		return nil, nil, err
	}

	kubeRuntimeInstaller, err := getClusterRuntimeInstaller(i.clusterRuntimeType, i.infraDriver, crInfo, registryDriver.GetInfo(), i.KubeadmConfig)
	if err != nil {
		return nil, nil, err
	}

	if err = kubeRuntimeInstaller.ScaleUp(newMasters, newWorkers); err != nil {
		return nil, nil, err
	}

	if err = i.handler.RunOnHosts(plugin.PostInitHost, all); err != nil {
		return nil, nil, err
	}

	if err = i.handler.RunOnMaster0(master0, plugin.PostScaleUpCluster); err != nil {
		return nil, nil, err
	}

	runtimeDriver, err := kubeRuntimeInstaller.GetCurrentRuntimeDriver()
	if err != nil {
		return nil, nil, err
	}

	if err = i.setRoles(runtimeDriver); err != nil {
		return nil, nil, err
	}

	if err = i.setNodeLabels(all, runtimeDriver); err != nil {
		return nil, nil, err
	}

	if err = i.setNodeTaints(all, runtimeDriver); err != nil {
		return nil, nil, err
	}

	nodesInfoList, err := GetNodesInfoList(i.infraDriver, all, nodeconfig.AddNode)
	if err != nil {
		return nil, nil, err
	}

	// 将node信息，如ManagementNetworkIP、ManagementNetworkInterface等信息更新到 configmap（kubepilot-nodeconfig）中
	if err = nodeconfig.ManageNodeConfigmapFuncMap[nodeconfig.AddNode](nodesInfoList); err != nil {
		return nil, nil, err
	}

	return registryDriver, runtimeDriver, nil
}

func (i *Installer) ScaleDown(mastersToDelete, workersToDelete []net.IP) (registry.Driver, clustermanager.Driver, error) {
	masters := i.infraDriver.GetHostIPListByRole(consts.MASTER)
	workers := getWorkerIPList(i.infraDriver)
	remainWorkers := unet.RemoveIPs(workers, workersToDelete)

	crInfo, err := i.containerRuntimeInstaller.GetInfo()
	if err != nil {
		return nil, nil, err
	}

	registryConfigurator, err := registry.NewConfigurator(unet.RemoveIPs(masters, mastersToDelete), crInfo, i.regConfig, i.infraDriver, i.Distributor)
	if err != nil {
		return nil, nil, err
	}

	registryDriver, err := registryConfigurator.GetDriver()
	if err != nil {
		return nil, nil, err
	}

	kubeRuntimeInstaller, err := getClusterRuntimeInstaller(i.clusterRuntimeType, i.infraDriver,
		crInfo, registryDriver.GetInfo(), i.KubeadmConfig)
	if err != nil {
		return nil, nil, err
	}

	runtimeDriver, err := kubeRuntimeInstaller.GetCurrentRuntimeDriver()
	if err != nil {
		return nil, nil, err
	}

	if len(mastersToDelete) != 0 {
		klog.V(5).Infof("check ssh of remainWorkers")
		if _, err = SshCheck(i.infraDriver, remainWorkers); err != nil {
			return nil, nil, fmt.Errorf("because master list changed, we need connect to all existing workers to maintain some configs, but failed: %v", err)
		}
	}

	klog.V(5).Infof("check ssh of nodesToDelete")
	disconnetedMasters, err := SshCheck(i.infraDriver, mastersToDelete)
	if err != nil {
		klog.Warning(err.Error())
		return nil, nil, err
	}
	disconnetedWorkers, err := SshCheck(i.infraDriver, workersToDelete)
	if err != nil {
		klog.Warning(err.Error())
		return nil, nil, err
	}

	if err = i.resetAndScaleDown(kubeRuntimeInstaller, registryConfigurator, unet.RemoveIPs(mastersToDelete, disconnetedMasters), unet.RemoveIPs(workersToDelete, disconnetedWorkers)); err != nil {
		return nil, nil, err
	}

	if err = i.onlyScaleDown(kubeRuntimeInstaller, disconnetedMasters, disconnetedWorkers); err != nil {
		return nil, nil, err
	}

	return registryDriver, runtimeDriver, nil
}

func (i *Installer) resetAndScaleDown(kubeRuntimeInstaller clustermanager.Installer, registryConfigurator registry.Configurator, mastersToDelete, workersToDelete []net.IP) error {
	allToDelete := append(mastersToDelete, workersToDelete...)

	if err := i.handler.RunOnHosts(plugin.PreCleanHost, allToDelete); err != nil {
		return err
	}

	if err := kubeRuntimeInstaller.ScaleDown(mastersToDelete, workersToDelete); err != nil {
		return err
	}

	if err := registryConfigurator.UninstallFrom(mastersToDelete, workersToDelete); err != nil {
		return err
	}

	if err := i.containerRuntimeInstaller.UnInstallFrom(allToDelete); err != nil {
		return err
	}

	if err := i.handler.RunOnHosts(plugin.PostCleanHost, allToDelete); err != nil {
		return err
	}

	// delete HostAlias
	if err := i.infraDriver.DeleteClusterHostAliases(allToDelete); err != nil {
		return err
	}

	if err := i.Distributor.Restore(i.infraDriver.GetClusterBasePath(), allToDelete); err != nil {
		return err
	}

	return nil
}

func (i *Installer) onlyScaleDown(kubeRuntimeInstaller clustermanager.Installer, mastersToDelete, workersToDelete []net.IP) error {
	return kubeRuntimeInstaller.ScaleDown(mastersToDelete, workersToDelete)
}
