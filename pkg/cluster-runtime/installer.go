package clusterruntime

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	pgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/plugin/v1beta1"
	clustermanager "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/kubeadm"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/nodeconfig"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	containerruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/container-runtime"
	imagedistributor "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-distributor"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver/sshdriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/plugin"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
	runtimeClient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/kustomize/kyaml/yaml"
)

const (
	// CRILabel is key for get container runtime interface type.
	CRILabel = "bingokube.bingosoft.net/container-runtime-type" // 通过Rootfs的Label选择containerd或者docker
	// CRTLabel is key for get cluster runtime type.
	CRTLabel = "bingokube.bingosoft.net/cluster-runtime-type" // 通过Rootfs的Label选择Kubernetes集群或者k3s集群作为实现

	RegistryConfigMapName     = "bingokube-registry"
	RegistryConfigMapDataName = "registry"
)

// RuntimeConfig for Installer
type RuntimeConfig struct {
	Distributor            imagedistributor.Distributor
	ContainerRuntimeConfig ctv1beta1.ContainerRuntimeConfig
	KubeadmConfig          kubeadm.KubeadmConfig
	Plugins                []pgv1beta1.Plugin
}

type Installer struct {
	RuntimeConfig
	handler                   plugin.Handler
	infraDriver               infradriver.InfraDriver
	containerRuntimeInstaller containerruntime.Installer
	clusterRuntimeType        string
	// hooks                     map[plugin.Phase]plugin.HookConfigList
	regConfig ctv1beta1.Registry
}

type InstallInfo struct {
	ContainerRuntimeType string
	ClusterRuntimeType   string
}

func getCRIInstaller(containerRuntime string, infraDriver infradriver.InfraDriver) (containerruntime.Installer, error) {
	switch containerRuntime {
	case constants.Docker:
		return containerruntime.NewInstaller(ctv1beta1.ContainerRuntimeConfig{
			Type: constants.Docker,
		}, infraDriver)
	case constants.Containerd:
		return containerruntime.NewInstaller(ctv1beta1.ContainerRuntimeConfig{
			Type: constants.Containerd,
		}, infraDriver)
	default:
		return nil, fmt.Errorf("not support container runtime %s", containerRuntime)
	}
}

func getClusterRuntimeInstaller(clusterRuntimeType string, driver infradriver.InfraDriver, crInfo containerruntime.RuntimeOpts,
	registryInfo registry.Info, kubeadmConfig kubeadm.KubeadmConfig) (clustermanager.Installer, error) {
	switch clusterRuntimeType {
	case constants.K8s:
		return k8s.NewKubeadmRuntime(kubeadmConfig, driver, crInfo, registryInfo)
		// Todo support k3s/k0s runtime
	default:
		return nil, fmt.Errorf("not support cluster runtime %s", clusterRuntimeType)
	}
}

func NewInstaller(infraDriver infradriver.InfraDriver, runtimeConfig RuntimeConfig, installInfo InstallInfo) (*Installer, error) {
	var (
		err       error
		installer = &Installer{
			regConfig:          infraDriver.GetClusterRegistry(),
			clusterRuntimeType: installInfo.ClusterRuntimeType,
		}
	)

	installer.RuntimeConfig = runtimeConfig
	// configure container runtime
	// Todo: need to support other container runtimes
	installer.containerRuntimeInstaller, err = getCRIInstaller(installInfo.ContainerRuntimeType, infraDriver)
	if err != nil {
		return nil, err
	}

	installer.infraDriver = infraDriver
	// 初始化加载所有插件
	installer.handler, err = plugin.NewHookOptions(runtimeConfig.Plugins, infraDriver)
	if err != nil {
		return nil, err
	}

	return installer, nil
}

func (i *Installer) Install() error {
	var (
		masters = i.infraDriver.GetHostIPListByRole(consts.MASTER)
		master0 = masters[0]
		workers = getWorkerIPList(i.infraDriver)
		all     = append(masters, workers...)
	)

	// 给所有节点设置别名 set HostAlias
	if err := i.infraDriver.SetClusterHostAliases(all); err != nil {
		return err
	}

	if err := i.Distributor.DistributeRegistry(masters, i.infraDriver.GetRegistryDataDir()); err != nil {
		return err
	}
	klog.V(5).Infof("success distribute registry into nodes: %s", masters)

	// 往所有的节点 distribute rootfs
	if err := i.Distributor.Distribute(all, i.infraDriver.GetClusterRootfsPath()); err != nil {
		return err
	}
	klog.V(5).Infof("success distribute rootfs into nodes: %s", all)

	// 在master0节点上执行勾子`PreInstallCluster`
	if err := i.handler.RunOnMaster0(master0, plugin.PreInstallCluster); err != nil {
		return err
	}

	if err := i.handler.RunOnHosts(plugin.PreInitHost, all); err != nil {
		return err
	}

	if err := i.containerRuntimeInstaller.InstallOn(all); err != nil {
		return err
	}
	klog.V(6).Info("success install container runtime")

	crInfo, err := i.containerRuntimeInstaller.GetInfo()
	if err != nil {
		return err
	}

	// 实例化registry
	registryConfigurator, err := registry.NewConfigurator(all, crInfo, i.regConfig, i.infraDriver, i.Distributor)
	if err != nil {
		return err
	}

	// 在每个指定的主机上安装registry配置
	if err = registryConfigurator.InstallOn(masters, workers); err != nil {
		return err
	}

	registryDriver, err := registryConfigurator.GetDriver()
	if err != nil {
		return err
	}

	registryInfo := registryConfigurator.GetRegistryInfo()

	kubeRuntimeInstaller, err := getClusterRuntimeInstaller(i.clusterRuntimeType, i.infraDriver,
		crInfo, registryDriver.GetInfo(), i.KubeadmConfig)
	if err != nil {
		return err
	}

	if err = kubeRuntimeInstaller.Install(); err != nil {
		return err
	}

	if err = i.handler.RunOnHosts(plugin.PostInitHost, all); err != nil {
		return err
	}

	if err = i.handler.RunOnMaster0(master0, plugin.PostInstallCluster); err != nil {
		return err
	}

	runtimeDriver, err := kubeRuntimeInstaller.GetCurrentRuntimeDriver()
	if err != nil {
		return err
	}

	if err = i.setRoles(runtimeDriver); err != nil {
		return err
	}

	if err = i.setNodeLabels(all, runtimeDriver); err != nil {
		return err
	}

	if err = i.setNodeTaints(all, runtimeDriver); err != nil {
		return err
	}

	if err = i.saveRegistryInfo(runtimeDriver, registryInfo); err != nil {
		return err
	}

	nodesInfoList, err := GetNodesInfoList(i.infraDriver, all, nodeconfig.CreateCluster)
	if err != nil {
		return err
	}

	// 将node信息，如ManagementNetworkIP、ManagementNetworkInterface等信息写到 configmap（kubepilot-nodeconfig）中
	addConfigMapFunc, ok := nodeconfig.ManageNodeConfigmapFuncMap[nodeconfig.CreateCluster]
	if !ok {
		return fmt.Errorf("%s configmap function not exist", nodeconfig.CreateCluster)
	}
	return addConfigMapFunc(nodesInfoList)
}

func (i *Installer) GetCurrentDriver() (registry.Driver, clustermanager.Driver, error) {
	var (
		masters             = i.infraDriver.GetHostIPListByRole(consts.MASTER)
		master0             = masters[0]
		registryDeployHosts = masters
	)
	crInfo, err := i.containerRuntimeInstaller.GetInfo()
	if err != nil {
		return nil, nil, err
	}

	if i.regConfig.LocalRegistry != nil && !*i.regConfig.LocalRegistry.HA {
		registryDeployHosts = []net.IP{master0}
	}
	// TODO, init here or in constructor?
	registryConfigurator, err := registry.NewConfigurator(registryDeployHosts, crInfo, i.regConfig, i.infraDriver, i.Distributor)
	if err != nil {
		return nil, nil, err
	}

	registryDriver, err := registryConfigurator.GetDriver()
	if err != nil {
		return nil, nil, err
	}

	kubeRuntimeInstaller, err := k8s.NewKubeadmRuntime(i.KubeadmConfig, i.infraDriver, crInfo, registryDriver.GetInfo())
	if err != nil {
		return nil, nil, err
	}

	runtimeDriver, err := kubeRuntimeInstaller.GetCurrentRuntimeDriver()
	if err != nil {
		return nil, nil, err
	}

	return registryDriver, runtimeDriver, nil
}

// setRoles 为所有节点设置角色
func (i *Installer) setRoles(driver clustermanager.Driver) error {
	nodeList := corev1.NodeList{}
	if err := driver.List(context.TODO(), &nodeList); err != nil {
		return err
	}

	genRoleLabelFunc := func(role string) string {
		return fmt.Sprintf("node-role.kubernetes.io/%s", role)
	}

	for idx, node := range nodeList.Items {
		addresses := node.Status.Addresses
		for _, address := range addresses {
			if address.Type != "InternalIP" {
				continue
			}
			roles := i.infraDriver.GetRoleListByHostIP(address.Address)
			if len(roles) == 0 {
				continue
			}
			newNode := node.DeepCopy()

			for _, role := range roles {
				newNode.Labels[genRoleLabelFunc(role)] = ""
			}
			patch := runtimeClient.MergeFrom(&nodeList.Items[idx])

			if err := driver.Patch(context.TODO(), newNode, patch); err != nil {
				return err
			}
		}
	}

	return nil
}

// setNodeLabels 为指定节点打上Label
func (i *Installer) setNodeLabels(hosts []net.IP, driver clustermanager.Driver) error {
	// set new added host labels if it is existed
	nodeList := corev1.NodeList{}
	if err := driver.List(context.TODO(), &nodeList); err != nil {
		return fmt.Errorf("failed to list cluster nodes: %v", err)
	}

	nodeLabel := make(map[string]corev1.Node)
	for _, node := range nodeList.Items {
		nodeLabel[getAddress(node.Status.Addresses)] = node
	}

	for _, ip := range hosts {
		labels := i.infraDriver.GetHostLabels(ip)
		if len(labels) == 0 {
			continue
		}

		if node, ok := nodeLabel[ip.String()]; ok {
			newNode := node.DeepCopy()
			m := node.GetLabels()
			for key, value := range labels {
				m[key] = value
			}

			newNode.SetLabels(m)
			newNode.SetResourceVersion("")
			if err := driver.Update(context.TODO(), newNode); err != nil {
				return fmt.Errorf("failed to label cluster nodes %s: %v", ip.String(), err)
			}
		}
	}

	return nil
}

func getAddress(addresses []corev1.NodeAddress) string {
	for _, v := range addresses {
		if strings.EqualFold(string(v.Type), "InternalIP") {
			return v.Address
		}
	}
	return ""
}

// setNodeTaints 为指定节点打污点
func (i *Installer) setNodeTaints(hosts []net.IP, driver clustermanager.Driver) error {
	var (
		k8snode    corev1.Node
		ok         bool
		nodeTaints []corev1.Taint
	)
	nodeList := corev1.NodeList{}
	if err := driver.List(context.TODO(), &nodeList); err != nil {
		return fmt.Errorf("failed to list cluster nodes: %v", err)
	}
	nodeTaint := make(map[string]corev1.Node)
	for _, node := range nodeList.Items {
		nodeTaint[getAddress(node.Status.Addresses)] = node
	}

	for _, ip := range hosts {
		taints := i.infraDriver.GetHostTaints(ip)
		if len(taints) == 0 {
			continue
		}

		if k8snode, ok = nodeTaint[ip.String()]; !ok {
			continue
		}
		newNode := k8snode.DeepCopy()
		for _, taint := range taints {
			if strings.Contains(taint.Key, sshdriver.Del) {
				taintKey := strings.TrimSuffix(taint.Key, sshdriver.Del)
				nodeTaints, _ = sshdriver.DeleteTaintsByKey(newNode.Spec.Taints, taintKey)
				newNode.Spec.Taints = nodeTaints
			} else if strings.Contains(string(taint.Effect), sshdriver.Del) {
				nodeTaints, _ = sshdriver.DeleteTaint(newNode.Spec.Taints, &taint) // #nosec
				newNode.Spec.Taints = nodeTaints
			} else {
				newNode.Spec.Taints = taints
			}
		}
		newNode.SetResourceVersion("")

		f := func() (bool, error) {
			if err := driver.Update(context.TODO(), newNode); err != nil {
				return false, nil
			}

			return true, nil
		}
		return wait.PollImmediate(1*time.Second, 10*time.Second, f)
	}

	return nil
}

// saveRegistryInfo 保存registry相关信息到configmap
func (i *Installer) saveRegistryInfo(driver clustermanager.Driver, registryInfo registry.Options) error {
	info, err := yaml.Marshal(registryInfo)
	if err != nil {
		return err
	}

	cm := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      RegistryConfigMapName,
			Namespace: constants.ClusterfileConfigMapNamespace,
		},
		Data: map[string]string{RegistryConfigMapDataName: string(info)},
	}

	ctx := context.Background()
	if err = driver.Create(ctx, cm); err != nil {
		if !apierrors.IsAlreadyExists(err) {
			return fmt.Errorf("unable to create configmap: %v", err)
		}

		if err = driver.Update(ctx, cm); err != nil {
			return fmt.Errorf("unable to update configmap: %v", err)
		}
	}
	return nil
}

func GetClusterInstallInfo(imageLabels map[string]string, criConfig ctv1beta1.ContainerRuntimeConfig) InstallInfo {
	cri := imageLabels[CRILabel]
	if cri == "" {
		// 默认选择containerd
		cri = constants.Containerd
	}
	if criConfig.Type != "" {
		cri = criConfig.Type
	}
	clusterRuntimeType := imageLabels[CRTLabel]
	if clusterRuntimeType == "" {
		clusterRuntimeType = constants.K8s
	}
	klog.Infof("The cri is [%s], cluster runtime type is [%s]", cri, clusterRuntimeType)
	return InstallInfo{
		ContainerRuntimeType: cri,
		ClusterRuntimeType:   clusterRuntimeType,
	}
}

func GetClusterConfPath(labels map[string]string) string {
	clusterRuntimeType := labels[CRTLabel]
	if clusterRuntimeType == "" {
		clusterRuntimeType = constants.K8s
	}
	switch clusterRuntimeType {
	case constants.K8s:
		return k8s.AdminKubeConfPath
	// TODO support k0s/k3s...
	default:
		return k8s.AdminKubeConfPath
	}
}
