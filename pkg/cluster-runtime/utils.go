package clusterruntime

import (
	"fmt"
	"net"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/nodeconfig"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/env"
	imagedistributor "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-distributor"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"k8s.io/klog/v2"
)

func getWorkerIPList(infraDriver infradriver.InfraDriver) []net.IP {
	masters := make(map[string]bool)
	for _, master := range infraDriver.GetHostIPListByRole(consts.MASTER) {
		masters[master.String()] = true
	}
	all := infraDriver.GetHostIPList()
	workers := make([]net.IP, len(all)-len(masters))

	index := 0
	for _, ip := range all {
		if !masters[ip.String()] {
			workers[index] = ip
			index++
		}
	}

	return workers
}

// LoadToRegistry just load container image to local registry
func LoadToRegistry(infraDriver infradriver.InfraDriver, distributor imagedistributor.Distributor) error {
	regConfig := infraDriver.GetClusterRegistry()
	// todo only support load image to local registry at present
	if regConfig.LocalRegistry == nil {
		return nil
	}

	deployHosts := infraDriver.GetHostIPListByRole(consts.MASTER)
	if len(deployHosts) < 1 {
		return fmt.Errorf("local registry host can not be nil")
	}
	master0 := deployHosts[0]

	klog.V(5).Infof("start to apply with mode(%s)", constants.LoadImage)
	if !*regConfig.LocalRegistry.HA {
		deployHosts = []net.IP{master0}
	}

	if err := distributor.DistributeRegistry(deployHosts, infraDriver.GetRegistryDataDir()); err != nil {
		return err
	}

	klog.V(5).Infof("load image success")
	return nil
}

// SshCheck 检查节点是否能ssh通
func SshCheck(infraDriver infradriver.InfraDriver, clientHosts []net.IP) ([]net.IP, error) {
	var failedNodes []net.IP
	for i := range clientHosts {
		n := clientHosts[i]
		klog.V(5).Infof("checking ssh client for [%s] ", n.String())
		if err := infraDriver.CmdAsync(n, nil, "ls >> /dev/null"); err != nil {
			failedNodes = append(failedNodes, n)
			klog.Errorf("failed to connect node %s: %v", n.String(), err)
		}
	}
	var retErr error
	if len(failedNodes) > 0 {
		retErr = fmt.Errorf("failed to connect node: %v, maybe you have change its sshpasswd, if so, please correct passwd via cmd (kubectl -n kube-system edit cm kubepilot-clusterfile) or check other errors by yourself", failedNodes)
	}

	return failedNodes, retErr
}

// GetNodesInfoList 返回一个包含节点信息的列表
// 每个节点的信息以 map[string]string 的形式存储
func GetNodesInfoList(infraDriver infradriver.InfraDriver, nodes []net.IP, operationType string) ([]map[string]string, error) {
	var (
		nodesInfoList         []map[string]string
		filteredNodesInfoList []map[string]string
		nodeInfo              map[string]string
		err                   error
	)

	switch operationType {
	case nodeconfig.DeleteNode:
		return ConvertIPSliceToMapSlice(nodes), nil
	case nodeconfig.AddNode:
		// 将每个节点的管理网卡信息添加到hostEnv，用于生成kubealived静态pod yaml文件时所需
		if info, ok := infraDriver.GetHostEnv(nodes[0])[constants.NodesInfo]; ok {
			nodesInfoList, err = env.ConvertJsonStrToMap(info)
			if err != nil {
				return nil, fmt.Errorf("failed to convert node info to map,err: %w", err)
			}
		}

		// 将 nodes 转换为 map 以便于快速查找
		nodesMap := make(map[string]struct{})
		for _, ip := range nodes {
			nodesMap[ip.String()] = struct{}{}
		}

		// 遍历 nodesInfoList
		for _, nodeInfo = range nodesInfoList {
			if managerIP, exists := nodeInfo[constants.ManagementNetworkIP]; exists {
				// 检查 managerIP 是否在 nodeips 中
				if _, found := nodesMap[managerIP]; found {
					// 如果存在，则保留
					filteredNodesInfoList = append(filteredNodesInfoList, nodeInfo)
				}
			}
		}
		return filteredNodesInfoList, nil
	case nodeconfig.CreateCluster:
		if info, ok := infraDriver.GetClusterEnv()[constants.NodesInfo]; ok {
			nodesInfoList, err = env.ConvertJsonStrToMap(info)
			if err != nil {
				return nil, fmt.Errorf("failed to convert node info to map,err: %w", err)
			}
		}
		return nodesInfoList, nil
	default:
		return nil, fmt.Errorf("unsupported operation type")
	}
}

// ConvertIPSliceToMapSlice 将 net.IP 切片转换为 map[string]string 切片
func ConvertIPSliceToMapSlice(ips []net.IP) []map[string]string {
	result := make([]map[string]string, len(ips))

	for i, ip := range ips {
		result[i] = map[string]string{
			constants.ManagementNetworkIP: ip.String(), // 将 net.IP 转换为字符串
		}
	}

	return result
}
