package clusterfile

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"reflect"
	"strconv"
	"strings"

	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	confv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/config/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	imgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	pgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/plugin/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/client/k8s"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/commands"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	entv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/interaction"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/maps"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/strs"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/klog/v2"
	"k8s.io/kube-proxy/config/v1alpha1"
	"k8s.io/kubelet/config/v1beta1"
	"k8s.io/kubernetes/cmd/kubeadm/app/apis/kubeadm/v1beta3"
	kubeadmConstants "k8s.io/kubernetes/cmd/kubeadm/app/constants"
)

var (
	ErrCancelled = errors.New("cancel upgrade")
)

func GetClusterFromFile(filepath string) (cluster *ctv1beta1.Cluster, err error) {
	cluster = &ctv1beta1.Cluster{}
	if err = file.UnmarshalFile(filepath, cluster); err != nil {
		return nil, fmt.Errorf("failed to get cluster from %s: %v", filepath, err)
	}
	cluster.SetAnnotations(constants.ClusterfileName, filepath)
	return cluster, nil
}

func decodeClusterfile(reader io.Reader, clusterfile *clusterfile) error {
	decoder := yaml.NewYAMLToJSONDecoder(bufio.NewReaderSize(reader, 4096))

	for {
		ext := runtime.RawExtension{}
		if err := decoder.Decode(&ext); err != nil {
			if err == io.EOF {
				return nil
			}
			return err
		}

		ext.Raw = bytes.TrimSpace(ext.Raw)
		if len(ext.Raw) == 0 || bytes.Equal(ext.Raw, []byte("null")) {
			continue
		}
		metaType := metav1.TypeMeta{}
		if err := yaml.Unmarshal(ext.Raw, &metaType); err != nil {
			return fmt.Errorf("failed to decodeClusterfile TypeMeta: %v", err)
		}

		switch metaType.Kind {
		case constants.ClusterKind:
			var cluster ctv1beta1.Cluster

			if err := yaml.Unmarshal(ext.Raw, &cluster); err != nil {
				return fmt.Errorf("failed to decodeClusterfile %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
			}
			// 初始化
			if err := checkAndSetCluster(&cluster); err != nil {
				return fmt.Errorf("failed to check and complete cluster: %v", err)
			}

			clusterfile.clusterInfo = &cluster
		case constants.ConfigKind:
			var cfg confv1beta1.Config

			if err := yaml.Unmarshal(ext.Raw, &cfg); err != nil {
				return fmt.Errorf("failed to decodeClusterfile %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
			}

			if cfg.Spec.Path == "" {
				return fmt.Errorf("failed to decodeClusterfile config %s, config path is empty", cfg.Name)
			}

			if cfg.Spec.Data == "" {
				return fmt.Errorf("failed to decodeClusterfile config %s, config data is empty", cfg.Name)
			}

			clusterfile.configs = append(clusterfile.configs, cfg)
		case constants.PluginKind:
			var plu pgv1beta1.Plugin

			if err := yaml.Unmarshal(ext.Raw, &plu); err != nil {
				return fmt.Errorf("failed to decodeClusterfile %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
			}

			clusterfile.plugins = append(clusterfile.plugins, plu)
		case constants.ApplicationKind:
			var app appv1beta1.Application

			if err := yaml.Unmarshal(ext.Raw, &app); err != nil {
				return fmt.Errorf("failed to decodeClusterfile %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
			}

			for _, config := range app.Spec.Configs {
				if config.Name == "" {
					return errors.New("the name of application config is empty")
				}

				if config.Launch != nil {
					launchCmds := parseLaunchCmds(config.Launch)
					if launchCmds == nil {
						return errors.New("failed to get launchCmds from application configs")
					}
				}

				for _, appFile := range config.Files {
					if appFile.Data == "" {
						return fmt.Errorf("failed to decodeClusterfile application config %s. data is empty", config.Name)
					}

					if appFile.Path == "" {
						return fmt.Errorf("failed to decodeClusterfile application config %s. path is empty", config.Name)
					}
				}
			}

			clusterfile.app = append(clusterfile.app, &app)
		case kubeadmConstants.InitConfigurationKind:
			var in v1beta3.InitConfiguration

			if err := yaml.Unmarshal(ext.Raw, &in); err != nil {
				return fmt.Errorf("failed to decodeClusterfile %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
			}

			clusterfile.kc.InitConfiguration = in
		case kubeadmConstants.JoinConfigurationKind:
			var in v1beta3.JoinConfiguration

			if err := yaml.Unmarshal(ext.Raw, &in); err != nil {
				return fmt.Errorf("failed to decodeClusterfile %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
			}

			clusterfile.kc.JoinConfiguration = in
		case kubeadmConstants.ClusterConfigurationKind:
			var in v1beta3.ClusterConfiguration

			if err := yaml.Unmarshal(ext.Raw, &in); err != nil {
				return fmt.Errorf("failed to decodeClusterfile %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
			}

			clusterfile.kc.ClusterConfiguration = in
		case constants.KubeletConfiguration:
			var in v1beta1.KubeletConfiguration

			if err := yaml.Unmarshal(ext.Raw, &in); err != nil {
				return fmt.Errorf("failed to decodeClusterfile %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
			}

			clusterfile.kc.KubeletConfiguration = in
		case constants.KubeProxyConfiguration:
			var in v1alpha1.KubeProxyConfiguration

			if err := yaml.Unmarshal(ext.Raw, &in); err != nil {
				return fmt.Errorf("failed to decodeClusterfile %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
			}

			clusterfile.kc.KubeProxyConfiguration = in
		}
	}
}

// checkAndSetCluster 检查cluster和补全信息
func checkAndSetCluster(cluster *ctv1beta1.Cluster) error {
	defaultInsecure := false
	defaultHA := true

	if cluster.Spec.Registry.LocalRegistry == nil && cluster.Spec.Registry.ExternalRegistry == nil {
		cluster.Spec.Registry.LocalRegistry = &ctv1beta1.LocalRegistry{}
	}

	if cluster.Spec.Registry.LocalRegistry != nil {
		if cluster.Spec.Registry.LocalRegistry.Domain == "" {
			cluster.Spec.Registry.LocalRegistry.Domain = constants.DefaultRegistryDomain
		}
		if cluster.Spec.Registry.LocalRegistry.Port == 0 {
			cluster.Spec.Registry.LocalRegistry.Port = constants.DefaultRegistryPort
		}
		if cluster.Spec.Registry.LocalRegistry.Insecure == nil {
			cluster.Spec.Registry.LocalRegistry.Insecure = &defaultInsecure
		}
		if cluster.Spec.Registry.LocalRegistry.HA == nil {
			cluster.Spec.Registry.LocalRegistry.HA = &defaultHA
		}
	}

	if cluster.Spec.Registry.ExternalRegistry != nil {
		if cluster.Spec.Registry.ExternalRegistry.Domain == "" {
			return fmt.Errorf("external registry domain can not be empty")
		}
	}

	var newEnv []string
	for _, env := range cluster.Spec.Env {
		if strings.HasPrefix(env, constants.EnvLocalRegistryDomain) || strings.HasPrefix(env, constants.EnvLocalRegistryPort) || strings.HasPrefix(env, constants.EnvLocalRegistryURL) || strings.HasPrefix(env, constants.EnvExternalRegistryDomain) ||
			strings.HasPrefix(env, constants.EnvExternalRegistryPort) || strings.HasPrefix(env, constants.EnvExternalRegistryURL) || strings.HasPrefix(env, constants.EnvRegistryDomain) || strings.HasPrefix(env, constants.EnvRegistryPort) ||
			strings.HasPrefix(env, constants.EnvRegistryURL) || strings.HasPrefix(env, constants.EnvContainerRuntime) || strings.HasPrefix(env, constants.EnvDNSSvcIP) || strings.HasPrefix(env, constants.EnvKubeSvcIP) {
			continue
		}
		newEnv = append(newEnv, env)
	}
	cluster.Spec.Env = newEnv

	clusterEnvMap := strs.ConvertStringSliceToMap(cluster.Spec.Env)
	if svcCIDR, ok := clusterEnvMap[constants.EnvSvcCIDR]; ok && svcCIDR != "" {
		cidrs := strings.Split(svcCIDR, ",")
		_, cidr, err := net.ParseCIDR(cidrs[0])
		if err != nil {
			return fmt.Errorf("failed to parse svc CIDR: %v", err)
		}
		kubeIP, err := unet.GetIndexIP(cidr, 1)
		if err != nil {
			return fmt.Errorf("failed to get 1th ip from svc CIDR: %v", err)
		}
		dnsIP, err := unet.GetIndexIP(cidr, 10)
		if err != nil {
			return fmt.Errorf("failed to get 10th ip from svc CIDR: %v", err)
		}
		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvKubeSvcIP, kubeIP))
		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvDNSSvcIP, dnsIP))
	}

	regConfig := ctv1beta1.RegistryConfig{}
	if cluster.Spec.Registry.LocalRegistry != nil {
		regConfig = cluster.Spec.Registry.LocalRegistry.RegistryConfig

		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvLocalRegistryDomain, regConfig.Domain))
		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%d", constants.EnvLocalRegistryPort, regConfig.Port))
		registryURL := net.JoinHostPort(regConfig.Domain, strconv.Itoa(regConfig.Port))
		if regConfig.Port == 0 {
			registryURL = regConfig.Domain
		}
		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvLocalRegistryURL, registryURL))
	}
	if cluster.Spec.Registry.ExternalRegistry != nil {
		regConfig = cluster.Spec.Registry.ExternalRegistry.RegistryConfig

		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvExternalRegistryDomain, regConfig.Domain))
		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvExternalRegistryVIP, cluster.Spec.Registry.ExternalRegistry.RegistryVIP))
		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%d", constants.EnvExternalRegistryPort, regConfig.Port))
		registryURL := net.JoinHostPort(regConfig.Domain, strconv.Itoa(regConfig.Port))
		if regConfig.Port == 0 {
			registryURL = regConfig.Domain
		}
		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvExternalRegistryURL, registryURL))
	}

	cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvRegistryDomain, regConfig.Domain))
	portStr := fmt.Sprintf("%d", regConfig.Port)
	if regConfig.Port == 0 {
		portStr = ""
	}
	cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvRegistryPort, portStr))
	registryURL := net.JoinHostPort(regConfig.Domain, strconv.Itoa(regConfig.Port))
	if regConfig.Port == 0 {
		registryURL = regConfig.Domain
	}
	cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvRegistryURL, registryURL))

	if cluster.Spec.ContainerRuntime.Type != "" {
		cluster.Spec.Env = append(cluster.Spec.Env, fmt.Sprintf("%s=%s", constants.EnvContainerRuntime, cluster.Spec.ContainerRuntime.Type))
	}

	if cluster.Spec.DataRoot == "" {
		cluster.Spec.DataRoot = constants.DefaultKubepilotDataDir
	}

	return nil
}

// parseLaunchCmds parse shell, kube,helm type launch cmds
// kubectl apply -n kube-system -f ns.yaml -f app.yaml
// helm install my-nginx bitnami/nginx
// key1=value1 key2=value2 && bash install1.sh && bash install2.sh
func parseLaunchCmds(launch *appv1beta1.Launch) []string {
	if launch.Cmds != nil {
		return launch.Cmds
	}
	// TODO add shell,helm,kube type cmds.
	return nil
}

// MergeClusterWithImageExtension :set default value get from image extension,such as image global env
func MergeClusterWithImageExtension(cluster *ctv1beta1.Cluster, imageExt entv1beta1.ImageExtension) *ctv1beta1.Cluster {
	if len(imageExt.Env) > 0 {
		envs := maps.ConvertToSlice(imageExt.Env)
		envs = append(envs, cluster.Spec.Env...)
		cluster.Spec.Env = envs
	}

	return cluster
}

// MergeArgsToCluster 若执行Clusterfile前集群已存在，则会跳过已安装的节点
func MergeArgsToCluster(currentImage string, cluster ctv1beta1.Cluster, mergeFlags *commands.MergeFlags) (*ctv1beta1.Cluster, error) {
	if len(mergeFlags.CustomEnv) > 0 {
		cluster.Spec.Env = append(cluster.Spec.Env, mergeFlags.CustomEnv...)
	}

	if len(mergeFlags.Cmds) > 0 {
		cluster.Spec.CMD = mergeFlags.Cmds
	}

	if len(mergeFlags.AppNames) > 0 {
		cluster.Spec.APPNames = mergeFlags.AppNames
	}

	if len(mergeFlags.Components) > 0 {
		cluster.Spec.Components = mergeFlags.Components
	}

	// if no master and node specify form flag, just return.
	if len(mergeFlags.Masters) == 0 && len(mergeFlags.Nodes) == 0 {
		return &cluster, nil
	}

	// 如果没有指定master，并且cluster信息也没有master，则尝试获取本地默认IP作为主节点地址
	if len(mergeFlags.Masters) == 0 && len(cluster.GetMasterIPList()) == 0 {
		defaultIP, err := unet.GetLocalDefaultIP()
		if err != nil {
			return nil, fmt.Errorf("failed to get local default IP: %v", err)
		}
		mergeFlags.Masters = defaultIP
		klog.Warningf("Input has no master and cluster.Spec.Master is empty, the current node [%s] is used as the default master", defaultIP)
	}

	flagMasters, flagNodes, err := unet.ParseToNetIPList(mergeFlags.Masters, mergeFlags.Nodes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse ip string to net IP list: %v", err)
	}
	masterIPs := cluster.GetMasterIPList()
	nodeIPs := cluster.GetNodeIPList()
	// 当在已有集群的环境上执行Clusterfile,有两种情况：
	// 1.若基本k8s镜像发生变更，则无须过滤已存在的节点,因为都要升级,只需考虑慎重升级.
	// 2.若基本k8s镜像没变化,则不能将当前集群已存在的节点出现在Clusterfile中.
	if len(currentImage) > 0 && currentImage != cluster.Spec.Image {
		// 判断Clusterfile文件中的image是否为k8s镜像
		imageSpec, err := GetImageInfo(cluster.Spec.Image)
		if err != nil {
			return nil, err
		}
		if !imageSpec.IsRootfs() {
			klog.Exitf("The Clusterfile.Spec.image type [%s] is not k8s image", imageSpec.ImageExtension.Type)
		}
		if yes, _ := interaction.Confirm("集群已存在,且镜像版本有变更,确认要继续安装吗?!!🤔", "你已取消重复安装集群👍"); !yes {
			return nil, ErrCancelled
		}

		// 这里需要去重，当镜像升级，且用户命令行输入的IP可能与现存的Clusterfile中的IP有重合
		flagMasters = unet.RemoveDuplicates(append(flagMasters, masterIPs...))
		flagNodes = unet.RemoveDuplicates(append(flagMasters, flagNodes...))

		klog.Warningf("continue...the nodes of the existing cluster nodes [%s]-[%s] will be upgraded together", flagMasters, flagNodes)
	} else {
		// validate run flags masters
		for _, ip := range flagMasters {
			if unet.IsInIPList(ip, masterIPs) {
				return nil, fmt.Errorf("failed to merge master ip form flags, duplicated ip is: %s", ip)
			}
		}

		// validate run flags nodes
		for _, ip := range flagNodes {
			if unet.IsInIPList(ip, nodeIPs) {
				return nil, fmt.Errorf("failed to merge node ip form flags, duplicated ip is: %s", ip)
			}
		}
	}

	// TODO: validate ssh auth
	flagHosts := unet.TransferIPToHosts(flagMasters, flagNodes, ctv1beta1.SSH{
		User:     mergeFlags.User,
		Passwd:   mergeFlags.Password,
		PkPasswd: mergeFlags.PkPassword,
		Pk:       mergeFlags.Pk,
		Port:     strconv.Itoa(int(mergeFlags.Port)),
	})

	cluster.Spec.Hosts = append(cluster.Spec.Hosts, flagHosts...)
	return &cluster, err
}

// BuildClusterStruct 基于run参数构建cluster数据结构
func BuildClusterStruct(imageName string, runFlags *commands.RunFlags) (*ctv1beta1.Cluster, error) {
	masterIPList, nodeIPList, err := unet.ParseToNetIPList(runFlags.Masters, runFlags.Nodes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse ip string to net IP list: %v", err)
	}

	cluster := ctv1beta1.Cluster{
		Spec: ctv1beta1.ClusterSpec{
			SSH: ctv1beta1.SSH{
				User:     runFlags.User,
				Passwd:   runFlags.Password,
				PkPasswd: runFlags.PkPassword,
				Pk:       runFlags.Pk,
				Port:     strconv.Itoa(int(runFlags.Port)),
			},
			Image: imageName,
			// use cluster ssh auth by default
			Hosts:      unet.TransferIPToHosts(masterIPList, nodeIPList, ctv1beta1.SSH{}),
			Env:        runFlags.CustomEnv,
			CMD:        runFlags.Cmds,
			APPNames:   runFlags.AppNames,
			Components: runFlags.Components,
		},
	}
	cluster.APIVersion = ctv1beta1.SchemeGroupVersion.String()
	cluster.Kind = constants.ClusterKind
	cluster.Name = runFlags.ClusterName

	return &cluster, nil
}

// ConstructClusterWithSpecifiedHosts 只保留指定的hosts
func ConstructClusterWithSpecifiedHosts(cluster *ctv1beta1.Cluster, ipsToKeep []net.IP) {
	var newHosts []ctv1beta1.Host

	for _, host := range cluster.Spec.Hosts {
		var newIPs []net.IP
		for _, ip := range host.IPS {
			if unet.IsInIPList(ip, ipsToKeep) {
				newIPs = append(newIPs, ip)
			}
		}
		if len(newIPs) > 0 {
			host.IPS = newIPs
			newHosts = append(newHosts, host)
		}
	}

	cluster.Spec.Hosts = newHosts
}

type ClusterInfo struct {
	Masters      []net.IP // join master
	Workers      []net.IP // join worker
	CurrentNodes []net.IP
	Cluster      *ctv1beta1.Cluster
	OptionFunc   []ClusterOptionFunc
	ScaleFlags   *commands.JoinFlags // join节点的用户名密码等信息
}

type ClusterOptionFunc func(c ClusterInfo) error

// ConstructClusterForScaleUp init cluster info for scale up
func ConstructClusterForScaleUp(c ClusterInfo) error {
	// TODO Add password encryption mode in the future
	// add joined masters
	for _, ip := range c.Masters {
		// if ip already taken by node, skip it
		if unet.IsInIPList(ip, c.CurrentNodes) {
			return fmt.Errorf("failed to scale master for duplicated ip: %s", ip)
		}
	}
	if len(c.Masters) != 0 {
		host := buildHost(consts.MASTER, c.Masters, c.ScaleFlags, c.Cluster.Spec.SSH)
		c.Cluster.Spec.Hosts = append(c.Cluster.Spec.Hosts, host)
	}

	for _, ip := range c.Workers {
		// if ip already taken by node, skip it
		if unet.IsInIPList(ip, c.CurrentNodes) {
			return fmt.Errorf("failed to scale node for duplicated ip: %s", ip)
		}
	}
	// add joined nodes
	if len(c.Workers) != 0 {
		host := buildHost(consts.NODE, c.Workers, c.ScaleFlags, c.Cluster.Spec.SSH)
		c.Cluster.Spec.Hosts = append(c.Cluster.Spec.Hosts, host)
	}

	return nil
}

// ParseClusterOsPlatform 提取集群系统架构配置
// 格式如下：Linux/amd64、Linux/arm64
func ParseClusterOsPlatform(clusterPlatformConfig string) ([]imgv1beta1.Platform, error) {
	// 如果为空不处理
	if len(clusterPlatformConfig) == 0 {
		return nil, nil
	}
	result := make([]imgv1beta1.Platform, 0)
	// 解析成数组
	var osPlatforms []string
	if err := json.Unmarshal([]byte(clusterPlatformConfig), &osPlatforms); err != nil {
		return nil, err
	}
	for _, osPlatform := range osPlatforms {
		platform, err := parsePlatform(osPlatform)
		if err != nil {
			return nil, err
		}
		result = append(result, platform)
	}
	return result, nil
}

// parsePlatform 将输入字符串解析为 Platform 结构体
func parsePlatform(osPlatform string) (imgv1beta1.Platform, error) {
	// 分割输入字符串，格式为 "OS/Architecture/Variant"
	// OS/Architecture 必须填，Variant非必须
	parts := strings.Split(osPlatform, "/")
	if len(parts) < 2 {
		return imgv1beta1.Platform{}, fmt.Errorf("invalid input format: %s", osPlatform)
	}
	// 初始化 Platform 结构体
	platform := imgv1beta1.Platform{
		OS:           strings.ToLower(strings.TrimSpace(parts[0])),
		Architecture: strings.ToLower(strings.TrimSpace(parts[1])),
	}
	// 如果存在 Variant，则设置 Variant 字段
	if len(parts) > 2 {
		platform.Variant = strings.ToLower(strings.TrimSpace(parts[2]))
	}
	return platform, nil
}

// AppendOsPlatform 添加clusterfile配置的osPlatform
// clusterHostsPlatform 根据ip获取的节点架构map
// osPlatforms cluserfile配置集群需要支持的架构
func AppendOsPlatform(clusterHostsPlatform map[imgv1beta1.Platform][]net.IP, osPlatforms []imgv1beta1.Platform) {
	// 定义中间存储变量
	temp := make(map[imgv1beta1.Platform][]net.IP)
	for _, osPlatform := range osPlatforms {
		found := false
		for key, _ := range clusterHostsPlatform {
			if key.Architecture == osPlatform.Architecture && key.OS == osPlatform.OS {
				found = true
				break
			}
		}
		// 如果根据ip获取的架构和配置的架构一致，则不添加
		if !found {
			temp[osPlatform] = nil
		}
	}
	if len(temp) == 0 {
		return
	}
	// 注入不存在的架构到clusterHostsPlatform
	for key, value := range temp {
		clusterHostsPlatform[key] = value
	}
}

func SaveNodeToCluster(c ClusterInfo) error {
	// TODO Add password encryption mode in the future
	// add joined masters
	if len(c.Masters) != 0 {
		host := buildHost(consts.MASTER, c.Masters, c.ScaleFlags, c.Cluster.Spec.SSH)
		c.Cluster.Spec.Hosts = append(c.Cluster.Spec.Hosts, host)
	}

	// add joined nodes
	if len(c.Workers) != 0 {
		host := buildHost(consts.NODE, c.Workers, c.ScaleFlags, c.Cluster.Spec.SSH)
		c.Cluster.Spec.Hosts = append(c.Cluster.Spec.Hosts, host)
	}

	return nil
}

func RemoveNodeFromCluster(c ClusterInfo) error {
	if len(c.Masters) != 0 {
		for i := range c.Cluster.Spec.Hosts {
			if strs.IsInSlice(consts.MASTER, c.Cluster.Spec.Hosts[i].Roles) {
				c.Cluster.Spec.Hosts[i].IPS = unet.RemoveIPs(c.Cluster.Spec.Hosts[i].IPS, c.Masters)
			}
			continue
		}
	}

	if len(c.Workers) != 0 {
		for i := range c.Cluster.Spec.Hosts {
			if strs.IsInSlice(consts.NODE, c.Cluster.Spec.Hosts[i].Roles) {
				c.Cluster.Spec.Hosts[i].IPS = unet.RemoveIPs(c.Cluster.Spec.Hosts[i].IPS, c.Workers)
			}
			continue
		}
	}

	// if hosts have no ip address exist,then delete this host.
	var hosts []ctv1beta1.Host
	for _, host := range c.Cluster.Spec.Hosts {
		if len(host.IPS) == 0 {
			continue
		}
		hosts = append(hosts, host)
	}
	c.Cluster.Spec.Hosts = hosts
	return nil
}

// buildHost init host struct
func buildHost(role string, joinIPs []net.IP, scaleFlags *commands.JoinFlags, clusterSSH ctv1beta1.SSH) ctv1beta1.Host {
	// todo we could support host level env form cli later.
	// todo we could support host level role form cli later.
	host := ctv1beta1.Host{
		IPS:   joinIPs,
		Roles: []string{role},
		Env:   scaleFlags.CustomEnv,
	}

	scaleFlagSSH := ctv1beta1.SSH{
		User:     scaleFlags.User,
		Passwd:   scaleFlags.Password,
		Port:     strconv.Itoa(int(scaleFlags.Port)),
		Pk:       scaleFlags.Pk,
		PkPasswd: scaleFlags.PkPassword,
	}

	if reflect.DeepEqual(scaleFlagSSH, clusterSSH) {
		return host
	}

	host.SSH = scaleFlagSSH
	return host
}

func GetCurrentCluster(client *k8s.Client) (*ctv1beta1.Cluster, error) {
	nodes, err := client.ListNodes()
	if err != nil {
		return nil, err
	}

	cluster := &ctv1beta1.Cluster{}
	var masterIPList []net.IP
	var nodeIPList []net.IP

	for _, node := range nodes.Items {
		addr := getNodeAddress(node)
		if addr == nil {
			return nil, fmt.Errorf("failed to get node address for node %s", node.Name)
		}
		if _, ok := node.Labels[constants.MasterRoleLabel]; ok {
			masterIPList = append(masterIPList, addr)
			continue
		}
		nodeIPList = append(nodeIPList, addr)
	}
	cluster.Spec.Hosts = []ctv1beta1.Host{{IPS: masterIPList, Roles: []string{consts.MASTER}}, {IPS: nodeIPList, Roles: []string{consts.NODE}}}

	return cluster, nil
}

func getNodeAddress(node corev1.Node) net.IP {
	if len(node.Status.Addresses) < 1 {
		return nil
	}

	var IP string
	for _, address := range node.Status.Addresses {
		if address.Type == "InternalIP" {
			IP = address.Address
			break
		}
	}

	return net.ParseIP(IP)
}

func GetClusterClient() *k8s.Client {
	client, err := k8s.NewK8sClient()
	if client != nil {
		return client
	}
	if err != nil {
		klog.Warningf("try to new k8s client via default kubeconfig, maybe this is a new cluster that needs to be created: %v", err)
	}
	return nil
}

// GetImageInfo return image info
func GetImageInfo(imgName string) (*entv1beta1.ImageSpec, error) {
	e, err := imageengine.NewImgEngine(image.EngineGlobalConfig{})
	if err != nil {
		return nil, fmt.Errorf("failed to new image engine: %v", err)
	}
	// 拉取相关镜像
	imgId, err := e.Pull(&image.PullOpts{
		SkipTLSVerify: true,
		Quiet:         false,
		PullPolicy:    "missing",
		Image:         imgName,
		Platform:      "local",
	})
	if err != nil {
		return nil, fmt.Errorf("failed to pull image [name=%s]: %v", imgName, err)
	}
	// inspect image
	imageSpec, err := e.Inspect(&image.InspectOpts{
		ImageNameOrID: imgId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to inspect image [id=%s]: %v", imgId, err)
	}

	return imageSpec, nil
}

func updateComponentRecord(componentList []string, component string, isRemoveRecord bool) []string {
	if isRemoveRecord && strs.IsInSlice(component, componentList) {
		componentList = strs.RemoveElement(componentList, component) // 移除组件记录
	}
	if !isRemoveRecord && !strs.IsInSlice(component, componentList) {
		componentList = append(componentList, component) // 增加组件记录
	}
	return componentList
}
