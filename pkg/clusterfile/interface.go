package clusterfile

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"time"

	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	confv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/config/v1beta1"
	pgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/plugin/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/client/k8s"
	clusterk8s "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/kubeadm"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	imageutils "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image/util"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/template"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/strs"
	"helm.sh/helm/v3/pkg/cli/values"
	"helm.sh/helm/v3/pkg/getter"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/yaml"
)

var (
	ErrClusterFileNotExists     = errors.New("the cluster file is not exist")
	ErrMissingPathOrClusterInfo = errors.New("either path or clusterInfo must be provided")
)

var backoff = wait.Backoff{
	Steps:    8,               // 重试次数
	Duration: 1 * time.Second, // 时间间隔
	Factor:   1,               // 增长因子,Factor大于0时，Backoff在计算下次的时间间隔时会根据Duration*Factor
	Jitter:   0,               // 抖动时间
}

type Manager interface {
	GetCluster() ctv1beta1.Cluster
	SetCluster(ctv1beta1.Cluster)
	GetApplication() []*appv1beta1.Application
	SetApplication(app []*appv1beta1.Application)
	GetConfigs() []confv1beta1.Config
	GetPlugins() []pgv1beta1.Plugin
	GetKubeadmConfig() *kubeadm.KubeadmConfig
	SaveAll(opts SaveOptions) error
	Process() error
	CheckComponentVersion(application appv1beta1.Application) (bool, error)
}

type clusterfile struct {
	path        string   // clusterfile 路径
	valueFiles  []string // 渲染 clusterfile 参数配置文件
	clusterInfo *ctv1beta1.Cluster
	configs     []confv1beta1.Config
	kc          kubeadm.KubeadmConfig
	plugins     []pgv1beta1.Plugin
	app         []*appv1beta1.Application
}

func (c *clusterfile) GetCluster() ctv1beta1.Cluster {
	return *c.clusterInfo
}

func (c *clusterfile) SetCluster(cluster ctv1beta1.Cluster) {
	c.clusterInfo = &cluster
}

func (c *clusterfile) GetApplication() []*appv1beta1.Application {
	return c.app
}

func (c *clusterfile) SetApplication(app []*appv1beta1.Application) {
	c.app = app
}

func (c *clusterfile) GetConfigs() []confv1beta1.Config {
	return c.configs
}

func (c *clusterfile) GetPlugins() []pgv1beta1.Plugin {
	return c.plugins
}

func (c *clusterfile) GetApps() []*appv1beta1.Application {
	return c.app
}

func (c *clusterfile) GetKubeadmConfig() *kubeadm.KubeadmConfig {
	return &c.kc
}

func (c *clusterfile) SaveAll(opts SaveOptions) error {
	var (
		cm              = &corev1.ConfigMap{}
		cf              = new(clusterfile)
		ctx             = context.Background()
		fileName        = file.GetDefaultClusterfile()
		clusterFileData []byte
		err             error
	)

	if err := os.MkdirAll(filepath.Dir(fileName), os.ModePerm); err != nil {
		return fmt.Errorf("failed to mkdir %s: %v", fileName, err)
	}
	env := c.clusterInfo.Spec.Env
	// 消除Env中重复项
	c.clusterInfo.Spec.Env = strs.RemoveDuplicate(env)
	// 填充creationTimestamp
	c.clusterInfo.CreationTimestamp = metav1.Now()

	// 序列化clusterfile
	clusterFileData, err = c.serializeClusterFile()
	if err != nil {
		return err
	}
	// 预安装集群Clusterfile文件只会保存在宿主机本地,不会保存到集群的configmap
	if opts.PreInstallationCluster {
		// 保存本地Clusterfile文件
		if err = rw.NewCommonWriter(fileName).WriteFile(clusterFileData); err != nil {
			return fmt.Errorf("failed to save clusterfile to disk:%v", err)
		}
		return nil
	}

	cli, err := clusterk8s.GetClientFromConfig(opts.ConfigPath)
	if err != nil {
		return fmt.Errorf("failed to new k8s runtime client via adminconf: %v", err)
	}

	start := func() (bool, error) {
		connCtx, cancel := context.WithTimeout(ctx, time.Second*5)
		defer cancel()

		if err = cli.Get(connCtx, types.NamespacedName{Name: constants.ClusterfileConfigMapName, Namespace: constants.ClusterfileConfigMapNamespace}, cm); err != nil {
			// 判断是否存在名称为kubepilot-clusterfile的configmap,若不存在，则创建
			if !apierrors.IsNotFound(err) {
				return false, fmt.Errorf("unable to get configmap %s: %v", constants.ClusterfileConfigMapName, err)
			}

			cm = &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					// 这个Name不能携带业务信息或者集群信息，因为在join节点的时候，就获取不到,
					// 所以当kubeverse需要纳管集群的时候，它自己加上表示保存在自己的数据库中即可.
					// 不需要安装部署服务考虑这些业务的模块.
					Name:      constants.ClusterfileConfigMapName,
					Namespace: constants.ClusterfileConfigMapNamespace,
				},
				Data: map[string]string{constants.ClusterfileConfigMapDataName: string(clusterFileData)},
			}
			if err = cli.Create(connCtx, cm, &client.CreateOptions{}); err != nil {
				return false, fmt.Errorf("unable to create configmap %s: %v", constants.ClusterfileConfigMapName, err)
			}
			return true, nil
		}

		if len(cm.Data[constants.ClusterfileConfigMapDataName]) == 0 {
			return false, fmt.Errorf("clusterfile data is nil for configmap %s in namespace %s", cm.Name, cm.Namespace)
		}

		// 反序列化clusterfile
		if err = decodeClusterfile(bytes.NewReader([]byte(cm.Data[constants.ClusterfileConfigMapDataName])), cf); err != nil {
			return false, fmt.Errorf("failed to decode clusterfile %s: %v", constants.ClusterfileConfigMapName, err)
		}

		// 处理clusterfile,包括集群和组件的更新
		cfData, err := cf.processClusterFile(opts)
		if err != nil {
			return false, fmt.Errorf("failed to process clusterfile from configmap %s: %v", constants.ClusterfileConfigMapName, err)
		}

		cm.Data[constants.ClusterfileConfigMapDataName] = string(cfData)

		if err = cli.Update(connCtx, cm, &client.UpdateOptions{}); err != nil {
			// 检查资源版本冲突以解决并发问题
			if !apierrors.IsConflict(err) {
				return false, fmt.Errorf("unable to update configmap %s: %v", constants.ClusterfileConfigMapName, err)
			}
			return false, nil
		}
		klog.V(5).Info("successfully saved clusterfile")
		return true, nil
	}
	return wait.ExponentialBackoff(backoff, start)
}

func getClusterfileFromCluster() (*clusterfile, error) {
	cf := new(clusterfile)
	cli, err := k8s.NewK8sClient()
	if err != nil {
		return nil, err
	}

	cm, err := cli.ConfigMap(constants.ClusterfileConfigMapNamespace).Get(context.TODO(), constants.ClusterfileConfigMapName, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	data := cm.Data[constants.ClusterfileConfigMapDataName]
	if len(data) > 0 {
		if err = decodeClusterfile(bytes.NewReader([]byte(data)), cf); err != nil {
			return nil, err
		}
		return cf, nil
	}
	return nil, errors.New("failed to get clusterfile from cluster")
}

func GetActualClusterFile() (Manager, bool, error) {
	clusterFile := new(clusterfile)

	// assume that we already have an existed cluster
	fromCluster, err := getClusterfileFromCluster()
	if err != nil {
		klog.Warningf("unable to get clusterfile from cluster configmap %s: %v", constants.ClusterfileConfigMapName, err)
	}

	if fromCluster != nil {
		return fromCluster, true, nil
	}

	// read local disk clusterfile
	clusterFileData, err := os.ReadFile(filepath.Clean(file.GetDefaultClusterfile()))
	if err != nil {
		return nil, false, err
	}

	if err = decodeClusterfile(bytes.NewReader(clusterFileData), clusterFile); err != nil {
		return nil, false, fmt.Errorf("failed to load clusterfile: %v", err)
	}

	return clusterFile, false, nil
}

func IsExistClusterfile() bool {
	// assume that we already have an existed cluster
	_, err := getClusterfileFromCluster()
	if err != nil {
		klog.Warningf("unable to get clusterfile from cluster configmap %s: %v", constants.ClusterfileConfigMapName, err)
	}

	// read local disk clusterfile
	_, err = os.ReadFile(filepath.Clean(file.GetDefaultClusterfile()))

	return err == nil

}

// OptionFunc 是一个函数类型，用于处理 clusterfile 结构体的配置选项
type OptionFunc func(*clusterfile)

// NewClusterfile 通过聚合一系列配置选项来创建一个新的 ClusterFile 管理器
func NewClusterfile(opts ...OptionFunc) Manager {
	cf := &clusterfile{}
	for _, opt := range opts {
		opt(cf)
	}
	return cf
}

// WithValueFiles 返回一个 OptionFunc，用于设置 clusterfile 的 valueFiles 字段
func WithValueFiles(valueFiles []string) OptionFunc {
	return func(c *clusterfile) {
		c.valueFiles = valueFiles
	}
}

// WithPath 返回一个 OptionFunc，用于设置 clusterfile 的 path 字段
func WithPath(path string) OptionFunc {
	return func(c *clusterfile) {
		c.path = path
	}
}

// WithClusterInfo 返回一个 OptionFunc，用于设置 clusterfile 的 clusterInfo 字段
func WithClusterInfo(clusterInfo *ctv1beta1.Cluster) OptionFunc {
	return func(c *clusterfile) {
		c.clusterInfo = clusterInfo
	}
}

// Process 处理clusterfile
func (c *clusterfile) Process() (err error) {
	clusterFileData, err := c.loadClusterFile()
	if err != nil {
		return err
	}

	klog.V(5).Infof("rendered Clusterfile: %+v", string(clusterFileData))
	return decodeClusterfile(bytes.NewReader(clusterFileData), c)
}

// loadClusterFile 根据配置加载集群文件或序列化集群信息
func (c *clusterfile) loadClusterFile() ([]byte, error) {
	// 检查路径和集群信息是否都未提供，如果都未提供，则返回错误
	if c.path == "" && c.clusterInfo == nil {
		return nil, ErrMissingPathOrClusterInfo
	}

	// 检查提供的路径是否存在文件，如果路径存在但文件不存在，则返回错误
	if c.path != "" && !file.IsFileExist(c.path) {
		return nil, ErrClusterFileNotExists
	}

	var (
		body []byte
		err  error
	)

	// 根据路径读取文件或序列化 clusterInfo
	if c.path != "" {
		// 如果提供了路径，则尝试读取文件
		body, err = os.ReadFile(filepath.Clean(c.path))
	} else {
		// 如果未提供路径但提供了集群信息，则尝试序列化集群信息
		body, err = yaml.Marshal(c.clusterInfo)
	}

	if err != nil {
		return nil, err
	}

	mergeValues, err := c.loadRenderValues()
	if err != nil {
		return nil, err
	}
	klog.V(5).Infof("loadClusterFile loadRenderValues: %+v", mergeValues)

	data := map[string]interface{}{
		"Values": mergeValues,
	}
	out := bytes.NewBuffer(nil)
	tpl, isOk, err := template.TryParse(string(body))
	if isOk {
		if err != nil {
			return nil, err
		}
		if err := tpl.Execute(out, data); err != nil {
			return nil, err
		}
	} else {
		out.Write(body)
	}

	return out.Bytes(), nil
}

// loadRenderValues 加载并合并集群文件中指定的值选项
func (c *clusterfile) loadRenderValues() (map[string]interface{}, error) {
	valueOpt := &values.Options{
		ValueFiles: c.valueFiles,
	}
	return valueOpt.MergeValues([]getter.Provider{{
		Schemes: []string{"http", "https"},
		New:     getter.NewHTTPGetter,
	}})
}

// CheckComponentVersion 检测组件版本是否改变
func (c *clusterfile) CheckComponentVersion(application appv1beta1.Application) (bool, error) {
	_, newTag, err := imageutils.ParseComponentFromImageName(application.GetName())
	if err != nil {
		return false, err
	}

	for _, item := range c.GetApplication() {
		if application.GetUID() == item.GetUID() {
			_, oldTag, err := imageutils.ParseComponentFromImageName(item.GetName())
			if err != nil {
				return false, err
			}
			if newTag != oldTag {
				// 找到相同的组件且版本不一致，即新组件
				return true, nil
			}
			// 找到相同的组件但是没有版本变化
			return false, nil
		}
	}
	// 列表没有找到该组件,说明是新组件
	return false, fmt.Errorf("the upgrade command cannot be executed because the application[%s] does not exist. please use the run command to install the application", application.GetName())
}

func (c *clusterfile) processClusterFile(opts SaveOptions) ([]byte, error) {
	// 更新Application
	if opts.Application != nil {
		c.updateApplication(opts.Application)
	}

	// 更新Cluster
	if err := c.updateCluster(opts); err != nil {
		return nil, err
	}

	// 序列化clusterfile
	cfData, err := c.serializeClusterFile()
	if err != nil {
		return nil, err
	}
	return cfData, nil
}

func (c *clusterfile) updateApplication(application *appv1beta1.Application) {
	var (
		applicationMap  = make(map[types.UID]*appv1beta1.Application)
		applicationList []*appv1beta1.Application
	)

	for _, item := range c.GetApplication() {
		applicationMap[item.GetUID()] = item
	}

	if _, ok := applicationMap[application.GetUID()]; ok && corev1.ConditionTrue == application.Status.Conditions[0].Status && application.Status.Conditions[0].Type == appv1beta1.AppUninstallSuccess {
		delete(applicationMap, application.GetUID())
		// 更新cluster.spec.components列表
		c.clusterInfo.Spec.Components = updateComponentRecord(c.clusterInfo.Spec.Components, application.Name, true)
	} else {
		applicationMap[application.GetUID()] = application
		c.clusterInfo.Spec.Components = updateComponentRecord(c.clusterInfo.Spec.Components, application.Name, false)
	}

	for _, v := range applicationMap {
		applicationList = append(applicationList, v)
	}
	c.SetApplication(applicationList)
}

func (c *clusterfile) updateCluster(opts SaveOptions) error {
	if len(opts.ClusterInfo.OptionFunc) > 0 {
		opts.ClusterInfo.Cluster = c.clusterInfo
		for _, fn := range opts.ClusterInfo.OptionFunc {
			if err := fn(opts.ClusterInfo); err != nil {
				return fmt.Errorf("error executing function: %v", err)
			}
		}
		c.SetCluster(*opts.ClusterInfo.Cluster)
	}
	return nil
}

func (c *clusterfile) serializeClusterFile() ([]byte, error) {
	var (
		// clusterfile data
		cfData                     [][]byte
		appData, confDadta, pgData []byte
	)

	cluster, err := yaml.Marshal(c.clusterInfo)
	if err != nil {
		return nil, err
	}
	cfData = append(cfData, cluster)

	if c.app != nil {
		for _, app := range c.app {
			appData, err = yaml.Marshal(app)
			if err != nil {
				return nil, err
			}
			cfData = append(cfData, appData)
		}
	}

	if len(c.configs) != 0 {
		for _, cg := range c.configs {
			confDadta, err = yaml.Marshal(cg)
			if err != nil {
				return nil, err
			}
			cfData = append(cfData, confDadta)
		}
	}

	if len(c.plugins) != 0 {
		for _, p := range c.plugins {
			pgData, err = yaml.Marshal(p)
			if err != nil {
				return nil, err
			}
			cfData = append(cfData, pgData)
		}
	}

	if len(c.kc.InitConfiguration.TypeMeta.Kind) != 0 {
		initConfiguration, err := yaml.Marshal(c.kc.InitConfiguration)
		if err != nil {
			return nil, err
		}
		cfData = append(cfData, initConfiguration)
	}

	if len(c.kc.JoinConfiguration.TypeMeta.Kind) != 0 {
		joinConfiguration, err := yaml.Marshal(c.kc.JoinConfiguration)
		if err != nil {
			return nil, err
		}
		cfData = append(cfData, joinConfiguration)
	}
	if len(c.kc.ClusterConfiguration.TypeMeta.Kind) != 0 {
		clusterConfiguration, err := yaml.Marshal(c.kc.ClusterConfiguration)
		if err != nil {
			return nil, err
		}
		cfData = append(cfData, clusterConfiguration)
	}

	if len(c.kc.KubeletConfiguration.TypeMeta.Kind) != 0 {
		kubeletConfiguration, err := yaml.Marshal(c.kc.KubeletConfiguration)
		if err != nil {
			return nil, err
		}
		cfData = append(cfData, kubeletConfiguration)
	}

	if len(c.kc.KubeProxyConfiguration.TypeMeta.Kind) != 0 {
		kubeProxyConfiguration, err := yaml.Marshal(c.kc.KubeProxyConfiguration)
		if err != nil {
			return nil, err
		}
		cfData = append(cfData, kubeProxyConfiguration)
	}

	return bytes.Join(cfData, []byte("---\n")), nil
}
