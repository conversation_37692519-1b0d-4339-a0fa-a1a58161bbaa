package clusterfile

import (
	"net"
	"reflect"
	"testing"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
)

func TestConstructClusterWithSpecifiedHosts(t *testing.T) {
	// 准备测试数据
	cluster := &ctv1beta1.Cluster{
		Spec: ctv1beta1.ClusterSpec{
			Hosts: []ctv1beta1.Host{
				{
					IPS:   []net.IP{net.ParseIP("***********"), net.ParseIP("***********")},
					Roles: []string{"master"},
				},
				{
					IPS:   []net.IP{net.ParseIP("***********"), net.ParseIP("***********")},
					Roles: []string{"node"},
				},
				{
					IPS:   []net.IP{net.ParseIP("************")},
					Roles: []string{"node"},
				},
			},
		},
	}

	ipsToKeep := []net.IP{net.ParseIP("***********"), net.ParseIP("***********")}

	// 执行函数
	ConstructClusterWithSpecifiedHosts(cluster, ipsToKeep)

	// 验证结果
	expectedHosts := []ctv1beta1.Host{
		{
			IPS:   []net.IP{net.ParseIP("***********")},
			Roles: []string{"master"},
		},
		{
			IPS:   []net.IP{net.ParseIP("***********")},
			Roles: []string{"node"},
		},
	}

	if !reflect.DeepEqual(cluster.Spec.Hosts, expectedHosts) {
		t.Errorf("ConstructClusterWithSpecifiedHosts() got = %v, want %v", cluster.Spec.Hosts, expectedHosts)
	}
}
