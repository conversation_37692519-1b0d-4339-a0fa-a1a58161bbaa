package ipvs

import (
	"testing"
)

var want = []string{
	`apiVersion: v1
kind: Pod
metadata:
  creationTimestamp: null
  name: kube-lvscare
  namespace: kube-system
spec:
  containers:
  - args:
    - care
    - --vs
    - **********:6443
    - --health-path
    - /healthz
    - --health-schem
    - https
    - --rs
    - **************:6443
    - --rs
    - **************:6443
    - --rs
    - **************:6443
    command:
    - /usr/bin/lvscare
    image: fdfadf
    imagePullPolicy: IfNotPresent
    name: main
    resources: {}
    securityContext:
      privileged: true
    volumeMounts:
    - mountPath: /lib/modules
      name: lib-modules
      readOnly: true
  hostNetwork: true
  volumes:
  - hostPath:
      path: /lib/modules
      type: ""
    name: lib-modules
status: {}
`,
	`apiVersion: v1
kind: Pod
metadata:
  creationTimestamp: null
  name: kube-lvscare
  namespace: kube-system
spec:
  containers:
  - args:
    - care
    - --vs
    - **********:6443
    - --health-path
    - /healthz
    - --health-schem
    - https
    - --rs
    - **************:6443
    command:
    - /usr/bin/lvscare
    image: fdfadf
    imagePullPolicy: IfNotPresent
    name: main
    resources: {}
    securityContext:
      privileged: true
    volumeMounts:
    - mountPath: /lib/modules
      name: lib-modules
      readOnly: true
  hostNetwork: true
  volumes:
  - hostPath:
      path: /lib/modules
      type: ""
    name: lib-modules
status: {}
`,
	`apiVersion: v1
kind: Pod
metadata:
  creationTimestamp: null
  name: reg-lvscare
  namespace: kube-system
spec:
  containers:
  - args:
    - care
    - --vs
    - **********:5000
    - --health-path
    - /healthz
    - --health-schem
    - https
    - --rs
    - **************:5000
    command:
    - /usr/bin/lvscare
    image: a1
    imagePullPolicy: IfNotPresent
    name: main
    resources: {}
    securityContext:
      privileged: true
    volumeMounts:
    - mountPath: /lib/modules
      name: lib-modules
      readOnly: true
  hostNetwork: true
  volumes:
  - hostPath:
      path: /lib/modules
      type: ""
    name: lib-modules
status: {}
`,
}

func TestLvsStaticPodYaml(t *testing.T) {
	type args struct {
		podName     string
		vip         string
		masters     []string
		image       string
		healthPath  string
		healthSchem string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				podName: "kube-lvscare",
				vip:     "**********:6443",
				masters: []string{
					"**************:6443",
					"**************:6443",
					"**************:6443",
				},
				image:       "fdfadf",
				healthPath:  "/healthz",
				healthSchem: "https",
			},
			want: want[0],
		},
		{
			args: args{
				podName:     "kube-lvscare",
				vip:         "**********:6443",
				masters:     []string{"**************:6443"},
				image:       "fdfadf",
				healthPath:  "/healthz",
				healthSchem: "https",
			},
			want: want[1],
		},
		{
			args: args{
				podName:     "reg-lvscare",
				vip:         "**********:5000",
				masters:     []string{"**************:5000"},
				image:       "a1",
				healthPath:  "/healthz",
				healthSchem: "https",
			},
			want: want[2],
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

		})
	}
}
