package ipvs

import (
	"fmt"
	"os"
	"path"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
)

type Kubealived struct {
	NetCardName          string `yaml:"netCardName"`
	ImageName            string `yaml:"imageName"`
	ImageVersion         string `yaml:"imageVersion"`
	InCluster            bool   `yaml:"inCluster"`
	Port                 string `yaml:"port"`
	Address              string `yaml:"address"`
	Subnet               string `yaml:"subnet"`
	Namespace            string `yaml:"namespace"`
	EnableLeaderElection string `yaml:"enableLeaderElection"`
	LeaseName            string `yaml:"leaseName"`
	LeaderElectionType   string `yaml:"leaderElectionType"`
	IpvsLBType           string `yaml:"ipvsLBType"`
	ForwardMethod        string `yaml:"forwardMethod"`
}

// BuildKubealivedStaticPodCmd 构建kubealived命令
func BuildKubealivedStaticPodCmd(content, fileName string) string {
	// FIXME: 这里不能使用简单的mkdir -p %s && echo \"%s\" > kubealived.yaml
	// 因为生成yaml文件字符串的值没有双引号包裹，导致kubelet对其解析失败，无法启动该静态Pod
	return fmt.Sprintf("mkdir -p %s && echo \"%s\" | sed -E 's/(value: )([a-zA-Z][a-zA-Z0-9]*)/\\1\"\\2\"/' > %s",
		constants.StaticPodDir,
		content,
		path.Join(constants.StaticPodDir, fileName),
	)
}

// BuildKubealivedStaticPodFromSpec return kubealived static pod spec
func BuildKubealivedStaticPodFromSpec(kl *Kubealived) *v1.Pod {
	cmd := string(constants.Run)
	namespace := metav1.NamespaceSystem

	env := []v1.EnvVar{
		{
			Name:  constants.EnableARP,
			Value: "true",
		},
		{
			Name:  constants.EnableLB,
			Value: "true",
		},
		{
			Name:  constants.Namespace,
			Value: kl.Namespace,
		},
		{
			Name:  constants.IsMaster,
			Value: "true",
		},
		{
			Name:  constants.LeaderElection,
			Value: kl.EnableLeaderElection,
		},
		{
			Name:  constants.LeaseName,
			Value: kl.LeaseName,
		},
		{
			Name:  constants.LeaseDuration,
			Value: "15",
		},
		{
			Name:  constants.RenewDeadline,
			Value: "10",
		},
		{
			Name:  constants.RetryPeriod,
			Value: "2",
		},
		{
			Name:  constants.ElectionType,
			Value: kl.LeaderElectionType,
		},
		{
			Name:  constants.IPVSLB,
			Value: kl.IpvsLBType,
		},
		{
			Name:  constants.ForwardMethod,
			Value: kl.ForwardMethod,
		},
	}

	if kl.NetCardName != "" {
		iface := []v1.EnvVar{
			{
				Name:  constants.NetCardName,
				Value: kl.NetCardName,
			},
		}
		env = append(env, iface...)
	}

	if kl.Address != "" {
		ip := []v1.EnvVar{
			{
				Name:  constants.Address,
				Value: kl.Address,
			},
			{
				Name:  constants.Subnet,
				Value: kl.Subnet,
			},
		}
		env = append(env, ip...)
	}

	klog.V(5).Infof("The Env about static pod kubealived: %+v", env)

	pod := &v1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "kubealived",
			Namespace: namespace,
		},
		Spec: v1.PodSpec{
			Containers: []v1.Container{
				{
					Name:            "kubealived",
					Image:           fmt.Sprintf("%s:%s", kl.ImageName, kl.ImageVersion),
					ImagePullPolicy: v1.PullIfNotPresent,
					SecurityContext: &v1.SecurityContext{
						Capabilities: &v1.Capabilities{
							Add: []v1.Capability{
								"NET_ADMIN",
								"NET_RAW",
							},
						},
					},
					Args: []string{
						cmd,
					},
					Env: env,
				},
			},
			HostNetwork: true,
		},
	}
	if kl.InCluster {
		// TODO: 当使用这种运行模式时，需要创建一个serviceAccount
		pod.Spec.ServiceAccountName = constants.KubealivedServiceAccount

	} else {
		confMount := v1.VolumeMount{
			Name:      constants.ConfigName,
			MountPath: constants.MoutPath,
		}
		pod.Spec.Containers[0].VolumeMounts = append(pod.Spec.Containers[0].VolumeMounts, confMount)

		confVolume := v1.Volume{
			Name: constants.ConfigName,
			VolumeSource: v1.VolumeSource{
				HostPath: &v1.HostPathVolumeSource{
					Path: constants.MoutPath,
				},
			},
		}
		pod.Spec.Volumes = append(pod.Spec.Volumes, confVolume)

		hostAlias := v1.HostAlias{
			IP:        constants.HostIP,
			Hostnames: []string{constants.HostName},
		}
		pod.Spec.HostAliases = append(pod.Spec.HostAliases, hostAlias)
	}

	// 添加 ipvs 规则文件卷挂载配置
	ipvsRuleMount := v1.VolumeMount{
		Name:      constants.IPVSRuleConfigName,
		MountPath: constants.IPVSRuleConfigPath,
	}
	pod.Spec.Containers[0].VolumeMounts = append(pod.Spec.Containers[0].VolumeMounts, ipvsRuleMount)
	ipvsRuleVolume := v1.Volume{
		Name: constants.IPVSRuleConfigName,
		VolumeSource: v1.VolumeSource{
			HostPath: &v1.HostPathVolumeSource{
				Path: constants.IPVSRuleConfigPath,
			},
		},
	}
	pod.Spec.Volumes = append(pod.Spec.Volumes, ipvsRuleVolume)

	return pod
}

func CreateKubealivedConfig(path string, data string) error {
	klog.V(5).Infof("The kubealived config data: %s", data)
	if err := os.WriteFile(path, []byte(data), 0600); err != nil {
		return fmt.Errorf("uable write static kubealived config to temp file: %v", err)
	}
	return nil
}

var IpvsRulesTemplate = `{{- range .ipvsRules }}
- name: {{ .name }}
  servicePort: {{ .servicePort }}
  destinationPort: {{ .destinationPort }}
  forwardingMethod: {{ .forwardingMethod | default "masquerade" }}
{{- if .enabledPersistentConnection }}
  enabledPersistentConnection: {{ .enabledPersistentConnection }}
{{- end }}
{{- if .timeout }}
  timeout: {{ .timeout }}
{{- end }}
{{- end }}

`
