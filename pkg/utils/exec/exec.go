package exec

import (
	"fmt"
	"os"
	"os/exec"
	"os/user"
	"strings"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"k8s.io/klog/v2"
)

const SUDO = "sudo"

func Cmd(name string, args ...string) error {
	username, err := GetCurrentUserName()
	if err != nil {
		return err
	}
	if username != constants.ROOT {
		args = append([]string{name}, args...)
		name = SUDO
	}
	cmd := exec.Command(name, args[:]...) // #nosec
	cmd.Stdin = os.Stdin
	cmd.Stderr = constants.StdErr
	cmd.Stdout = constants.StdOut
	return cmd.Run()
}

func CmdOutput(name string, args ...string) ([]byte, error) {
	username, err := GetCurrentUserName()
	if err != nil {
		return nil, err
	}
	if username != constants.ROOT {
		args = append([]string{name}, args...)
		name = SUDO
	}
	cmd := exec.Command(name, args[:]...) // #nosec
	return cmd.CombinedOutput()
}

func RunSimpleCmd(cmd string) (string, error) {
	username, err := GetCurrentUserName()
	if err != nil {
		return "", err
	}
	var result []byte
	if username != constants.ROOT {
		result, err = exec.Command(SUDO, "/bin/bash", "-c", cmd).CombinedOutput() // #nosec
	} else {
		result, err = exec.Command("/bin/bash", "-c", cmd).CombinedOutput() // #nosec
	}
	if err != nil {
		klog.V(5).Infof("failed to execute command(%s): error(%v)", cmd, err)
	}
	return string(result), err
}

func CheckCmdIsExist(cmd string) (string, bool) {
	cmd = fmt.Sprintf("type %s", cmd)
	out, err := RunSimpleCmd(cmd)
	if err != nil {
		return "", false
	}

	outSlice := strings.Split(out, "is")
	last := outSlice[len(outSlice)-1]

	if last != "" && !strings.Contains(last, "not found") {
		return strings.TrimSpace(last), true
	}
	return "", false
}

func GetCurrentUserName() (string, error) {
	u, err := user.Current()
	return u.Username, err
}
