package net

import (
	"errors"
	"fmt"
	"net"
	"os"
	"strings"
	"syscall"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/exec"
	"k8s.io/klog/v2"

	"github.com/vishvananda/netlink"
	k8snet "k8s.io/apimachinery/pkg/util/net"
	k8sutilsnet "k8s.io/utils/net"
)

const (
	RouteArg                    = "%s via %s dev %s metric 50"
	BackupAndDelStaticRouteFile = `if [ -f /etc/sysconfig/network-scripts/route-%s ]; then
  yes | cp /etc/sysconfig/network-scripts/route-%s /etc/sysconfig/network-scripts/.route-%s
  sed -i "/%s/d" /etc/sysconfig/network-scripts/route-%s
fi`
	AddStaticRouteFile = `cat /etc/sysconfig/network-scripts/route-%s|grep "%s" || echo "%s" >> /etc/sysconfig/network-scripts/route-%s`
)

const (
	RouteOK     = "ok"
	RouteFailed = "failed"
)

var ErrNotIPV4 = errors.New("IP addresses are not IPV4 rules")

type Route struct {
	Host    net.IP
	Gateway net.IP
}

func NewRouter(host, gateway net.IP) *Route {
	return &Route{
		Host:    host,
		Gateway: gateway,
	}
}

func CheckIsDefaultRoute(host net.IP) error {
	ok, err := isDefaultRouteIP(host)
	if err == nil && ok {
		_, err = constants.StdOut.WriteString(RouteOK)
	}
	if err == nil && !ok {
		_, err = constants.StdErr.WriteString(RouteFailed)
	}
	return err
}

// SetRoute ip route add $route
func (r *Route) SetRoute() error {
	if !k8sutilsnet.IsIPv4(r.Gateway) || !k8sutilsnet.IsIPv4(r.Host) {
		klog.Warningf("%v, skip", ErrNotIPV4)
		return nil
	}
	err := addRouteGatewayViaHost(r.Host, r.Gateway, 50)
	if err != nil && !errors.Is(err, os.ErrExist) /* return if route already exist */ {
		return fmt.Errorf("failed to add %s route gateway via host err: %v", r.Host, err)
	}

	netInterface, err := GetHostNetInterface(r.Gateway)
	if err != nil {
		return err
	}
	if netInterface != "" {
		route := fmt.Sprintf(RouteArg, r.Host, r.Gateway, netInterface)
		_, err = exec.RunSimpleCmd(fmt.Sprintf(AddStaticRouteFile, netInterface, route, route, netInterface))
		if err != nil {
			return err
		}
	}
	klog.Info(fmt.Sprintf("success to set route.(host:%s, gateway:%s)", r.Host, r.Gateway))
	return nil
}

// DelRoute ip route del $route
func (r *Route) DelRoute() error {
	if !k8sutilsnet.IsIPv4(r.Gateway) || !k8sutilsnet.IsIPv4(r.Host) {
		klog.Warningf("%v, skip", ErrNotIPV4)
		return nil
	}
	err := delRouteGatewayViaHost(r.Host, r.Gateway)
	if err != nil && !errors.Is(err, syscall.ESRCH) /* return if route does not exist */ {
		return fmt.Errorf("failed to delete %s route gateway via host: %v", r.Host, err)
	}
	netInterface, err := GetHostNetInterface(r.Gateway)
	if err != nil {
		return err
	}
	if netInterface != "" {
		route := fmt.Sprintf(RouteArg, r.Host, r.Gateway, netInterface)
		out, err := exec.RunSimpleCmd(fmt.Sprintf(BackupAndDelStaticRouteFile, netInterface, netInterface, netInterface, route, netInterface))
		if err != nil {
			klog.Info(out)
			return err
		}
	}
	klog.Info(fmt.Sprintf("success to del route(host:%s, gateway:%s)", r.Host, r.Gateway))
	return nil
}

// isDefaultRouteIP return true if host equal default route ip host.
func isDefaultRouteIP(host net.IP) (bool, error) {
	netIP, err := k8snet.ChooseHostInterface()
	if err != nil {
		return false, fmt.Errorf("failed to get default route ip, err: %v", err)
	}
	return netIP.Equal(host), nil
}

func addRouteGatewayViaHost(host, gateway net.IP, priority int) error {
	Dst := &net.IPNet{
		IP:   host,
		Mask: net.CIDRMask(32, 32),
	}
	r := &netlink.Route{
		Dst:      Dst,
		Gw:       gateway,
		Priority: priority,
	}
	return netlink.RouteAdd(r)
}

func delRouteGatewayViaHost(host, gateway net.IP) error {
	Dst := &net.IPNet{
		IP:   host,
		Mask: net.CIDRMask(32, 32),
	}
	r := &netlink.Route{
		Dst: Dst,
		Gw:  gateway,
	}
	return netlink.RouteDel(r)
}

func IsIpv4(ip string) bool {
	arr := strings.Split(ip, ".")
	if len(arr) != 4 {
		return false
	}
	for _, v := range arr {
		if v == "" {
			return false
		}
		if len(v) > 1 && v[0] == '0' {
			return false
		}
		num := 0
		for _, c := range v {
			if c >= '0' && c <= '9' {
				num = num*10 + int(c-'0')
			} else {
				return false
			}
		}
		if num > 255 {
			return false
		}
	}
	return true
}
