package net

import (
	"net"
	"reflect"
	"testing"

	"k8s.io/klog/v2"

	"github.com/stretchr/testify/assert"
)

func TestAssemblyIPList(t *testing.T) {
	tests := []struct {
		name    string
		ipStr   string
		wantErr bool
	}{
		{
			name:    "baseData1",
			ipStr:   "************-************",
			wantErr: false,
		},
		/*{
			name:    "baseData2",
			ipStr:   "0.0.0.0-*******",
			wantErr: false,
		},*/
		{
			name:    "errorData",
			ipStr:   "************0-************",
			wantErr: true,
		},
		{
			name:    "errorData2",
			ipStr:   "************0-************-************5",
			wantErr: true,
		},
		{
			name:    "errorData3",
			ipStr:   "-10.110.101.",
			wantErr: true,
		},
		{
			name:    "errorData4",
			ipStr:   "************-",
			wantErr: true,
		},
		{
			name:    "errorData5",
			ipStr:   "a-b",
			wantErr: true,
		},
		{
			name:    "errorData6",
			ipStr:   "-b",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			klog.Infof("start to test case %s", tt.name)
			resultIPStr, err := TransferToIPList(tt.ipStr)
			if err != nil && tt.wantErr == false {
				t.Errorf("input ipStr(%s), found non-nil error(%v), but expect nil error. returned ipStr(%s)", tt.ipStr, err, resultIPStr)
			}
			if err == nil && tt.wantErr == true {
				t.Errorf("input ipStr(%s), found nil error, but expect non-nil error,returned ipStr(%s)", tt.ipStr, resultIPStr)
			}
		})
	}
}

func TestIPStrsToIPs(t *testing.T) {
	tests := []struct {
		name        string
		inputIPStrs []string
		wantedIPs   []net.IP
	}{
		{
			name:        "baseData1",
			inputIPStrs: []string{"************"},
			wantedIPs:   []net.IP{net.ParseIP("************")},
		},
		{
			name:        "baseData2",
			inputIPStrs: []string{"************", "sdfghjkl"},
			wantedIPs:   []net.IP{net.ParseIP("************"), nil},
		},
		{
			name:        "baseData2",
			inputIPStrs: []string{"************", "**************"},
			wantedIPs:   []net.IP{net.ParseIP("************"), net.ParseIP("**************")},
		},
		{
			name:        "empty input of nil",
			inputIPStrs: nil,
			wantedIPs:   nil,
		},
		{
			name:        "non-empty input with empty string",
			inputIPStrs: []string{""},
			wantedIPs:   []net.IP{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			klog.Infof("start to test case %s", tt.name)
			ips := IPStrsToIPs(tt.inputIPStrs)
			if !equalNetIPs(ips, tt.wantedIPs) {
				t.Errorf("wanted ips is (%s), but got (%s)", tt.wantedIPs, ips)
			}
		})
	}
}

func TestIPsToIPStrs(t *testing.T) {
	tests := []struct {
		name         string
		inputIPs     []net.IP
		wantedIPStrs []string
	}{
		{
			name:         "baseData1",
			inputIPs:     []net.IP{net.ParseIP("************")},
			wantedIPStrs: []string{"************"},
		},
		{
			name:         "baseData2",
			inputIPs:     []net.IP{net.ParseIP("************"), net.ParseIP("************")},
			wantedIPStrs: []string{"************", "************"},
		},
		{
			name:         "baseData3",
			inputIPs:     []net.IP{net.ParseIP("************"), net.ParseIP("10.110.101.653")},
			wantedIPStrs: []string{"************", "<nil>"},
		},
		{
			name:         "empty input of nil",
			inputIPs:     nil,
			wantedIPStrs: nil,
		},
		{
			name:         "non-empty input with empty string",
			inputIPs:     []net.IP{nil},
			wantedIPStrs: []string{"<nil>"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			klog.Infof("start to test case %s", tt.name)
			ipStrs := IPsToIPStrs(tt.inputIPs)
			if !equalIPStrs(ipStrs, tt.wantedIPStrs) {
				t.Errorf("wanted IP strings is (%s), but got (%s)", tt.wantedIPStrs, ipStrs)
			}
		})
	}
}

func Test_returnFilteredIPList(t *testing.T) {
	tests := []struct {
		name              string
		clusterIPList     []net.IP
		toBeDeletedIPList []net.IP
		IPListExpected    []net.IP
	}{
		{
			"test",
			[]net.IP{net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********")},
			[]net.IP{net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********")},
			[]net.IP{},
		},
		{
			"test1",
			[]net.IP{net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********")},
			[]net.IP{},
			[]net.IP{net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********")},
		},
		{
			"test2",
			[]net.IP{net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********")},
			[]net.IP{net.ParseIP("**********")},
			[]net.IP{net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********")},
		},
		{
			"test3",
			[]net.IP{},
			[]net.IP{net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********"), net.ParseIP("**********")},
			[]net.IP{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if res := RemoveIPs(tt.clusterIPList, tt.toBeDeletedIPList); res != nil {
				assert.Equal(t, tt.IPListExpected, res)
			}
		})
	}
}

func equalNetIPs(a, b []net.IP) bool {
	if len(a) != len(b) {
		return false
	}
	for index := range a {
		if !a[index].Equal(b[index]) {
			return false
		}
	}
	return true
}

func equalIPStrs(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for index := range a {
		if a[index] != b[index] {
			return false
		}
	}
	return true
}

func TestRemoveDuplicates(t *testing.T) {
	tests := []struct {
		input    []net.IP
		expected []net.IP
	}{
		{
			input:    []net.IP{net.ParseIP("***********"), net.ParseIP("***********"), net.ParseIP("***********")},
			expected: []net.IP{net.ParseIP("***********"), net.ParseIP("***********")},
		},
		{
			input:    []net.IP{net.ParseIP("***********"), net.ParseIP("***********"), net.ParseIP("***********")},
			expected: []net.IP{net.ParseIP("***********")},
		},
		{
			input:    []net.IP{net.ParseIP("***********"), net.ParseIP("***********"), net.ParseIP("***********")},
			expected: []net.IP{net.ParseIP("***********"), net.ParseIP("***********"), net.ParseIP("***********")},
		},
		{
			input:    []net.IP{},
			expected: nil,
		},
	}

	for _, test := range tests {
		result := RemoveDuplicates(test.input)
		if !reflect.DeepEqual(result, test.expected) {
			t.Errorf("Expected %v, but got %v", test.expected, result)
		}
	}
}
