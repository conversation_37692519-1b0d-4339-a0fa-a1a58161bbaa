package net

import (
	"bytes"
	"crypto/tls"
	"errors"
	"fmt"
	"math/big"
	"net"
	"net/http"
	"sort"
	"strings"
	"time"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"k8s.io/klog/v2"

	k8snet "k8s.io/apimachinery/pkg/util/net"
)

// ValidateNodes validates the input host args such as master and node string
func ValidateNodes(masters, workers string) error {
	var errs []string
	// validate input masters IP info
	if len(masters) != 0 {
		if err := ValidateIPStr(masters); err != nil {
			errs = append(errs, err.Error())
		}
	}
	if len(workers) != 0 {
		if err := ValidateIPStr(workers); err != nil {
			errs = append(errs, err.Error())
		}
	}

	if len(errs) == 0 {
		return nil
	}
	return fmt.Errorf(strings.Join(errs, ","))
}

func ValidateIPStr(inputStr string) error {
	if len(inputStr) == 0 {
		return fmt.Errorf("input IP info cannot be empty")
	}

	// 1. validate if it is IP range
	if strings.Contains(inputStr, "-") {
		ips := strings.Split(inputStr, "-")
		if len(ips) != 2 {
			return fmt.Errorf("input IP(%s) is range format but invalid, IP range format must be xxx.xxx.xxx.1-xxx.xxx.xxx.70", inputStr)
		}

		if net.ParseIP(ips[0]) == nil {
			return fmt.Errorf("input IP(%s) is invalid", ips[0])
		}
		if net.ParseIP(ips[1]) == nil {
			return fmt.Errorf("input IP(%s) is invalid", ips[1])
		}

		if CompareIP(ips[0], ips[1]) >= 0 {
			return fmt.Errorf("input IP(%s) must be less than input IP(%s)", ips[0], ips[1])
		}

		return nil
	}

	// 2. validate if it is IP list, like ***********,***********,***********
	for _, ip := range strings.Split(inputStr, ",") {
		if net.ParseIP(ip) == nil {
			return fmt.Errorf("input IP(%s) is invalid", ip)
		}
	}

	return nil
}

// ValidateScaleIPStr validates all the input args from scale up Or scale down command.
func ValidateScaleIPStr(masters, nodes string) error {
	var errMsg []string

	if nodes == "" && masters == "" {
		return fmt.Errorf("master and node cannot both be empty")
	}

	// validate input masters IP info
	if len(masters) != 0 {
		if err := ValidateIPStr(masters); err != nil {
			errMsg = append(errMsg, err.Error())
		}
	}

	// validate input nodes IP info
	if len(nodes) != 0 {
		if err := ValidateIPStr(nodes); err != nil {
			errMsg = append(errMsg, err.Error())
		}
	}

	if len(errMsg) == 0 {
		return nil
	}
	return fmt.Errorf(strings.Join(errMsg, ","))
}

// ParseToNetIPList now only supports input IP list and IP range.
// IP list, like ***********,***********,***********
// IP range, like ***********-***********, which means ***********,***********,***********
// P.S. we have guaranteed that all the input masters and nodes are validated.
func ParseToNetIPList(masters, workers string) ([]net.IP, []net.IP, error) {
	newMasters, err := TransferToIPList(masters)
	if err != nil {
		return nil, nil, err
	}

	newNodes, err := TransferToIPList(workers)
	if err != nil {
		return nil, nil, err
	}

	newMasterIPList := IPStrsToIPs(strings.Split(newMasters, ","))
	newNodeIPList := IPStrsToIPs(strings.Split(newNodes, ","))

	return newMasterIPList, newNodeIPList, nil
}

func GetHostNetInterface(host net.IP) (string, error) {
	netInterfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}
	for i := 0; i < len(netInterfaces); i++ {
		if (netInterfaces[i].Flags & net.FlagUp) == 0 {
			continue
		}
		addrs, err := netInterfaces[i].Addrs()
		if err != nil {
			return "", fmt.Errorf("failed to get Addrs: %v", err)
		}
		if IsLocalIP(host, addrs) {
			return netInterfaces[i].Name, nil
		}
	}
	return "", nil
}

func GetLocalHostAddresses() ([]net.Addr, error) {
	netInterfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("failed to get net.Interfaces: %v", err)
	}

	var allAddrs []net.Addr
	for i := 0; i < len(netInterfaces); i++ {
		if (netInterfaces[i].Flags & net.FlagUp) == 0 {
			continue
		}
		addrs, err := netInterfaces[i].Addrs()
		if err != nil {
			return nil, fmt.Errorf("failed to get Addrs: %v", err)
		}
		for j := 0; j < len(addrs); j++ {
			allAddrs = append(allAddrs, addrs[j])
		}
	}
	return allAddrs, nil
}

func IsLocalIP(ip net.IP, addrs []net.Addr) bool {
	if len(addrs) == 0 {
		addrs, _ = GetLocalHostAddresses()
	}

	for _, address := range addrs {
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() && ipnet.IP.Equal(ip) {
			return true
		}
	}
	return false
}

// GetNetCard Deprecated
func GetNetCard(nodes []map[string]interface{}) (string, error) {
	if nodes == nil {
		return "", errors.New("nodes info is empty")
	}
	netIP, err := GetLocalDefaultIP()
	if err != nil {
		return "", err
	}
	for _, node := range nodes {
		if node[constants.ManagementNetworkIP] == netIP {
			return node[constants.ManagementNetworkInterface].(string), nil
		}
	}
	return "", errors.New("not found default netcard")
}

// GetLocalDefaultNetCard Deprecated
func GetLocalDefaultNetCard() (string, error) {
	ip, err := k8snet.ChooseHostInterface()
	if err != nil {
		return "", fmt.Errorf("failed to get default route ip, err: %v", err)
	}
	// 获取系统所有的网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", fmt.Errorf("could not get network interfaces: %v", err)
	}

	// 遍历所有网络接口
	for _, iface := range interfaces {
		// 获取接口的所有地址
		addrs, err := iface.Addrs()
		if err != nil {
			klog.Errorf("could not get addresses for interface %s: %v", iface.Name, err)
			continue
		}

		// 遍历接口的所有地址，查找与给定 IP 匹配的接口
		for _, addr := range addrs {
			var ipNet *net.IPNet
			var ok bool

			if ipNet, ok = addr.(*net.IPNet); !ok {
				continue
			}

			// 检查地址是否与传入的 IP 匹配
			if ipNet.IP.Equal(ip) {
				return iface.Name, nil
			}
		}
	}

	return "", fmt.Errorf("no interface found with IP address %s", ip.String())
}

func GetLocalDefaultIP() (string, error) {
	netIP, err := k8snet.ChooseHostInterface()
	if err != nil {
		return "", fmt.Errorf("failed to get default route ip, err: %v", err)
	}
	return netIP.String(), nil
}

func GetLocalIP(master0IPPort string) (net.IP, error) {
	conn, err := net.Dial("udp", master0IPPort)
	if err != nil {
		return nil, err
	}
	localAddr := conn.LocalAddr().String()
	return net.ParseIP(strings.Split(localAddr, ":")[0]), err
}

// TransferToIPList transfer network segment string to ip list string
func TransferToIPList(ipStr string) (string, error) {
	var result []string
	var ips = strings.Split(ipStr, "-")
	if ipStr == "" || !strings.Contains(ipStr, "-") {
		return ipStr, nil
	}
	if len(ips) != 2 {
		return "", fmt.Errorf("input IP(%s) is invalid, IP range format must be xxx.xxx.xxx.1-xxx.xxx.xxx.2", ipStr)
	}
	if returnedIP := net.ParseIP(ips[0]); returnedIP == nil {
		return "", fmt.Errorf("failed tp parse IP(%s)", ips[0])
	}
	if returnedIP := net.ParseIP(ips[1]); returnedIP == nil {
		return "", fmt.Errorf("failed tp parse IP(%s)", ips[1])
	}

	// ips[0],ips[1] = ************, ************;  result = [************, ************, ************, ************, ************]
	for res := CompareIP(ips[0], ips[1]); res <= 0; {
		result = append(result, ips[0])
		ips[0] = NextIP(ips[0]).String()
		res = CompareIP(ips[0], ips[1])
	}
	if len(result) == 0 {
		return "", fmt.Errorf("input IP(%s) is invalid", ipStr)
	}
	return strings.Join(result, ","), nil
}

func IPToInt(v string) *big.Int {
	ip := net.ParseIP(v).To4()
	if val := ip.To4(); val != nil {
		return big.NewInt(0).SetBytes(val)
	}
	return big.NewInt(0).SetBytes(ip.To16())
}

func CompareIP(v1, v2 string) int {
	i := IPToInt(v1)
	j := IPToInt(v2)

	if i == nil {
		return 2
	}
	if j == nil {
		return 2
	}

	return i.Cmp(j)
}

func NextIP(ip string) net.IP {
	i := IPToInt(ip)
	return i.Add(i, big.NewInt(1)).Bytes()
}

// WaitVipRunning HTTP probe
func WaitVipRunning(url string) (bool, error) {
	client := &http.Client{Transport: &http.Transport{
		DisableKeepAlives: false,
		TLSClientConfig:   &tls.Config{InsecureSkipVerify: true},
	},
		Timeout: 2 * time.Second,
	}

	return probe(client, url)
}

// detect node's health by api
func probe(client *http.Client, url string) (bool, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return false, err
	}
	resp, err := client.Do(req)
	if err != nil {
		return false, nil
	}
	defer func() { _ = resp.Body.Close() }()
	return resp.StatusCode == 200, nil
}

func SortIPList(iplist []string) {
	realIPs := make([]net.IP, 0, len(iplist))
	for _, ip := range iplist {
		realIPs = append(realIPs, net.ParseIP(ip))
	}

	sort.Slice(realIPs, func(i, j int) bool {
		return bytes.Compare(realIPs[i], realIPs[j]) < 0
	})

	for i := range realIPs {
		iplist[i] = realIPs[i].String()
	}
}

func IsInIPList(key net.IP, slice []net.IP) bool {
	for _, s := range slice {
		if s.Equal(key) {
			return true
		}
	}
	return false
}

func IPStrsToIPs(ipStrs []string) []net.IP {
	if ipStrs == nil {
		return nil
	}

	var result []net.IP
	for _, ipStr := range ipStrs {
		if ipStr == "" {
			continue
		}
		result = append(result, net.ParseIP(ipStr))
	}
	return result
}

func IPsToIPStrs(ips []net.IP) []string {
	if ips == nil {
		return nil
	}

	var result []string
	for _, ip := range ips {
		result = append(result, ip.String())
	}
	return result
}

func RemoveIPs(clusterIPList []net.IP, toBeDeletedIPList []net.IP) (res []net.IP) {
	for _, ip := range clusterIPList {
		if !IsInIPList(ip, toBeDeletedIPList) {
			res = append(res, ip)
		}
	}
	return
}

func GetIndexIP(subnet *net.IPNet, index int) (string, error) {
	bip := big.NewInt(0).SetBytes(subnet.IP.To4())
	if subnet.IP.To4() == nil {
		bip = big.NewInt(0).SetBytes(subnet.IP.To16())
	}
	ip := net.IP(big.NewInt(0).Add(bip, big.NewInt(int64(index))).Bytes())
	if subnet.Contains(ip) {
		return ip.String(), nil
	}

	return "", fmt.Errorf("can't generate IP with index %d from subnet. subnet too small. subnet: %q", index, subnet)
}

// TransferIPToHosts constructs v1beta1.Host through []net.ip
func TransferIPToHosts(masterIPList, nodeIPList []net.IP, sshAuthOnHosts ctv1beta1.SSH) []ctv1beta1.Host {
	var hosts []ctv1beta1.Host
	if len(masterIPList) != 0 {
		hosts = append(hosts, ctv1beta1.Host{
			Roles: []string{consts.MASTER},
			IPS:   masterIPList,
			SSH:   sshAuthOnHosts,
		})
	}

	if len(nodeIPList) != 0 {
		hosts = append(hosts, ctv1beta1.Host{
			Roles: []string{consts.NODE},
			IPS:   nodeIPList,
			SSH:   sshAuthOnHosts,
		})
	}

	return hosts
}

// RemoveDuplicates IP去重
func RemoveDuplicates(res []net.IP) []net.IP {
	if len(res) == 0 {
		return nil
	}
	var uniqueIPs []net.IP
	ipmap := make(map[string]struct{})
	for _, r := range res {
		str := r.String()
		if _, exits := ipmap[str]; !exits {
			ipmap[str] = struct{}{}
			uniqueIPs = append(uniqueIPs, r)
		}
	}

	return uniqueIPs
}
