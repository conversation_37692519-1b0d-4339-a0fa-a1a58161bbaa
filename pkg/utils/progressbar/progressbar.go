package progressbar

import (
	"github.com/schollz/progressbar/v3"
	"k8s.io/klog/v2"
)

// Client 进度条
type Client struct {
	progressbar.ProgressBar
}

var (
	width                  = 50
	optionEnableColorCodes = progressbar.OptionEnableColorCodes(true)
	optionSetWidth         = progressbar.OptionSetWidth(width)
	optionShowCount        = progressbar.OptionShowCount()
	OptionShowIts          = progressbar.OptionShowIts()
	optionSetTheme         = progressbar.OptionSetTheme(progressbar.Theme{
		Saucer:        "=",
		SaucerHead:    ">",
		SaucerPadding: " ",
		BarStart:      "[",
		BarEnd:        "]",
	})
)

// NewProgressBarClient 创建进度条:
// [start copy files to *************]  94% [==============================================>   ] (18/19, 6 it/s) [3s:0s]
func NewProgressBarClient(total int, describe string) *Client {
	return &Client{
		*progressbar.NewOptions(total,
			optionEnableColorCodes,
			optionSetWidth,
			optionSetTheme,
			optionShowCount,
			OptionShowIts,
			progressbar.OptionSetDescription(describe),
		),
	}
}

// Increment add 1 to progress bar
func (c *Client) Increment() {
	if err := c.Add(1); err != nil {
		klog.Errorf("failed to increment progress bar, err: %s", err)
	}
}

// Fail print error message
func (c *Client) Fail(err error) {
	if err != nil {
		c.Describe(err.Error())
	}
}

// Refresh make progress bar refresh
// NB: We have to do this when progress bar is finished, but we want to reuse it
func (c *Client) Refresh() {
	// save current
	current := c.ProgressBar.State().CurrentBytes
	c.Reset()
	if err := c.Set(int(current)); err != nil {
		klog.Errorf("failed to refresh progress bar, err: %s", err)
	}
}

// SetTotal set total num of progress bar
func (c *Client) SetTotal(num int) {
	if num > c.GetMax() {
		c.ChangeMax(num)
		c.Refresh()
	}
}
