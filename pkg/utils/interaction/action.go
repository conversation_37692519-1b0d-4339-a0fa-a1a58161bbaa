package interaction

import (
	"errors"
	"regexp"

	"k8s.io/klog/v2"

	"github.com/manifoldco/promptui"
)

// Confirm is send the prompt and get result
func Confirm(prompt, cancel string) (bool, error) {
	var yesRx = regexp.MustCompile("^(?:y(?:es)?)$")
	var noRx = regexp.MustCompile("^(?:n(?:o)?)$")
	promptLabel := "Yes [y/yes], No [n/no]"
	klog.Info(prompt)

	validate := func(input string) error {
		if !yesRx.MatchString(input) && !noRx.MatchString(input) {
			return errors.New("invalid input, please enter 'y', 'yes', 'n', or 'no'")
		}
		return nil
	}

	promptObj := promptui.Prompt{
		Label:    promptLabel,
		Validate: validate,
	}

	result, err := promptObj.Run()
	if err != nil {
		return false, err
	}

	if yesRx.MatchString(result) {
		return true, nil
	}
	klog.Warningf(cancel)
	return false, nil
}

func PasswordInput(promptInput string) string {
	validate := func(input string) error {
		if len(input) < 6 {
			return errors.New("password must have more than 6 characters")
		}
		return nil
	}
	if promptInput == "" {
		promptInput = "Please input password"
	}
	prompt := promptui.Prompt{
		Label:    promptInput,
		Validate: validate,
		Mask:     '*',
	}

	result, err := prompt.Run()

	if err != nil {
		klog.Errorf("Prompt failed %v\n", err)
		return ""
	}

	return result
}

func SelectInput(promptInput string, items []string) string {
	if promptInput == "" {
		promptInput = "Please select"
	}
	if len(items) == 0 {
		klog.Error("select items is empty")
		return ""
	}
	prompt := promptui.Select{
		Label: promptInput,
		Items: items,
	}
	_, result, err := prompt.Run()
	if err != nil {
		klog.Errorf("Prompt failed %v\n", err)
		return ""
	}

	return result
}

func Input(promptInput, defaultValue string, validate func(input string) error) string {
	prompt := promptui.Prompt{
		Label:    promptInput,
		Validate: validate,
		Default:  defaultValue,
	}
	result, err := prompt.Run()
	if err != nil {
		klog.Errorf("Prompt failed %v\n", err)
		return ""
	}
	return result
}
