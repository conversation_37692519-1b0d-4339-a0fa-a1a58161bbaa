package rand

import (
	"bytes"
	"crypto/rand"
	"math/big"
)

// GenerateRandomName generate rand name
func GenerateRandomName(len int) string {
	var name string
	var str = "abcdefghijklmnopqrstuvwxyz1234567890"
	b := bytes.NewBufferString(str)
	length := b.Len()
	bigInt := big.NewInt(int64(length))
	for i := 0; i < len; i++ {
		randomInt, _ := rand.Int(rand.Reader, bigInt)
		name += string(str[randomInt.Int64()])
	}
	return name
}
