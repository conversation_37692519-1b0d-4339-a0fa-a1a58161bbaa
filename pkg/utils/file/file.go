package file

import (
	"archive/tar"
	"bufio"
	"bytes"
	"compress/gzip"
	"errors"
	"fmt"
	"io"
	"os"
	"path"
	"path/filepath"
	"reflect"
	"regexp"
	"strings"
	"syscall"

	"github.com/mitchellh/go-homedir"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
	"golang.org/x/sys/unix"
	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"
)

// GetKubepilotrWorkDir default dir is ~/.kubepilot
func GetKubepilotrWorkDir() string {
	return filepath.Join(GetHomeDir(), ".kubepilot")
}

// GetDefaultClusterfile default file is ~/.kubepilot/Clusterfile
// Please note the capital letters, prevent random changes
func GetDefaultClusterfile() string {
	return filepath.Join(GetKubepilotrWorkDir(), "Clusterfile")
}

// GetDefaultApplicationFile application.json文件路径
func GetDefaultApplicationFile() string {
	return filepath.Join(GetKubepilotrWorkDir(), "application.json")
}

func DefaultRegistryAuthConfigDir() string {
	return filepath.Join(GetHomeDir(), ".docker/config.json")
}

// DefaultKubeConfigDir ~/.kube/
func DefaultKubeConfigDir() string {
	return filepath.Join(GetHomeDir(), ".kube")
}

func DefaultAuthFilePath() string {
	dir, err := homedir.Dir()
	if err != nil {
		dir = "/root"
	}
	return filepath.Join(dir, ".kubepilot/auth.json")
}

// MkDirs creates directories.
func MkDirs(dirs ...string) error {
	if len(dirs) == 0 {
		return nil
	}
	for _, dir := range dirs {
		err := os.MkdirAll(dir, 0755)
		if err != nil {
			return fmt.Errorf("unable to create %s, %v", dir, err)
		}
	}
	return nil
}

// GetHomeDir returnm Root directory
func GetHomeDir() string {
	home, err := homedir.Dir()
	if err != nil {
		return "/root"
	}
	return home
}

const compressionBufSize = 32768

type Options struct {
	Compress    bool
	KeepRootDir bool
	ToStream    bool
}

func validatePath(path string) error {
	if _, err := os.Stat(path); err != nil {
		return fmt.Errorf("dir %s does not exist, err: %s", path, err)
	}
	return nil
}

// TarWithRootDir
// src is the dir or single file to tar
// not contain the dir
// newFolder is a folder for tar file
func TarWithRootDir(paths ...string) (readCloser io.ReadCloser, err error) {
	return compress(paths, Options{Compress: false, KeepRootDir: true})
}

// TarWithoutRootDir function will tar files, but without keeping the original dir
// this is useful when we tar files at the build stage
func TarWithoutRootDir(paths ...string) (readCloser io.ReadCloser, err error) {
	return compress(paths, Options{Compress: false, KeepRootDir: false})
}

func Untar(src io.Reader, dst string) (int64, error) {
	return Decompress(src, dst, Options{Compress: false})
}

// GzipCompress make the tar stream to be gzip stream.
func GzipCompress(in io.Reader) (io.ReadCloser, chan struct{}) {
	compressionDone := make(chan struct{})

	pipeReader, pipeWriter := io.Pipe()
	// Use a bufio.Writer to avoid excessive chunking in HTTP request.
	bufWriter := bufio.NewWriterSize(pipeWriter, compressionBufSize)
	compressor := gzip.NewWriter(bufWriter)

	go func() {
		_, err := io.Copy(compressor, in)
		if err == nil {
			err = compressor.Close()
		}
		if err == nil {
			err = bufWriter.Flush()
		}
		if err != nil {
			// leave this err
			_ = pipeWriter.CloseWithError(err)
		} else {
			if err = pipeWriter.Close(); err != nil {
				return
			}
		}
		close(compressionDone)
	}()

	return pipeReader, compressionDone
}

func compress(paths []string, options Options) (reader io.ReadCloser, err error) {
	if len(paths) == 0 {
		return nil, errors.New("paths is empty")
	}
	for _, p := range paths {
		err = validatePath(p)
		if err != nil {
			return nil, err
		}
	}

	pr, pw := io.Pipe()
	tw := tar.NewWriter(pw)
	bufWriter := bufio.NewWriterSize(nil, compressionBufSize)
	if options.Compress {
		tw = tar.NewWriter(gzip.NewWriter(pw))
	}
	go func() {
		defer func() {
			if err = tw.Close(); err != nil {
				return
			}
			if err = pw.Close(); err != nil {
				return
			}
		}()

		for _, p := range paths {
			if err = writeToTarWriter(p, tw, bufWriter, options); err != nil {
				_ = pw.CloseWithError(err)
			}
		}
	}()

	return pr, nil
}

func writeWhiteout(header *tar.Header, fi os.FileInfo, path string) *tar.Header {
	// overlay whiteout process
	// this is a whiteout file
	if fi.Mode()&os.ModeCharDevice != 0 && header.Devminor == 0 && header.Devmajor == 0 {
		hName := header.Name
		header.Name = filepath.Join(filepath.Dir(hName), constants.WhiteoutPrefix+filepath.Base(hName))
		header.Mode = 0600
		header.Typeflag = tar.TypeReg
		header.Size = 0
	}

	var woh *tar.Header
	if fi.Mode()&os.ModeDir != 0 {
		opaque, walkErr := Lgetxattr(path, "trusted.overlay.opaque")
		if walkErr != nil {
			klog.Warningf("failed to get trusted.overlay.opaque for %s at opaque, err: %v", path, walkErr)
		}

		if len(opaque) == 1 && opaque[0] == 'y' {
			if header.PAXRecords != nil {
				delete(header.PAXRecords, "trusted.overlay.opaque")
			}

			woh = &tar.Header{
				Typeflag:   tar.TypeReg,
				Mode:       header.Mode & int64(os.ModePerm),
				Name:       filepath.Join(header.Name, constants.WhiteoutOpaqueDir),
				Size:       0,
				Uid:        header.Uid,
				Uname:      header.Uname,
				Gid:        header.Gid,
				Gname:      header.Gname,
				AccessTime: header.AccessTime,
				ChangeTime: header.ChangeTime,
			}
		}
	}
	return woh
}

func readWhiteout(hdr *tar.Header, path string) (bool, error) {
	var (
		base = filepath.Base(path)
		dir  = filepath.Dir(path)
		err  error
	)

	switch {
	case base == constants.WhiteoutOpaqueDir:
		err = unix.Setxattr(dir, "trusted.overlay.opaque", []byte{'y'}, 0)
		return false, err
	case strings.HasPrefix(base, constants.WhiteoutPrefix):
		oBase := base[len(constants.WhiteoutPrefix):]
		oPath := filepath.Join(dir, oBase)

		// make a whiteout file
		if err = unix.Mknod(oPath, unix.S_IFCHR, 0); err != nil {
			return false, err
		}
		return false, os.Chown(oPath, hdr.Uid, hdr.Gid)
	}

	return true, nil
}

func writeToTarWriter(path string, tarWriter *tar.Writer, bufWriter *bufio.Writer, options Options) error {
	var newFolder string
	if options.KeepRootDir {
		fi, err := os.Stat(path)
		if err != nil {
			return err
		}
		if fi.IsDir() {
			newFolder = filepath.Base(path)
		}
	}

	dir := strings.TrimSuffix(path, "/")
	srcPrefix := filepath.ToSlash(dir + "/")

	return filepath.Walk(dir, func(file string, fi os.FileInfo, err error) error {
		// generate tar header
		header, walkErr := tar.FileInfoHeader(fi, file)
		if walkErr != nil {
			return walkErr
		}
		// root dir
		if file != dir {
			absPath := filepath.ToSlash(file)
			header.Name = filepath.Join(newFolder, strings.TrimPrefix(absPath, srcPrefix))
		} else {
			// do not contain root dir
			if fi.IsDir() {
				return nil
			}
			// for supporting tar single file
			header.Name = filepath.Join(newFolder, filepath.Base(dir))
		}
		// if current file is whiteout, the header has been changed,
		// and we write a reg header into tar stream, but will not read its content
		// cause doing so will lead to error. (its size is 0)

		// if current target is dir, we will check if it is an opaque.
		// and set add Suffix WhiteoutOpaqueDir for opaque.
		// but we still need to write its original header into tar stream,
		// because we need to create dir on this original header.
		woh := writeWhiteout(header, fi, file)
		walkErr = tarWriter.WriteHeader(header)
		if walkErr != nil {
			return fmt.Errorf("failed to write original header, path: %s, err: %v", file, walkErr)
		}
		// this is an opaque, write the opaque header, in order to set header.PAXRecords with trusted.overlay.opaque:y
		// when decompress the tar stream.
		if woh != nil {
			walkErr = tarWriter.WriteHeader(woh)
			if walkErr != nil {
				return fmt.Errorf("failed to write opaque header, path: %s, err: %v", file, walkErr)
			}
		}
		// if not a dir && size > 0, write file content
		// the whiteout size is 0
		if header.Typeflag == tar.TypeReg && header.Size > 0 {
			var fHandler *os.File
			fHandler, walkErr = os.Open(filepath.Clean(file))
			if walkErr != nil {
				return walkErr
			}
			defer func() {
				if err = fHandler.Close(); err != nil {
					klog.Errorf("failed to close file: %v", err)
				}
			}()
			bufWriter.Reset(tarWriter)
			defer bufWriter.Reset(nil)

			_, walkErr = io.Copy(bufWriter, fHandler)
			if walkErr != nil {
				return walkErr
			}

			walkErr = bufWriter.Flush()
			if walkErr != nil {
				return walkErr
			}
		}
		return nil
	})
}

func removePreviousFiles(path string) error {
	dir := filepath.Dir(path)
	existPath := path
	if base := filepath.Base(path); strings.HasPrefix(base, constants.WhiteoutPrefix) {
		existPath = filepath.Join(dir, strings.TrimPrefix(base, constants.WhiteoutPrefix))
	}

	if _, err := os.Stat(existPath); err == nil {
		if err = os.RemoveAll(existPath); err != nil {
			return err
		}
	}
	return nil
}

// Decompress 解压tar文件，但不会改变原文件的元数据
func Decompress(src io.Reader, dst string, options Options) (int64, error) {
	// need to set umask to be 000 for current process.
	// there will be some files having higher permission like 777,
	// eventually permission will be set to 755 when umask is 022.
	oldMask := syscall.Umask(0)
	defer syscall.Umask(oldMask)

	err := os.MkdirAll(dst, constants.FileMode0755)
	if err != nil {
		return 0, err
	}

	reader := src
	if options.Compress {
		reader, err = gzip.NewReader(src)
		if err != nil {
			return 0, err
		}
	}

	var (
		size int64
		dirs []*tar.Header
		tr   = tar.NewReader(reader)
	)
	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return 0, err
		}
		size += header.Size
		// validate name against path traversal
		if !validRelPath(header.Name) {
			return 0, fmt.Errorf("tar contained invalid name error %q", header.Name)
		}

		// #nosec
		target := filepath.Join(dst, header.Name)
		if err = removePreviousFiles(target); err != nil {
			return 0, err
		}

		goon, err := readWhiteout(header, target)
		if err != nil {
			return 0, err
		}
		// it is an opaque / whiteout, don't write its file content.
		if !goon {
			continue
		}

		switch header.Typeflag {
		case tar.TypeDir:
			if _, err = os.Stat(target); err != nil {
				if err = os.MkdirAll(target, os.FileMode(header.Mode)); err != nil {
					return 0, err
				}
				dirs = append(dirs, header)
			}

		case tar.TypeReg:
			if err = func() error {
				// regularly won't mkdir, unless add newFolder on compressing
				inErr := os.MkdirAll(filepath.Dir(target), 0700|0055)
				if inErr != nil {
					return inErr
				}
				// #nosec
				fileToWrite, inErr := os.OpenFile(target, os.O_CREATE|os.O_TRUNC|os.O_RDWR, os.FileMode(header.Mode))
				if inErr != nil {
					return inErr
				}

				defer func() {
					if err := fileToWrite.Close(); err != nil {
						klog.Errorf("failed to close file: %v", err)
					}
				}()
				if _, inErr = io.Copy(fileToWrite, tr); inErr != nil {
					return inErr
				}
				// for not changing
				return os.Chtimes(target, header.AccessTime, header.ModTime)
			}(); err != nil {
				return 0, err
			}
		}
	}

	for _, h := range dirs {
		// #nosec
		p := filepath.Join(dst, h.Name)
		if err = os.Chtimes(p, h.AccessTime, h.ModTime); err != nil {
			return 0, err
		}
	}

	return size, nil
}

// check for path traversal and correct forward slashes
func validRelPath(p string) bool {
	if p == "" || strings.Contains(p, `\`) || strings.HasPrefix(p, "/") || strings.Contains(p, "../") {
		return false
	}
	return true
}

// Lgetxattr retrieves the value of the extended attribute identified by attr and associated with the given path in the file system.
func Lgetxattr(path string, attr string) ([]byte, error) {
	// Start with a 128 length byte array
	dest := make([]byte, 128)
	sz, errno := unix.Lgetxattr(path, attr, dest)

	switch {
	case errno == unix.ENODATA:
		return nil, nil
	case errno == unix.ERANGE:
		// 128 byte array might just not be good enough. A dummy buffer is used
		// to get the real size of the xattrs on disk
		sz, errno = unix.Lgetxattr(path, attr, []byte{})
		if errno != nil {
			return nil, errno
		}
		dest = make([]byte, sz)
		sz, errno = unix.Lgetxattr(path, attr, dest)
		if errno != nil {
			return nil, errno
		}
	case errno != nil:
		return nil, errno
	}
	return dest[:sz], nil
}

func IsFileExist(fileName string) bool {
	_, err := os.Stat(fileName)
	return err == nil || os.IsExist(err)
}

func CountDirFiles(dirName string) int {
	var count int
	err := filepath.Walk(dirName, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		count++
		return nil
	})
	if err != nil {
		klog.Warningf("failed to count dir files: %v", err)
		return 0
	}
	return count
}

func RecursionCopy(src, dst string) error {
	f := NewFileSystem()
	if IsDir(src) {
		return f.CopyDir(src, dst)
	}
	err := f.MkdirAll(filepath.Dir(dst))
	if err != nil {
		return fmt.Errorf("failed to mkdir for recursion copy, err: %v", err)
	}

	_, err = f.CopyFile(src, dst)
	return err
}

func IsDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}

type FilterOptions struct {
	All, OnlyDir, OnlyFile, WithFullPath bool
}

// GetDirNameListInDir :Get all Dir Name or file name List In Dir
func GetDirNameListInDir(dir string, opts FilterOptions) ([]string, error) {
	files, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}
	var dirs []string

	if opts.All {
		for _, file := range files {
			if opts.WithFullPath {
				dirs = append(dirs, filepath.Join(dir, file.Name()))
			} else {
				dirs = append(dirs, file.Name())
			}
		}
		return dirs, nil
	}

	if opts.OnlyDir {
		for _, file := range files {
			if !file.IsDir() {
				continue
			}
			if opts.WithFullPath {
				dirs = append(dirs, filepath.Join(dir, file.Name()))
			} else {
				dirs = append(dirs, file.Name())
			}
		}
		return dirs, nil
	}

	if opts.OnlyFile {
		for _, file := range files {
			if file.IsDir() {
				continue
			}
			if opts.WithFullPath {
				dirs = append(dirs, filepath.Join(dir, file.Name()))
			} else {
				dirs = append(dirs, file.Name())
			}
		}
		return dirs, nil
	}

	return dirs, nil
}

func IsAbs(hostPath string) bool {
	return path.IsAbs(hostPath) || filepath.IsAbs(hostPath)
}

func UnmarshalFile(file string, obj interface{}) error {
	data, err := os.ReadFile(filepath.Clean(file))
	if err != nil {
		return err
	}

	if err = yaml.Unmarshal(data, obj); err != nil {
		return fmt.Errorf("failed to unmarshal file %s to %s: %v", file, reflect.TypeOf(obj), err)
	}
	return nil
}

func MarshalToFile(file string, obj interface{}) error {
	data, err := yaml.Marshal(obj)
	if err != nil {
		return err
	}

	return rw.NewAtomicWriter(file).WriteFile(data)
}

func MarshalWithDelimiter(configs ...interface{}) ([]byte, error) {
	var cfgs [][]byte
	for _, cfg := range configs {
		data, err := yaml.Marshal(cfg)
		if err != nil {
			return nil, err
		}
		cfgs = append(cfgs, data)
	}
	return bytes.Join(cfgs, []byte("\n---\n")), nil
}

func Matcher(path string) bool {
	ext := strings.ToLower(filepath.Ext(path))
	return ext == ".yaml" || ext == ".yml"
}

// GrepFile like command grep -E
// for example: GrepFile(`^hello`, "hello.txt")
// \n is striped while read
func GrepFile(patten string, filename string) (lines []string, err error) {
	re, err := regexp.Compile(patten)
	if err != nil {
		return
	}

	fd, err := os.Open(filename)
	if err != nil {
		return
	}
	lines = make([]string, 0)
	reader := bufio.NewReader(fd)
	prefix := ""
	var isLongLine bool
	for {
		byteLine, isPrefix, er := reader.ReadLine()
		if er != nil && er != io.EOF {
			return nil, er
		}
		if er == io.EOF {
			break
		}
		line := string(byteLine)
		if isPrefix {
			prefix += line
			continue
		} else {
			isLongLine = true
		}

		line = prefix + line
		if isLongLine {
			prefix = ""
		}
		if re.MatchString(line) {
			lines = append(lines, line)
		}
	}
	return lines, nil
}

// GrepNonCommentLines 从指定文件中筛选出非注释行。
// GrepNonCommentLines 是 GrepFile 的语法糖，patten="^\s*[^#]+"
func GrepNonCommentLines(filename string) ([]string, error) {
	lines, err := GrepFile(`^\s*[^#]+`, filename)
	if err != nil {
		return nil, err
	}

	return lines, nil
}
