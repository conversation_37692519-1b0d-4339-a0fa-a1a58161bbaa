package utils

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"os"
	"path"
	"strings"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	confv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/config/v1beta1"
	pgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/plugin/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"k8s.io/klog/v2"
	v2 "sigs.k8s.io/controller-runtime/pkg/webhook/conversion/testdata/api/v2"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/kube-proxy/config/v1alpha1"
	"k8s.io/kubelet/config/v1beta1"
	"k8s.io/kubernetes/cmd/kubeadm/app/apis/kubeadm/v1beta3"
	kubeadmConstants "k8s.io/kubernetes/cmd/kubeadm/app/constants"
)

var decodeCRDFuncMap = map[string]func(reader io.Reader) (interface{}, error){
	constants.ClusterKind:                     decodeClusterFunc,
	constants.ConfigKind:                      decodeConfigListFunc,
	constants.PluginKind:                      decodePluginListFunc,
	kubeadmConstants.InitConfigurationKind:    decodeInitConfigurationFunc,
	kubeadmConstants.JoinConfigurationKind:    decodeJoinConfigurationFunc,
	kubeadmConstants.ClusterConfigurationKind: decodeClusterConfigurationFunc,
	constants.KubeletConfiguration:            decodeKubeletConfigurationFunc,
	constants.KubeProxyConfiguration:          decodeKubeProxyConfigurationFunc,
}

// DecodeCRDFromFile decode custom resource definition from file, if not found, return io.EOF error.
func DecodeCRDFromFile(filepath string, kind string) (interface{}, error) {
	file, err := os.Open(path.Clean(filepath))
	if err != nil {
		return nil, fmt.Errorf("failed to open configfile(%s): %v", filepath, err)
	}
	defer func() {
		if err = file.Close(); err != nil {
			klog.Warningf("failed to dump config close clusterfile: %v", err)
		}
	}()
	return decodeCRDFuncMap[kind](file)
}

// DecodeCRDFromByte decode custom resource definition from byte slice, if not found, return io.EOF error.
func DecodeCRDFromByte(data []byte, kind string) (interface{}, error) {
	return decodeCRDFuncMap[kind](bytes.NewReader(data))
}

// DecodeCRDFromString decode custom resource definition from string, if not found, return io.EOF error.
func DecodeCRDFromString(data string, kind string) (interface{}, error) {
	return decodeCRDFuncMap[kind](strings.NewReader(data))
}

func NewK8sYamlDecoder(reader io.Reader) *yaml.YAMLToJSONDecoder {
	return yaml.NewYAMLToJSONDecoder(bufio.NewReaderSize(reader, 4096))
}

func decodeCRDFromReader(decoder *yaml.YAMLToJSONDecoder, kind string,
	unmarshalType func(version string) interface{} /*Get different constructs based on version and parse them.*/) (interface{}, error) {
	for {
		ext := runtime.RawExtension{}
		if err := decoder.Decode(&ext); err != nil {
			return nil, err
		}
		// TODO: This needs to be able to handle object in other encodings and schemas.
		ext.Raw = bytes.TrimSpace(ext.Raw)
		if len(ext.Raw) == 0 || bytes.Equal(ext.Raw, []byte("null")) {
			continue
		}
		metaType := metav1.TypeMeta{}
		if err := yaml.Unmarshal(ext.Raw, &metaType); err != nil {
			return nil, fmt.Errorf("failed to decode cluster: %v", err)
		}
		if metaType.Kind != kind {
			continue
		}
		in := unmarshalType(metaType.APIVersion)
		if err := yaml.Unmarshal(ext.Raw, in); err != nil {
			return nil, fmt.Errorf("failed to decode %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
		}
		return in, nil
	}
}

func DecodeV1ClusterFromFile(filepath string) (*ctv1beta1.Cluster, error) {
	file, err := os.Open(path.Clean(filepath))
	if err != nil {
		return nil, fmt.Errorf("failed to dump config: %v", err)
	}
	defer func() {
		if err = file.Close(); err != nil {
			klog.Warningf("failed to dump config close clusterfile: %v", err)
		}
	}()

	cluster, err := decodeCRDFromReader(NewK8sYamlDecoder(file), constants.ClusterKind, func(version string) interface{} { return &ctv1beta1.Cluster{} })
	return cluster.(*ctv1beta1.Cluster), err
}

func decodeClusterFunc(reader io.Reader) (out interface{}, err error) {
	switchVersion := func(version string) interface{} {
		switch version {
		case ctv1beta1.SchemeGroupVersion.String():
			return &ctv1beta1.Cluster{}
		case v2.GroupVersion.String():
			return &ctv1beta1.Cluster{}
		default:
			return &ctv1beta1.Cluster{}
		}
	}
	out, err = decodeCRDFromReader(NewK8sYamlDecoder(reader), constants.ClusterKind, switchVersion)
	if err != nil {
		return nil, err
	}
	return
}

// ConvertV1ClusterToV2Cluster
// Deprecated
func ConvertV1ClusterToV2Cluster(v1Cluster *ctv1beta1.Cluster) *ctv1beta1.Cluster {
	var (
		hosts   []ctv1beta1.Host
		cluster = &ctv1beta1.Cluster{}
	)
	// if len(v1Cluster.Spec.Masters.IPList) != 0 {
	// 	hosts = append(hosts, v2.Host{IPS: v1Cluster.Spec.Masters.IPList, Roles: []string{consts.MASTER}})
	// }
	// if len(v1Cluster.Spec.Nodes.IPList) != 0 {
	// 	hosts = append(hosts, v2.Host{IPS: v1Cluster.Spec.Nodes.IPList, Roles: []string{consts.NODE}})
	// }

	cluster.APIVersion = v2.GroupVersion.String()
	cluster.Spec.SSH = v1Cluster.Spec.SSH
	cluster.Spec.Env = v1Cluster.Spec.Env
	cluster.Spec.Hosts = hosts
	cluster.Spec.Image = v1Cluster.Spec.Image
	cluster.Name = v1Cluster.Name
	cluster.Kind = v1Cluster.Kind
	return cluster
}

func decodeConfigListFunc(reader io.Reader) (interface{}, error) {
	var (
		configs       []confv1beta1.Config
		decoder       = NewK8sYamlDecoder(reader)
		switchVersion = func(version string) interface{} { return &confv1beta1.Config{} }
	)
	for {
		in, err := decodeCRDFromReader(decoder, constants.ConfigKind, switchVersion)
		if err != nil {
			if err == io.EOF {
				return configs, nil
			}
			return nil, fmt.Errorf("failed to decode config: %v", err)
		}
		configs = append(configs, *in.(*confv1beta1.Config))
	}
}

func decodePluginListFunc(reader io.Reader) (interface{}, error) {
	var (
		plugins       []pgv1beta1.Plugin
		decoder       = NewK8sYamlDecoder(reader)
		switchVersion = func(version string) interface{} { return &pgv1beta1.Plugin{} }
	)

	for {
		in, err := decodeCRDFromReader(decoder, constants.PluginKind, switchVersion)
		if err != nil {
			if err == io.EOF {
				return plugins, nil
			}
			return nil, fmt.Errorf("failed to decode config: %v", err)
		}
		plugins = append(plugins, *in.(*pgv1beta1.Plugin))
	}
}

func decodeInitConfigurationFunc(reader io.Reader) (out interface{}, err error) {
	switchVersion := func(version string) interface{} { return &v1beta3.InitConfiguration{} }
	return decodeCRDFromReader(NewK8sYamlDecoder(reader), kubeadmConstants.InitConfigurationKind, switchVersion)
}

func decodeJoinConfigurationFunc(reader io.Reader) (out interface{}, err error) {
	switchVersion := func(version string) interface{} { return &v1beta3.JoinConfiguration{} }
	return decodeCRDFromReader(NewK8sYamlDecoder(reader), kubeadmConstants.JoinConfigurationKind, switchVersion)
}

func decodeClusterConfigurationFunc(reader io.Reader) (out interface{}, err error) {
	switchVersion := func(version string) interface{} { return &v1beta3.ClusterConfiguration{} }
	return decodeCRDFromReader(NewK8sYamlDecoder(reader), kubeadmConstants.ClusterConfigurationKind, switchVersion)
}

func decodeKubeletConfigurationFunc(reader io.Reader) (out interface{}, err error) {
	switchVersion := func(version string) interface{} { return &v1beta1.KubeletConfiguration{} }
	return decodeCRDFromReader(NewK8sYamlDecoder(reader), constants.KubeletConfiguration, switchVersion)
}

func decodeKubeProxyConfigurationFunc(reader io.Reader) (out interface{}, err error) {
	switchVersion := func(version string) interface{} { return &v1alpha1.KubeProxyConfiguration{} }
	return decodeCRDFromReader(NewK8sYamlDecoder(reader), constants.KubeProxyConfiguration, switchVersion)
}
