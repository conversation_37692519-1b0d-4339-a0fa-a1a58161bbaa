package utils

/*func TestDecodeCRDFromFile(t *testing.T) {
	type args struct {
		filepath string
		kind     string
	}
	testFile := "test/file/Clusterfile"
	var tests = []struct {
		name string
		args args
	}{
		{
			"test " + common.InitConfiguration,
			args{
				filepath: testFile,
				kind:     common.InitConfiguration,
			},
		},
		{
			"test " + common.JoinConfiguration,
			args{
				filepath: testFile,
				kind:     common.JoinConfiguration,
			},
		},
		{
			"test " + common.KubeletConfiguration,
			args{
				filepath: testFile,
				kind:     common.KubeletConfiguration,
			},
		},
		{
			"test " + common.Cluster,
			args{
				filepath: testFile,
				kind:     common.Cluster,
			},
		},
		{
			"test " + common.Plugin,
			args{
				filepath: testFile,
				kind:     common.Plugin,
			},
		},
		{
			"test " + common.Config,
			args{
				filepath: testFile,
				kind:     common.Config,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if obj, err := DecodeCRDFromFile(tt.args.filepath, tt.args.kind); err != nil {
				t.<PERSON>("DecodeV1CRD1() error = %v", err)
			} else {
				fmt.Printf("%#+v \n", obj)
			}
		})
	}
}

func TestDecodeV1ClusterFromFile(t *testing.T) {
	testFile := "test/file/v1Clusterfile"
	t.Run("test decode v1 cluster"+testFile, func(t *testing.T) {
		got, err := DecodeV1ClusterFromFile(testFile)
		if err != nil {
			t.Errorf("failed to decode v1 cluster form %s: %v", testFile, err)
		}
		fmt.Printf("got:\n %#+v", got)
	})
}*/
