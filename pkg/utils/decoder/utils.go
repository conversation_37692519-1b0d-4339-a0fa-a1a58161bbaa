package utils

import (
	"fmt"
	"net"
	"regexp"
	"time"
)

func Retry(tryTimes int, trySleepTime time.Duration, action func() error) error {
	var err error
	for i := 0; i < tryTimes; i++ {
		err = action()
		if err == nil {
			return nil
		}

		time.Sleep(trySleepTime * time.Duration(2*i+1))
	}
	return fmt.Errorf("retry action timeout: %v", err)
}

// ConfirmOperation confirm whether to continue with the operation, typing yes will return true.
func ConfirmOperation(promptInfo string) (bool, error) {
	var yesRx = regexp.MustCompile("^(?:y(?:es)?)$")
	var noRx = regexp.MustCompile("^(?:n(?:o)?)$")
	var input string
	for {
		fmt.Printf(promptInfo + " Yes [y/yes], No [n/no] : ")
		_, err := fmt.Scanln(&input)
		if err != nil {
			return false, err
		}
		if yesRx.MatchString(input) {
			break
		}
		if noRx.MatchString(input) {
			return false, nil
		}
	}
	return true, nil
}

func WrapExecResult(host net.IP, command string, output []byte, err error) error {
	return fmt.Errorf("failed to execute command(%s) on host(%s): output(%s), error(%v)", command, host.String(), output, err)
}
