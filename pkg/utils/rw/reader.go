package rw

import (
	"bufio"
	"io"
	"os"
	"path/filepath"

	"github.com/pkg/errors"
	"k8s.io/klog/v2"
)

type FileReader interface {
	ReadLines() ([]string, error)
	ReadAll() ([]byte, error)
}

type fileReader struct {
	fileName string
}

func (r fileReader) ReadLines() ([]string, error) {
	var lines []string

	if _, err := os.Stat(r.fileName); err != nil || os.IsNotExist(err) {
		return nil, errors.New("no such file")
	}

	file, err := os.Open(filepath.Clean(r.fileName))
	if err != nil {
		return nil, err
	}
	defer func() {
		if err = file.Close(); err != nil {
			klog.Fatalf("failed to close file: %v", err)
		}
	}()
	br := bufio.NewReader(file)
	for {
		line, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		lines = append(lines, string(line))
	}
	return lines, nil
}

func (r fileReader) ReadAll() ([]byte, error) {
	if _, err := os.Stat(r.fileName); err != nil || os.IsNotExist(err) {
		return nil, errors.New("no such file")
	}

	file, err := os.Open(filepath.Clean(r.fileName))
	if err != nil {
		return nil, err
	}
	defer func() {
		if err := file.Close(); err != nil {
			klog.Errorf("failed to close file: %v", err)
		}
	}()

	content, err := os.ReadFile(filepath.Clean(r.fileName))
	if err != nil {
		return nil, err
	}

	return content, nil
}

func NewFileReader(fileName string) FileReader {
	return fileReader{
		fileName: fileName,
	}
}
