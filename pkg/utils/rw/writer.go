package rw

import (
	"os"
	"path/filepath"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"k8s.io/klog/v2"
)

type FileWriter interface {
	WriteFile(content []byte) error
}

type atomicFileWriter struct {
	f    *os.File
	path string
	perm os.FileMode
}

func (a *atomicFileWriter) close() (err error) {
	if err = a.f.Sync(); err != nil {
		err := a.f.Close()
		if err != nil {
			return err
		}
		return err
	}
	if err := a.f.Close(); err != nil {
		return err
	}
	if err := os.Chmod(a.f.Name(), a.perm); err != nil {
		return err
	}
	return os.Rename(a.f.Name(), a.path)
}

func newAtomicFileWriter(path string, perm os.FileMode) (*atomicFileWriter, error) {
	tmpFile, err := os.CreateTemp(filepath.Dir(path), ".FTmp-")
	if err != nil {
		return nil, err
	}
	return &atomicFileWriter{f: tmpFile, path: path, perm: perm}, nil
}

type atomicWriter struct{ fileName string }

func (a atomicWriter) Clean(file *os.File) {
	if file == nil {
		return
	}
	// the following operation won't fail regularly, if failed, log it
	if err := file.Close(); err != nil && err != os.ErrClosed {
		klog.Warning(err)
	}
	if err := os.Remove(file.Name()); err != nil {
		klog.Warning(err)
	}
}

func (a atomicWriter) WriteFile(content []byte) error {
	dir := filepath.Dir(a.fileName)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err = os.MkdirAll(dir, constants.FileMode0755); err != nil {
			return err
		}
	}

	afw, err := newAtomicFileWriter(a.fileName, constants.FileMode0644)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			a.Clean(afw.f)
		}
	}()
	if _, err = afw.f.Write(content); err != nil {
		return err
	}
	return afw.close()
}

func NewAtomicWriter(fileName string) FileWriter {
	return atomicWriter{
		fileName: fileName,
	}
}

type commonWriter struct {
	fileName string
}

func (c commonWriter) WriteFile(content []byte) error {
	dir := filepath.Dir(c.fileName)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err = os.MkdirAll(dir, constants.FileMode0755); err != nil {
			return err
		}
	}
	return os.WriteFile(c.fileName, content, constants.FileMode0644)
}

func NewCommonWriter(fileName string) FileWriter {
	return commonWriter{
		fileName: fileName,
	}
}
