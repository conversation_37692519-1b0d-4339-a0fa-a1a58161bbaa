package shellcommand

import (
	"fmt"
)

const DefaultKubepilotHostAliasAnnotation = "# hostalias-set-by-pilotctl"

// CommandSetHostAlias 新增 `/etc/hosts` 文件中的域名解析规则
func CommandSetHostAlias(hostName, ip string) string {
	return fmt.Sprintf(`sed -i "/\ %s/d" /etc/hosts;echo "%s %s %s" >>/etc/hosts`, hostName, ip, hostName, DefaultKubepilotHostAliasAnnotation)
}

// CommandUnSetHostAlias 删除 `/etc/hosts` 文件中的域名解析规则
func CommandUnSetHostAlias() string {
	return fmt.Sprintf(`echo "$(sed "/%s/d" /etc/hosts)" > /etc/hosts`, DefaultKubepilotHostAliasAnnotation)
}

// CommandRemoveIP 删除节点上的任意IP
func CommandRemoveIP(ip string) string {
	return fmt.Sprintf(`ip addr flush to %s`, ip)
}
