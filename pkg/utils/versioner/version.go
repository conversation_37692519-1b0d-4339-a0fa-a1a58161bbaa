package versioner

import (
	"fmt"
	"strings"
)

// Version is a string that we used to normalize version string.
type Version string

// splitVersion takes version string, and encapsulates it in comparable []string.
func splitVersion(version string) []string {
	version = strings.Replace(version, "v", "", -1)
	version = strings.Split(version, "-")[0]
	return strings.Split(version, ".")
}

// GreaterThan if givenVersion >= oldVersion return true, else return false
func (v Version) GreaterThan(oldVersion Version) (bool, error) {
	givenVersion := splitVersion(string(v))
	ov := splitVersion(string(oldVersion))

	if len(givenVersion) != 3 || len(ov) != 3 {
		return false, fmt.Errorf("error version format %s %s", v, ov)
	}
	// TODO check if necessary need v = version logic!
	if givenVersion[0] > ov[0] {
		return true, nil
	} else if givenVersion[0] < ov[0] {
		return false, nil
	}
	if givenVersion[1] > ov[1] {
		return true, nil
	} else if givenVersion[1] < ov[1] {
		return false, nil
	}
	if givenVersion[2] > ov[2] {
		return true, nil
	}
	return true, nil
}
