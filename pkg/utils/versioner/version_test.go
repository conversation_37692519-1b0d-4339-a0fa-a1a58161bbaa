package versioner

import (
	"reflect"
	"testing"

	"k8s.io/klog/v2"
)

func TestVersion_Compare(t *testing.T) {
	tests := []struct {
		name         string
		givenVersion Version
		oldVersion   Version
		wantRes      bool
	}{
		{
			name:         "test v > v1",
			givenVersion: "v1.20.4",
			oldVersion:   "v1.19.8",
			wantRes:      true,
		},
		{
			name:         "test v = v1",
			givenVersion: "v1.19.8",
			oldVersion:   "v1.19.8",
			wantRes:      true,
		},
		{
			name:         "test v < v1",
			givenVersion: "v1.19.8",
			oldVersion:   "v1.20.4",
			wantRes:      false,
		},
		{
			name:         "test1 old Version illegal",
			givenVersion: "v1.19.8",
			oldVersion:   "",
			wantRes:      false,
		},
		{
			name:         "test2 give Version illegal",
			givenVersion: "",
			oldVersion:   "v1.19.8",
			wantRes:      false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			v := tt.givenVersion
			res, err := v.<PERSON>han(tt.oldVersion)
			if err != nil {
				klog.Errorf("compare kubernetes version failed: %s", err)
			}
			if !reflect.DeepEqual(res, tt.wantRes) {
				t.Errorf("Version compare failed! result: %v, want: %v", res, tt.wantRes)
			}
		})
	}
}
