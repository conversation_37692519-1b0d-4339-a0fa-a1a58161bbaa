package http

import (
	"context"
	"crypto/tls"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"

	"k8s.io/klog/v2"
)

func WaitUntilEndpointAlive(ctx context.Context, endpoint string) error {
	if !strings.HasPrefix(endpoint, "http") {
		endpoint = "http://" + endpoint
	}
	u, err := url.Parse(endpoint)
	if err != nil {
		return err
	}
	klog.V(5).Infof("checking if endpoint %s is alive", u.String())
	for {
		select {
		case <-ctx.Done():
			return err
		default:
			var resp *http.Response
			resp, err = DefaultClient.Get(u.String())
			if err == nil {
				_, _ = io.Copy(io.Discard, resp.Body)
				resp.Body.Close()
				klog.V(5).Infof("http endpoint %s is alive", u.String())
				return nil
			}
			time.Sleep(100 * time.Millisecond)
		}
	}
}

var DefaultClient = &http.Client{
	Transport: DefaultSkipVerify,
}

var DefaultSkipVerify = &http.Transport{
	Proxy:               http.ProxyFromEnvironment,
	MaxIdleConnsPerHost: 100,
	DialContext: (&net.Dialer{
		Timeout:   30 * time.Second,
		KeepAlive: 30 * time.Second,
	}).DialContext,
	MaxIdleConns:          100,
	IdleConnTimeout:       90 * time.Second,
	TLSHandshakeTimeout:   10 * time.Second,
	ExpectContinueTimeout: 1 * time.Second,
	// nosemgrep
	TLSClientConfig: &tls.Config{
		InsecureSkipVerify: true,
	},
}
