package strs

import (
	"testing"
)

func TestConvertStringSliceToMap(t *testing.T) {
	tests := []struct {
		name     string
		input    []string
		expected map[string]string
	}{
		{
			name:     "Empty slice",
			input:    []string{},
			expected: map[string]string{},
		},
		{
			name:     "Single key-value pair",
			input:    []string{"key=value"},
			expected: map[string]string{"key": "value"},
		},
		{
			name:     "JSON array",
			input:    []string{"key=[{\"name\":\"John\"}],v1=v1"},
			expected: map[string]string{"key": "[{\"name\":\"John\"}]", "v1": "v1"},
		},
		{
			name:     "JSON object",
			input:    []string{"key={\"name\":\"John\"}"},
			expected: map[string]string{"key": "{\"name\":\"John\"}"},
		},
		{
			name:     "Multiple key-value pairs",
			input:    []string{"key1=value1", "key2=value2"},
			expected: map[string]string{"key1": "value1", "key2": "value2"},
		},
		{
			name:     "Nested key-value pairs",
			input:    []string{"key=value1,value2=value3"},
			expected: map[string]string{"key": "value1", "value2": "value3"},
		},
		{
			name:     "Invalid JSON",
			input:    []string{"key={invalid}"},
			expected: map[string]string{"key": "{invalid}"},
		},
		{
			name:     "Nested key-value pairs",
			input:    []string{"key=value1\\,value2\\,value3"},
			expected: map[string]string{"key": "value1,value2,value3"},
		},
		{
			name:     "Multiple parsing functions applied",
			input:    []string{"key1={\"name\":\"John\"}", "key2=hello\\,world"},
			expected: map[string]string{"key1": "{\"name\":\"John\"}", "key2": "hello,world"},
		},
		{
			name:     "Special characters",
			input:    []string{"key1=0 0 * * *", "key2=0/2 0\\,24 * *"},
			expected: map[string]string{"key1": "0 0 * * *", "key2": "0/2 0,24 * *"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertStringSliceToMap(tt.input)
			if !isEqual(result, tt.expected) {
				t.Errorf("Test %s failed: expected %v, got %v", tt.name, tt.expected, result)
			}
		})
	}
}

func isEqual(a, b map[string]string) bool {
	if len(a) != len(b) {
		return false
	}

	for k, v := range a {
		if bv, ok := b[k]; !ok || v != bv {
			return false
		}
	}

	return true
}

func TestFixJSON(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"{key1:value1,key2:value2}", `{"key1":"value1","key2":"value2"}`},
		// Add more test cases if needed
	}

	for _, test := range tests {
		actual := fixJSON(test.input)
		if actual != test.expected {
			t.Errorf("fixJSON(%s) = %s, expected %s", test.input, actual, test.expected)
		}
	}
}
