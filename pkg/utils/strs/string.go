package strs

import (
	"crypto/rand"
	"encoding/json"
	"fmt"
	"net"
	"regexp"
	"strings"
	"unicode"
)

func GetRandomString(n int) string {
	randBytes := make([]byte, n/2)
	_, _ = rand.Read(randBytes)
	return fmt.Sprintf("%x", randBytes)
}

type Interface interface {
	// GetIntersection get intersection element form between slice.
	GetIntersection() []string
	// GetUnion get Union element between two slice.
	GetUnion() []string
	// GetSrcSubtraction get different element in src compare to dst.
	GetSrcSubtraction() []string
	// GetDstSubtraction get different element in dst compare to src.
	GetDstSubtraction() []string
}

type Comparator struct {
	Src []string
	Dst []string
}

func (c Comparator) GetIntersection() []string {
	var result []string
	for _, elem := range c.Src {
		// elem both exist in src and dst at the same time.
		if IsInSlice(elem, c.Dst) {
			result = append(result, elem)
		}
	}
	return result
}

func (c Comparator) GetUnion() []string {
	result := c.Src
	for _, elem := range c.Dst {
		// get all elem
		if !IsInSlice(elem, c.Src) {
			result = append(result, elem)
		}
	}
	return result
}

func (c Comparator) GetSrcSubtraction() []string {
	var result []string
	for _, elem := range c.Src {
		// get src elem which not in dst
		if !IsInSlice(elem, c.Dst) {
			result = append(result, elem)
		}
	}
	return result
}

func (c Comparator) GetDstSubtraction() []string {
	var result []string
	for _, elem := range c.Dst {
		// get dst elem which not in src
		if !IsInSlice(elem, c.Src) {
			result = append(result, elem)
		}
	}
	return result
}

func NewComparator(src, dst []string) Interface {
	return Comparator{
		Src: src,
		Dst: dst,
	}
}

func IsInSlice(key string, slice []string) bool {
	for _, s := range slice {
		if key == s {
			return true
		}
	}
	return false
}

func Reverse(s []string) []string {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
	return s
}

func ContainPartial(list []string, partial string) (result []string) {
	for i := range list {
		if strings.Contains(list[i], partial) {
			result = append(result, list[i])
		}
	}
	return
}

// RemoveDuplicate 删除重复项
func RemoveDuplicate(list []string) []string {
	var result []string
	flagMap := map[string]struct{}{}
	for _, v := range list {
		if _, ok := flagMap[v]; !ok {
			flagMap[v] = struct{}{}
			result = append(result, v)
		}
	}
	return result
}

func IsLetterOrNumber(k string) bool {
	for _, r := range k {
		if r == '_' {
			continue
		}
		if !unicode.IsLetter(r) && !unicode.IsNumber(r) {
			return false
		}
	}
	return true
}

// Merge :merge slice type as overwrite model
func Merge(ms ...[]string) []string {
	var base []string
	diffMap := make(map[string]bool)
	for i, s := range ms {
		if i == 0 {
			base = s
			for _, v := range base {
				diffMap[v] = true
			}
		}

		for _, v := range s {
			if !diffMap[v] {
				base = append(base, v)
				diffMap[v] = true
			}
		}
	}
	return base
}

// ConvertMapToSlice 将map的key，value变为切片的值key=value
func ConvertMapToSlice(m map[string]string) []string {
	var res []string
	for k, v := range m {
		res = append(res, fmt.Sprintf("%s=%s", k, v))
	}

	return res
}

// parseValueType 定义了不同的解析类型
type parseValueType string

const (
	// parseJson 表示JSON格式的解析
	parseJson parseValueType = "json"
	// ParseEscapeCharacters 表示转义字符的解析
	ParseEscapeCharacters parseValueType = "escapeCharacters"
)

// parseValueFuncMap 是一个映射，将解析类型与具体的解析函数关联起来
var parseValueFuncMap = map[parseValueType]func(value string) string{
	parseJson:             parseJsonValue,
	ParseEscapeCharacters: ParseEscapeCharactersValue,
}

// parseJsonValue 尝试解析给定的字符串是否为JSON格式，如果是，则解析并格式化返回
func parseJsonValue(value string) string {
	var jsonValue interface{}

	// 检查字符串是否以JSON格式开始和结束
	if (strings.HasPrefix(value, "[") && strings.HasSuffix(value, "]")) ||
		(strings.HasPrefix(value, "{") && strings.HasSuffix(value, "}")) {

		// 对JSON字符串进行修复
		value = fixJSON(value)

		// 解析JSON字符串
		if err := json.Unmarshal([]byte(value), &jsonValue); err == nil {
			// 格式化解析后的JSON，并将其转换回字符串
			prettyValue, _ := json.Marshal(jsonValue)
			return string(prettyValue)
		}
	}

	// 如果不是有效的JSON，则直接返回原始字符串
	return value
}

// ParseEscapeCharactersValue 替换字符串中的转义字符
func ParseEscapeCharactersValue(value string) string {
	return strings.ReplaceAll(value, `\,`, `,`)
}

// ConvertStringSliceToMap 将一个字符串切片转换成一个键值对的映射
func ConvertStringSliceToMap(stringSlice []string) map[string]string {
	ret := make(map[string]string)

	// 编译正则表达式，用于匹配键值对字符串
	re := regexp.MustCompile(`(\w+)=((?:\[[^\]]*\])|(?:\{[^\}]*\})|(?:[^\\,]|\\.)*)(?:,|$)`)

	for _, input := range stringSlice {
		// 使用正则表达式匹配字符串中的所有键值对
		matches := re.FindAllStringSubmatch(input, -1)

		for _, match := range matches {
			if len(match) == 3 {
				key := strings.TrimSpace(match[1])
				value := strings.TrimSpace(match[2])

				// 应用所有解析函数
				for _, f := range parseValueFuncMap {
					value = f(value)
				}
				ret[key] = value
			}
		}
	}

	// 返回转换后的字典
	return ret
}

// fixJSON 修复JSON字符串，如[{k1:v1,k2:v2}]->[{"k1":"v1","k2":"v2"}]
func fixJSON(input string) string {
	// 定义正则表达式以匹配键和值不带双引号的情况
	re := regexp.MustCompile(`(\w+):([^,\}\]]+)`)

	// 将匹配内容替换为带双引号的键和值
	fixed := re.ReplaceAllString(input, `"$1":"$2"`)

	return fixed
}

func Diff(old, new []net.IP) (add, sub []net.IP) {
	diffMap := make(map[string]bool)
	for _, v := range old {
		diffMap[v.String()] = true
	}
	for _, v := range new {
		if !diffMap[v.String()] {
			add = append(add, v)
		} else {
			diffMap[v.String()] = false
		}
	}
	for _, v := range old {
		if diffMap[v.String()] {
			sub = append(sub, v)
		}
	}

	return
}

func RemoveElement(slice []string, element string) []string {
	var newSlice []string
	for _, v := range slice {
		if v != element {
			newSlice = append(newSlice, v)
		}
	}
	return newSlice
}
