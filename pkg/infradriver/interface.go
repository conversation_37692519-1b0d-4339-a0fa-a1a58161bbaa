package infradriver

import (
	"net"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	imgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	corev1 "k8s.io/api/core/v1"
)

type InfraDriver interface {
	// GetHostTaints GetHostTaint return host taint
	GetHostTaints(host net.IP) []corev1.Taint
	GetHostIPList() []net.IP
	GetHostIPListByRole(role string) []net.IP
	GetRoleListByHostIP(ip string) []string
	// 	GetHostsPlatform return remote host platform
	GetHostsPlatform(hosts []net.IP) (map[imgv1beta1.Platform][]net.IP, error)
	// GetHostEnv return merged env with host env and cluster env.
	GetHostEnv(host net.IP) map[string]string
	// AddHostEnv add host env
	AddHostEnv(host net.IP, key string, value string)
	// GetHostLabels return host labels.
	GetHostLabels(host net.IP) map[string]string
	// GetClusterEnv return cluster.spec.env as map[string]interface{}
	GetClusterEnv() map[string]string
	AddClusterEnv(envs []string)
	// GetApplicationName get application name
	GetApplicationName() string
	// SetApplicationName set application name
	SetApplicationName(name string)
	// GetApplicationEnv application.spec.env as map[string]interface{}
	GetApplicationEnv() map[string]string
	AddApplicationEnv(envs []string)
	// GetClusterName ${clusterName}
	GetClusterName() string
	// GetClusterImageName ${cluster image Name}
	GetClusterImageName() string
	// GetClusterLaunchCmds ${user-defined launch command}
	GetClusterLaunchCmds() []string
	// GetClusterLaunchApps ${user-defined launch apps}
	GetClusterLaunchApps() []string
	// GetClusterRootfsPath /var/lib/kubepilot/data/${clusterName}/rootfs
	GetClusterRootfsPath() string
	// GetClusterBasePath /var/lib/kubepilot/data/${clusterName}
	GetClusterBasePath() string
	// Execute use eg.Go to execute shell cmd concurrently
	Execute(hosts []net.IP, f func(host net.IP) error) error
	// Copy local files to remote host
	// scp -r /tmp root@***********:/root/tmp => Copy("***********","tmp","/root/tmp")
	// need check md5sum
	Copy(host net.IP, localFilePath, remoteFilePath string) error
	// CopyR copy remote host files to localhost
	CopyR(host net.IP, remoteFilePath, localFilePath string) error
	// CmdAsync exec command on remote host, and asynchronous return logs
	CmdAsync(host net.IP, env map[string]string, cmd ...string) error
	// Cmd exec command on remote host, and return combined standard output and standard error
	Cmd(host net.IP, env map[string]string, cmd string) ([]byte, error)
	// CmdToString exec command on remote host, and return spilt standard output and standard error
	CmdToString(host net.IP, env map[string]string, cmd, spilt string) (string, error)
	// IsFileExist check remote file exist or not
	IsFileExist(host net.IP, remoteFilePath string) (bool, error)
	// IsDirExist Remote file existence returns true, nil
	IsDirExist(host net.IP, remoteDirPath string) (bool, error)
	// GetPlatform Get remote platform
	GetPlatform(host net.IP) (imgv1beta1.Platform, error)
	// GetHostName Return host name by ip
	GetHostName(host net.IP) (string, error)
	// Ping Ping remote host
	Ping(host net.IP) error
	// SetHostName add or update host name on host
	SetHostName(host net.IP, hostName string) error
	// SetClusterHostAliases set additional HostAliases
	SetClusterHostAliases(hosts []net.IP) error
	// DeleteClusterHostAliases delete additional HostAliases
	DeleteClusterHostAliases(hosts []net.IP) error
	// RemoveVIP Delete the VIP to prevent IP address occupancy
	RemoveVIP(hosts []net.IP, VIP string) error
	// GetClusterRegistry return registry info
	GetClusterRegistry() ctv1beta1.Registry
	// IsRemoteRegistry whether it use remote registry
	IsRemoteRegistry() bool
	// GetRegistryDataDir return registry data dir
	GetRegistryDataDir() string
	// SetClusterHostFile set cluster /etc/hosts
	SetClusterHostFile(hosts []net.IP, ip, domain string) error
	// SetHostFile set /etc/hosts
	SetHostFile(host net.IP, ip, domain string) error
}
