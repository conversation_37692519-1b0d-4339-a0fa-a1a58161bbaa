package sshdriver

import (
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
)

const (
	// Del 减号
	Del = "-"
	// Equal 等号
	Equal = "="
	// Colon 冒号
	Colon = ":"
)

// convertTaint process data in the specified format
func convertTaint(data string) (corev1.Taint, error) {
	var (
		key, value, effect string
		TaintEffectValues  = []corev1.TaintEffect{corev1.TaintEffectNoSchedule, corev1.TaintEffectNoExecute, corev1.TaintEffectPreferNoSchedule}
	)
	data = strings.TrimSpace(data)

	switch {
	// key1=value1:NoSchedule
	case strings.Contains(data, Equal) && !strings.Contains(data, Equal+Colon) && !strings.Contains(data, Del):
		temps := strings.Split(data, Equal)
		if len(temps) != 2 {
			return corev1.Taint{}, fmt.Errorf("faild to split taint argument: %s", data)
		}
		key = temps[0]
		taintArgs := strings.Split(temps[1], Colon)
		if len(taintArgs) != 2 {
			return corev1.Taint{}, fmt.Errorf("error: invalid taint data: %s", data)
		}
		value, effect = taintArgs[0], taintArgs[1]
		effect = strings.TrimSuffix(effect, Del)

		// key1:NoSchedule
	case !strings.Contains(data, Equal) && strings.Contains(data, Colon) && !strings.Contains(data, Del):
		temps := strings.Split(data, Colon)
		if len(temps) != 2 {
			return corev1.Taint{}, fmt.Errorf("faild to split taint argument: %s", data)
		}
		key, value, effect = temps[0], "", temps[1]

		// key1=:NoSchedule
	case strings.Contains(data, Equal+Colon) && !strings.Contains(data, Del):
		temps := strings.Split(data, Equal+Colon)
		if len(temps) != 2 {
			return corev1.Taint{}, fmt.Errorf("faild to split taint argument: %s", data)
		}
		key, value, effect = temps[0], "", temps[1]

		// key1-
	case strings.Contains(data, Del) && !strings.Contains(data, Equal) && !strings.Contains(data, Colon):
		key, value, effect = data, "", ""

		// key1:NoSchedule-
	case strings.Contains(data, Del) && !strings.Contains(data, Equal) && strings.Contains(data, Colon):
		temps := strings.Split(data, Colon)
		if len(temps) != 2 {
			return corev1.Taint{}, fmt.Errorf("faild to split taint argument: %s", data)
		}
		key, value, effect = temps[0], "", temps[1]
	}

	// determine whether the Effect is legal
	if effect != "" {
		taintEffect := strings.TrimSuffix(effect, Del)
		if notInEffect(corev1.TaintEffect(taintEffect), TaintEffectValues) {
			return corev1.Taint{}, fmt.Errorf("taint effect %s need in %v", data, TaintEffectValues)
		}
	}

	taint := corev1.Taint{
		Key:    key,
		Value:  value,
		Effect: corev1.TaintEffect(effect),
	}
	return taint, nil
}

// notInEffect 无效的tait
func notInEffect(effect corev1.TaintEffect, effects []corev1.TaintEffect) bool {
	for _, e := range effects {
		if e == effect {
			return false
		}
	}
	return true
}

// DeleteTaintsByKey removes all the taints that have the same key to given taintKey
func DeleteTaintsByKey(taints []corev1.Taint, taintKey string) ([]corev1.Taint, bool) {
	var newTaints []corev1.Taint
	for i := range taints {
		if taintKey == taints[i].Key {
			continue
		}
		newTaints = append(newTaints, taints[i])
	}
	return newTaints, len(taints) != len(newTaints)
}

// DeleteTaint removes all the taints that have the same key and effect to given taintToDelete.
func DeleteTaint(taints []corev1.Taint, taintToDelete *corev1.Taint) ([]corev1.Taint, bool) {
	var newTaints []corev1.Taint
	for i := range taints {
		if taintToDelete.MatchTaint(&taints[i]) {
			continue
		}
		newTaints = append(newTaints, taints[i])
	}
	return newTaints, len(taints) != len(newTaints)
}
