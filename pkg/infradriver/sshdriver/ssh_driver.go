package sshdriver

import (
	"context"
	"errors"
	"fmt"
	"net"
	"path/filepath"
	"strings"

	buildahutil "github.com/containers/buildah/util"
	"github.com/imdario/mergo"
	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/ssh"
	umaps "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/maps"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/shellcommand"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/strs"
	"golang.org/x/sync/errgroup"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
	utilnet "k8s.io/utils/net"
)

type SshDriver struct {
	sshConfiguration map[string]ssh.Interface
	hosts            []net.IP
	hostTaint        map[string][]corev1.Taint
	hostRolesMap     map[string][]string
	roleHostsMap     map[string][]net.IP
	hostLabels       map[string]map[string]string
	hostEnvMap       map[string]map[string]string
	clusterEnv       map[string]string
	cluster          ctv1beta1.Cluster
	applicationEnv   map[string]string
	application      appv1beta1.Application
	name             string
}

func (s *SshDriver) GetHostTaints(host net.IP) []corev1.Taint {
	return s.hostTaint[host.String()]
}

func (s *SshDriver) GetHostIPList() []net.IP {
	return s.hosts
}

func (s *SshDriver) GetHostIPListByRole(role string) []net.IP {
	return s.roleHostsMap[role]
}

func (s *SshDriver) GetRoleListByHostIP(ip string) []string {
	return s.hostRolesMap[ip]
}

func (s *SshDriver) GetHostsPlatform(hosts []net.IP) (map[v1beta1.Platform][]net.IP, error) {
	// key: platform, value: []net.IP
	m := make(map[v1beta1.Platform][]net.IP)

	for _, ip := range hosts {
		plat, err := s.GetPlatform(ip)
		if err != nil {
			return nil, err
		}
		_, ok := m[plat]
		if !ok {
			m[plat] = []net.IP{ip}
		} else {
			m[plat] = append(m[plat], ip)
		}
	}

	return m, nil
}

func (s *SshDriver) GetHostEnv(host net.IP) map[string]string {
	env := s.hostEnvMap[host.String()]
	// set this parameter if not exist
	if _, ok := env[constants.EnvHostIP]; !ok {
		env[constants.EnvHostIP] = host.String()
	}
	return env
}

func (s *SshDriver) AddHostEnv(host net.IP, key string, value string) {
	if _, ok := s.hostEnvMap[host.String()]; !ok {
		s.hostEnvMap[host.String()] = make(map[string]string)
	}

	s.hostEnvMap[host.String()][key] = value
}

func (s *SshDriver) GetHostLabels(host net.IP) map[string]string {
	return s.hostLabels[host.String()]
}

func (s *SshDriver) GetClusterEnv() map[string]string {
	return s.clusterEnv
}

func (s *SshDriver) AddClusterEnv(envs []string) {
	if s.clusterEnv == nil && envs == nil {
		return
	}
	m := strs.ConvertStringSliceToMap(envs)
	for k, v := range m {
		s.clusterEnv[k] = v
	}
}

func (s *SshDriver) SetApplicationName(name string) {
	s.name = name
}

func (s *SshDriver) GetApplicationName() string {
	return s.name
}

func (s *SshDriver) GetApplicationEnv() map[string]string {
	return s.applicationEnv
}

func (s *SshDriver) AddApplicationEnv(envs []string) {
	if envs == nil {
		return
	}
	if s.applicationEnv == nil {
		s.applicationEnv = make(map[string]string)
	}
	m := strs.ConvertStringSliceToMap(envs)
	for k, v := range m {
		s.applicationEnv[k] = v
	}
}

func (s *SshDriver) GetClusterName() string {
	return s.cluster.Name
}

func (s *SshDriver) GetClusterImageName() string {
	return s.cluster.Spec.Image
}

func (s *SshDriver) GetClusterLaunchCmds() []string {
	return s.cluster.Spec.CMD
}

func (s *SshDriver) GetClusterLaunchApps() []string {
	return s.cluster.Spec.APPNames
}

// GetClusterRootfsPath 存放rootfs数据的目录
func (s *SshDriver) GetClusterRootfsPath() string {
	path := s.cluster.Spec.DataRoot
	if len(path) == 0 {
		path = constants.DefaultKubepilotDataDir
	}
	return filepath.Join(path, s.cluster.Name, "rootfs")
}

func (s *SshDriver) GetClusterBasePath() string {
	path := s.cluster.Spec.DataRoot
	if len(path) == 0 {
		path = constants.DefaultKubepilotDataDir
	}
	return filepath.Join(path, s.cluster.Name)
}

func (s *SshDriver) Execute(hosts []net.IP, f func(host net.IP) error) error {
	eg, _ := errgroup.WithContext(context.TODO())

	for _, ip := range hosts {
		host := ip
		eg.Go(func() error {
			if err := f(host); err != nil {
				return fmt.Errorf("happen err on host [%s]: %v", host.String(), err)
			}
			return nil
		})
	}
	return eg.Wait()
}

func (s *SshDriver) Copy(host net.IP, localFilePath, remoteFilePath string) error {
	client := s.sshConfiguration[host.String()]
	if client == nil {
		return fmt.Errorf("the node [%s] not be found in cluster", host.String())
	}

	return client.Copy(host, localFilePath, remoteFilePath)
}

func (s *SshDriver) CopyR(host net.IP, remoteFilePath, localFilePath string) error {
	client := s.sshConfiguration[host.String()]
	if client == nil {
		return fmt.Errorf("the node [%s] not be found in cluster", host.String())
	}

	return client.CopyR(host, localFilePath, remoteFilePath)
}

func (s *SshDriver) CmdAsync(host net.IP, env map[string]string, cmd ...string) error {
	client := s.sshConfiguration[host.String()]
	if client == nil {
		return fmt.Errorf("the node [%s] not be found in cluster", host.String())
	}
	return client.CmdAsync(host, env, cmd...)
}

func (s *SshDriver) Cmd(host net.IP, env map[string]string, cmd string) ([]byte, error) {
	client := s.sshConfiguration[host.String()]
	if client == nil {
		return nil, fmt.Errorf("the node [%s] not be found in cluster", host.String())
	}
	return client.Cmd(host, env, cmd)
}

func (s *SshDriver) CmdToString(host net.IP, env map[string]string, cmd, spilt string) (string, error) {
	client := s.sshConfiguration[host.String()]
	if client == nil {
		return "", fmt.Errorf("the node [%s] not be found in cluster", host.String())
	}
	return client.CmdToString(host, env, cmd, spilt)
}

func (s *SshDriver) IsFileExist(host net.IP, remoteFilePath string) (bool, error) {
	client := s.sshConfiguration[host.String()]
	if client == nil {
		return false, fmt.Errorf("the node [%s] not be found in cluster", host.String())
	}
	return client.IsFileExist(host, remoteFilePath)
}

func (s *SshDriver) IsDirExist(host net.IP, remoteDirPath string) (bool, error) {
	client := s.sshConfiguration[host.String()]
	if client == nil {
		return false, fmt.Errorf("the node [%s] not be found in cluster", host.String())
	}
	return client.RemoteDirExist(host, remoteDirPath)
}

func (s *SshDriver) GetPlatform(host net.IP) (v1beta1.Platform, error) {
	client := s.sshConfiguration[host.String()]
	if client == nil {
		return v1beta1.Platform{}, fmt.Errorf("the node [%s] not be found in cluster", host.String())
	}
	return client.GetPlatform(host)
}

func (s *SshDriver) GetHostName(host net.IP) (string, error) {
	uname, err := s.CmdToString(host, nil, "uname -n", "")
	if err != nil {
		return "", err
	}
	if len(uname) == 0 {
		return "", fmt.Errorf("unable to get name from remote node [%s]", host.String())
	}
	// 节点名称有可能是大写，导致生成的证书与kubeadm的原生证书不一致
	// 所以需要转小写
	return strings.ToLower(uname), nil
}

func (s *SshDriver) Ping(host net.IP) error {
	client := s.sshConfiguration[host.String()]
	if client == nil {
		return fmt.Errorf("the node [%s] not be found in cluster", host.String())
	}
	return client.Ping(host)
}

func (s *SshDriver) SetHostName(host net.IP, hostName string) error {
	cmd := fmt.Sprintf("hostnamectl set-hostname %s", host)
	return s.CmdAsync(host, nil, cmd)
}

func (s *SshDriver) SetClusterHostAliases(hosts []net.IP) error {
	for _, host := range hosts {
		for _, hostAliases := range s.cluster.Spec.HostAliases {
			hostname := strings.Join(hostAliases.Hostnames, " ")
			if err := s.CmdAsync(host, nil, shellcommand.CommandSetHostAlias(hostname, hostAliases.IP)); err != nil {
				return fmt.Errorf("failed to set hostalias for host [%s] to hostname [%s]", host, hostname)
			}
		}
	}
	return nil
}

func (s *SshDriver) DeleteClusterHostAliases(hosts []net.IP) error {
	for _, host := range hosts {
		if err := s.CmdAsync(host, nil, shellcommand.CommandUnSetHostAlias()); err != nil {
			return err
		}
	}
	return nil
}

func (s *SshDriver) RemoveVIP(hosts []net.IP, VIP string) error {
	for _, host := range hosts {
		if err := s.CmdAsync(host, nil, shellcommand.CommandRemoveIP(VIP)); err != nil {
			return err
		}
	}
	return nil
}

// GetClusterRegistry 获取registry信息
func (s *SshDriver) GetClusterRegistry() ctv1beta1.Registry {
	return s.cluster.Spec.Registry
}

// IsRemoteRegistry 是否使用外部registry
func (s *SshDriver) IsRemoteRegistry() bool {
	return s.cluster.Spec.Registry.ExternalRegistry != nil
}

// GetRegistryDataDir 获取registry数据目录
func (s *SshDriver) GetRegistryDataDir() string {
	registryDataDir := constants.DefaultRegistryDataDir
	if dir, ok := s.GetClusterEnv()[constants.EnvRegistryDataDir]; ok {
		registryDataDir = dir
	}

	return registryDataDir
}

// SetHostFile 配置/etc/hosts
func (s *SshDriver) SetHostFile(host net.IP, ip, domain string) error {
	klog.V(5).Info("start configure hosts file")
	if err := s.CmdAsync(host, nil, shellcommand.CommandSetHostAlias(domain, ip)); err != nil {
		return fmt.Errorf("failed to config cluster hosts file cmd: %v", err)
	}
	return nil
}

// SetClusterHostFile 配置集群 /etc/hosts
func (s *SshDriver) SetClusterHostFile(hosts []net.IP, ip, domain string) error {
	klog.V(5).Info("start configure cluster hosts file")
	f := func(host net.IP) error {
		if err := s.CmdAsync(host, nil, shellcommand.CommandSetHostAlias(domain, ip)); err != nil {
			return fmt.Errorf("failed to config cluster hosts file cmd: %v", err)
		}
		return nil
	}

	return s.Execute(hosts, f)
}

func NewSshDriver(cluster *ctv1beta1.Cluster) (*SshDriver, error) {
	var err error
	d := &SshDriver{
		cluster:          *cluster,
		sshConfiguration: map[string]ssh.Interface{},
		roleHostsMap:     map[string][]net.IP{},
		hostRolesMap:     map[string][]string{},
		// TODO 需要将应用和系统的环境变量拆分开
		hostEnvMap: map[string]map[string]string{},
		hostLabels: map[string]map[string]string{},
		hostTaint:  map[string][]corev1.Taint{},
	}

	// initialize hosts field
	for _, host := range cluster.Spec.Hosts {
		d.hosts = append(d.hosts, host.IPS...)
	}

	if len(d.hosts) == 0 {
		return nil, errors.New("the hosts to be specified is empty")
	}

	if err = checkAllHostsFromSameFamily(d.hosts); err != nil {
		return nil, err
	}

	if utilnet.IsIPv6String(d.hosts[0].String()) {
		hostIPFamilyEnv := fmt.Sprintf("%s=%s", constants.EnvHostIPFamily, utilnet.IPv6)
		if !buildahutil.StringInSlice(hostIPFamilyEnv, cluster.Spec.Env) {
			cluster.Spec.Env = append(cluster.Spec.Env, hostIPFamilyEnv)
		}
	}

	// initialize sshConfiguration field
	for i := range cluster.Spec.Hosts {
		if err = mergo.Merge(&cluster.Spec.Hosts[i].SSH, &cluster.Spec.SSH); err != nil {
			return nil, err
		}
		for _, ip := range cluster.Spec.Hosts[i].IPS {
			d.sshConfiguration[ip.String()] = ssh.NewSSHClient(&cluster.Spec.Hosts[i].SSH, true)
		}
	}

	// initialize roleHostsMap field
	for _, host := range cluster.Spec.Hosts {
		for _, role := range host.Roles {
			ips, ok := d.roleHostsMap[role]
			if !ok {
				d.roleHostsMap[role] = host.IPS
			} else {
				d.roleHostsMap[role] = append(ips, host.IPS...)
			}
		}
		for _, ip := range host.IPS {
			d.hostRolesMap[ip.String()] = host.Roles
		}
	}

	d.clusterEnv = strs.ConvertStringSliceToMap(cluster.Spec.Env)

	// initialize hostEnvMap and host labels field
	// merge the host ENV and global env, the host env will overwrite cluster.Spec.Env
	for _, host := range cluster.Spec.Hosts {
		for _, ip := range host.IPS {
			d.hostEnvMap[ip.String()] = umaps.Merge(strs.ConvertStringSliceToMap(host.Env), d.clusterEnv)
			d.hostLabels[ip.String()] = host.Labels
		}
	}

	for _, host := range cluster.Spec.Hosts {
		for _, ip := range host.IPS {
			d.hostTaint[ip.String()], err = convertTaints(host.Taints)
			if err != nil {
				return nil, err
			}
		}
	}

	return d, err
}

// checkAllHostsFromSameFamily 检查给定的IP列表必须是同一种IP格式(IPV4或者IPV6，不能混合)
func checkAllHostsFromSameFamily(nodeList []net.IP) error {
	var netFamily bool
	for i, ip := range nodeList {
		if i == 0 {
			netFamily = utilnet.IsIPv4(ip)
		}

		if netFamily != utilnet.IsIPv4(ip) {
			return fmt.Errorf("all hosts must be in same ip family, but the node list given are mixed with ipv4 and ipv6: %v", nodeList)
		}
	}
	return nil
}

// convertTaints 转为k8s taint资源
func convertTaints(taints []string) ([]corev1.Taint, error) {
	var k8staints []corev1.Taint
	for _, taint := range taints {
		k8stant, err := convertTaint(taint)
		if err != nil {
			return nil, err
		}
		k8staints = append(k8staints, k8stant)
	}
	return k8staints, nil
}
