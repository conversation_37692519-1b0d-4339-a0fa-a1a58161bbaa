package app

import (
	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/plugin"
)

// ApplicationDriver 应用程序驱动器
type ApplicationDriver interface {
	// GetImageLaunchCmds return appended each app launch cmds Or globalCmds.
	GetImageLaunchCmds() []string

	// GetAppLaunchCmds return application launch commands from configs
	GetAppLaunchCmds(appName string) []string

	// GetAppNames return application name list
	GetAppNames() []string

	// GetAppRoot return appRoot path by its name.
	GetAppRoot(appName string) string

	// FileProcess Process application file using at mount stage to modify build app files.
	FileProcess(mountDir string) error

	// GetApplication return app
	GetApplication() appv1beta1.Application

	SetApplication(app *appv1beta1.Application)

	SetPluginHandler(handler plugin.Handler)

	// Install 安装Application
	Install(infraDriver infradriver.InfraDriver) error

	// Uninstall 卸载Application
	Uninstall(infraDriver infradriver.InfraDriver) error

	// Upgrade 升级Application
	Upgrade(infraDriver infradriver.InfraDriver) error

	Save() error
}
