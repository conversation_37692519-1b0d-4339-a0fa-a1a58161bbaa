package filedriver

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"path/filepath"

	"github.com/imdario/mergo"
	"github.com/pkg/errors"
	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/env"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
	"gopkg.in/yaml.v3"
	"k8s.io/klog/v2"
)

func NewFileProcessor(appFile appv1beta1.AppFile) (FileProcessor, error) {
	switch appFile.Strategy {
	case appv1beta1.OverWriteStrategy:
		return overWriteProcessor{appFile}, nil
	case appv1beta1.MergeStrategy:
		return mergeProcessor{appFile}, nil
	}

	return nil, fmt.Errorf("failed to init fileProcessor,%s is not register", appFile.Strategy)
}

// overWriteProcessor this will overwrite the FilePath with the Values.
type overWriteProcessor struct {
	appv1beta1.AppFile
}

func (r overWriteProcessor) Process(appRoot string) error {
	target := filepath.Join(appRoot, r.Path)

	klog.V(5).Infof("will do overwrite processor on the file : %s", target)
	err := rw.NewCommonWriter(target).WriteFile([]byte(r.Data))
	if err != nil {
		return fmt.Errorf("failed to write to file %s with raw mode: %v", target, err)
	}
	return nil
}

// mergeProcessor :this will merge the FilePath with the Values.
// Only files in yaml format are supported.
// if Strategy is "merge" will deeply merge each yaml file section.
type mergeProcessor struct {
	appv1beta1.AppFile
}

func (m mergeProcessor) Process(appRoot string) error {
	var (
		result     [][]byte
		srcDataMap = make(map[string]interface{})
	)
	if err := yaml.Unmarshal([]byte(m.Data), &srcDataMap); err != nil {
		return fmt.Errorf("failed to load config data: %v", err)
	}
	target := filepath.Join(appRoot, m.Path)
	klog.V(5).Infof("will do merge processor on the file : %s", target)

	f, err := os.Open(filepath.Clean(target))
	if err != nil {
		return err
	}

	dec := yaml.NewDecoder(f)
	for {
		destDataMap := make(map[string]interface{})
		err = dec.Decode(destDataMap)
		if errors.Is(err, io.EOF) {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to unmarshal config data: %v", err)
		}

		err = mergo.Merge(&destDataMap, &srcDataMap, mergo.WithOverride)
		if err != nil {
			return fmt.Errorf("failed to merge config: %v", err)
		}

		out, err := yaml.Marshal(destDataMap)
		if err != nil {
			return err
		}

		result = append(result, out)
	}
	if err = rw.NewCommonWriter(target).WriteFile(bytes.Join(result, []byte("\n---\n"))); err != nil {
		return fmt.Errorf("failed to write to file %s with raw mode: %v", target, err)
	}
	return nil
}

// EnvRender :this will render the FilePath with the Values.
type EnvRender struct {
	EnvData map[string]string
}

func (e EnvRender) Process(appRoot string) error {
	if len(e.EnvData) == 0 {
		return nil
	}
	klog.V(5).Infof("will render the dir : %s with the values: %+v\n", appRoot, e.EnvData)

	return env.RenderTemplate(appRoot, e.EnvData)
}
