package appdriver

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"syscall"

	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	filedriver "gitlab.bingosoft.net/bingokube/kubepilot/pkg/app/appdriver/file-driver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	entapp "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application/v1beta1"
	entv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	imageutils "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image/util"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/plugin"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/maps"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/strs"
	"k8s.io/klog/v2"
)

type AppDriver struct {
	app        *appv1beta1.Application
	launchApps []string
	// registeredApps is the app name list which registered in image extension at build stage.
	registeredApps []string
	// globalCmds is raw cmds without any application info
	globalCmds []string
	// globalEnv is global env registered in image extension
	globalEnv map[string]string
	// appLaunchCmdsMap contains the whole appLaunchCmds with app name as its key.
	appLaunchCmdsMap map[string][]string
	// appDeleteCmdsMap    map[string][]string
	// extension is ImageExtension
	extension entv1beta1.ImageExtension
	// appRootMap contains the whole app root with app name as its key.
	appRootMap map[string]string
	// appEnvMap contains the whole app env with app name as its key.
	appEnvMap map[string]map[string]string
	// appFileProcessorMap contains the whole FileProcessors with app name as its key.
	appFileProcessorMap map[string][]filedriver.FileProcessor
	// handler plugin manager
	handler plugin.Handler
}

func (a *AppDriver) GetAppLaunchCmds(appName string) []string {
	return a.appLaunchCmdsMap[appName]
}

func (a *AppDriver) GetAppNames() []string {
	return a.launchApps
}

func (a *AppDriver) GetAppRoot(appName string) string {
	return a.appRootMap[appName]
}

func (a *AppDriver) SetPluginHandler(handler plugin.Handler) {
	a.handler = handler
}

func (a *AppDriver) GetImageLaunchCmds() []string {
	if a.globalCmds != nil {
		return a.globalCmds
	}
	var cmds []string

	for _, appName := range a.launchApps {
		if appCmds, ok := a.appLaunchCmdsMap[appName]; ok {
			cmds = append(cmds, appCmds...)
		}
	}

	return cmds
}

func (a *AppDriver) GetApplication() appv1beta1.Application {
	return *a.app
}

func (a *AppDriver) SetApplication(app *appv1beta1.Application) {
	a.app = app
}

// Install 启动相关应用程序
func (a *AppDriver) Install(infraDriver infradriver.InfraDriver) error {
	var (
		rootfsPath = infraDriver.GetClusterRootfsPath()
		masters    = infraDriver.GetHostIPListByRole(consts.MASTER)
		workers    = infraDriver.GetHostIPListByRole(consts.NODE)
		master0    = masters[0]
		all        = append(masters, workers...)
		launchCmds = a.GetImageLaunchCmds()
	)
	if a.handler != nil {
		if err := a.handler.RunOnHosts(plugin.PreInstallApp, all); err != nil {
			return err
		}
		defer func() {
			if err := a.handler.RunOnHosts(plugin.PostInstallApp, all); err != nil {
				return
			}
		}()
	}

	// TODO 是否需要return?
	if len(a.GetAppNames()) > 0 {
		klog.Infof("start to install applications: %s", a.GetAppNames())
	}
	if len(launchCmds) > 0 {
		klog.V(5).Infof("will to install applications with cmd: %s", launchCmds)
	}
	for _, cmdline := range launchCmds {
		if cmdline == "" {
			continue
		}
		if err := infraDriver.CmdAsync(master0, nil, fmt.Sprintf(constants.CdAndExecCmd, fmt.Sprintf("%s/%s", rootfsPath, a.app.GetUID()), cmdline)); err != nil {
			return err
		}
	}

	return nil
}

// Uninstall 卸载相关应用程序
func (a *AppDriver) Uninstall(infraDriver infradriver.InfraDriver) error {
	var (
		rootfsPath = infraDriver.GetClusterRootfsPath()
		masters    = infraDriver.GetHostIPListByRole(consts.MASTER)
		workers    = infraDriver.GetHostIPListByRole(consts.NODE)
		master0    = masters[0]
		all        = append(masters, workers...)
	)

	if a.handler != nil {
		if err := a.handler.RunOnHosts(plugin.PreUninstallApp, all); err != nil {
			return err
		}
		defer func() {
			if err := a.handler.RunOnHosts(plugin.PostUninstallApp, all); err != nil {
				return
			}
		}()
	}

	// TODO 是否需要return?
	if len(a.GetAppNames()) > 0 {
		klog.Infof("start to uninstall applications: %s", a.GetAppNames())
	}

	name, _, err := imageutils.ParseComponentFromImageName(a.app.GetName())
	if err != nil {
		return err
	}
	destPath := filepath.Join(rootfsPath, string(a.app.GetUID()))
	cmd := fmt.Sprintf("bash %s", filepath.Join(constants.Scripts, fmt.Sprintf("%s-uninstall.sh", name)))
	return infraDriver.CmdAsync(master0, nil, fmt.Sprintf(constants.CdAndExecCmd, destPath, cmd))
}

// Upgrade 升级相关应用程序
func (a *AppDriver) Upgrade(infraDriver infradriver.InfraDriver) error {
	var (
		rootfsPath = infraDriver.GetClusterRootfsPath()
		masters    = infraDriver.GetHostIPListByRole(consts.MASTER)
		workers    = infraDriver.GetHostIPListByRole(consts.NODE)
		master0    = masters[0]
		all        = append(masters, workers...)
	)

	if a.handler != nil {
		if err := a.handler.RunOnHosts(plugin.PreUpgradeApp, all); err != nil {
			return err
		}
		defer func() {
			if err := a.handler.RunOnHosts(plugin.PostUpgradeApp, all); err != nil {
				return
			}
		}()
	}

	// TODO 是否需要return?
	if len(a.GetAppNames()) > 0 {
		klog.Infof("start to upgrade applications: %s", a.GetAppNames())
	}

	name, _, err := imageutils.ParseComponentFromImageName(a.app.GetName())
	if err != nil {
		return err
	}
	destPath := filepath.Join(rootfsPath, string(a.app.GetUID()))
	cmd := fmt.Sprintf("bash %s", filepath.Join(constants.Scripts, fmt.Sprintf("%s-upgrade.sh", name)))
	return infraDriver.CmdAsync(master0, nil, fmt.Sprintf(constants.CdAndExecCmd, destPath, cmd))
}

// Save application install history
// TODO save to cluster, also need a save struct.
func (a *AppDriver) Save() error {
	applicationFile := file.GetDefaultApplicationFile()

	f, err := os.OpenFile(filepath.Clean(applicationFile), os.O_RDWR|os.O_APPEND|os.O_CREATE, 0600)
	if err != nil {
		return err
	}
	defer func() {
		_ = f.Close()
	}()
	// fd 获取链接的文件描述符
	if err = syscall.Flock(int(f.Fd()), syscall.LOCK_EX|syscall.LOCK_NB); err != nil {
		return fmt.Errorf("cannot flock file %s - %s", applicationFile, err)
	}
	defer func() {
		if err = syscall.Flock(int(f.Fd()), syscall.LOCK_UN); err != nil {
			klog.Errorf("failed to unlock [%s]", applicationFile)
		}
	}()

	// TODO do not need all ImageExtension
	content, err := json.MarshalIndent(a.extension, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal image extension: %v", err)
	}
	_, err = f.Write(content)

	return err
}

func (a *AppDriver) FileProcess(mountDir string) error {
	for appName, processors := range a.appFileProcessorMap {
		for _, fp := range processors {
			if err := fp.Process(filepath.Join(mountDir, a.GetAppRoot(appName))); err != nil {
				return fmt.Errorf("failed to process appFiles for %s: %v", appName, err)
			}
		}
	}
	return nil
}

func NewAppDriver(app *appv1beta1.Application, ext entv1beta1.ImageExtension) (*AppDriver, error) {
	appDriver := formatImageExtension(ext)
	appDriver.app = app
	// initialize globalCmds, overwrite default cmds from image extension.
	if len(app.Spec.Cmds) > 0 {
		appDriver.globalCmds = app.Spec.Cmds
	}

	// initialize appNames field, overwrite default app names from image extension.
	if app.Spec.LaunchApps != nil {
		// validate app.Spec.LaunchApps, if not in image extension,will return error
		// NOTE: app name =="" is valid
		for _, wanted := range app.Spec.LaunchApps {
			if len(wanted) == 0 {
				continue
			}
			if !strs.IsInSlice(wanted, appDriver.registeredApps) {
				return nil, fmt.Errorf("app name `%s` is not found in %s", wanted, appDriver.registeredApps)
			}
		}

		appDriver.launchApps = app.Spec.LaunchApps
	}

	// initialize Configs field
	for _, config := range app.Spec.Configs {
		if config.Name == "" {
			return nil, fmt.Errorf("application configs name could not be nil")
		}

		name := config.Name
		// make sure config in launchApps, if not will ignore this config.
		if !strs.IsInSlice(name, appDriver.launchApps) {
			continue
		}

		if config.Launch != nil {
			launchCmds := parseLaunchCmds(config.Launch)
			if launchCmds == nil {
				return nil, fmt.Errorf("failed to get launchCmds from application configs")
			}
			appDriver.appLaunchCmdsMap[name] = launchCmds
		}

		// merge config env with extension env
		if len(config.Env) > 0 {
			appEnvFromExtension := appDriver.appEnvMap[name]
			appEnvFromConfig := strs.ConvertStringSliceToMap(config.Env)
			appDriver.appEnvMap[name] = maps.Merge(appEnvFromConfig, appEnvFromExtension)
		}

		// initialize app FileProcessors
		var fileProcessors []filedriver.FileProcessor
		if len(appDriver.appEnvMap[name]) > 0 {
			fileProcessors = append(fileProcessors, filedriver.EnvRender{EnvData: appDriver.appEnvMap[name]})
		}

		for _, appFile := range config.Files {
			fp, err := filedriver.NewFileProcessor(appFile)
			if err != nil {
				return nil, err
			}
			fileProcessors = append(fileProcessors, fp)
		}
		appDriver.appFileProcessorMap[name] = fileProcessors
	}

	return appDriver, nil
}

func formatImageExtension(extension entv1beta1.ImageExtension) *AppDriver {
	appDriver := &AppDriver{
		extension:           extension,
		globalCmds:          extension.Launch.Cmds,
		globalEnv:           extension.Env,
		launchApps:          extension.Launch.AppNames,
		registeredApps:      []string{},
		appLaunchCmdsMap:    map[string][]string{},
		appRootMap:          map[string]string{},
		appEnvMap:           map[string]map[string]string{},
		appFileProcessorMap: map[string][]filedriver.FileProcessor{},
	}

	for _, registeredApp := range extension.Applications {
		appName := registeredApp.Name()
		// initialize app name
		appDriver.registeredApps = append(appDriver.registeredApps, appName)

		// initialize app root path
		appRoot := makeItDir(filepath.Join(entapp.AppRootRelPath, appName))
		appDriver.appRootMap[appName] = appRoot

		// initialize app LaunchCmds
		app := registeredApp.(*v1beta1.Application)
		appDriver.appLaunchCmdsMap[appName] = []string{v1beta1.GetAppLaunchCmd(appRoot, app)}

		// initialize app env
		appDriver.appEnvMap[appName] = maps.Merge(app.AppEnv, extension.Env)

		// initialize app FileProcessors
		if len(appDriver.appEnvMap[appName]) > 0 {
			appDriver.appFileProcessorMap[appName] = []filedriver.FileProcessor{filedriver.EnvRender{EnvData: appDriver.appEnvMap[appName]}}
		}
	}

	return appDriver
}

// parseLaunchCmds parse shell, kube,helm type launch cmds
// kubectl apply -n kube-system -f ns.yaml -f app.yaml
// helm install my-nginx bitnami/nginx
// key1=value1 key2=value2 && bash install1.sh && bash install2.sh
func parseLaunchCmds(launch *appv1beta1.Launch) []string {
	if launch.Cmds != nil {
		return launch.Cmds
	}
	// TODO add shell,helm,kube type cmds.
	return nil
}

func makeItDir(str string) string {
	if !strings.HasSuffix(str, "/") {
		return str + "/"
	}
	return str
}
