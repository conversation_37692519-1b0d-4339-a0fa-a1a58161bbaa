package app

import (
	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/app/appdriver"
	entv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
)

func NewApplicationDriver(app *appv1beta1.Application, ext entv1beta1.ImageExtension) (ApplicationDriver, error) {
	return appdriver.NewAppDriver(app, ext)
}
