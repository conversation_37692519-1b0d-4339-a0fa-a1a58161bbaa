package cmds

import "fmt"

const (
	App         = "app"
	AppCmds     = "appcmds"
	Launch      = "launch"
	Cmds        = "cmds"
	CNI         = "cni"
	CSI         = "csi"
	KUBEVERSION = "kubeversion"

	Label      = "label"
	Maintainer = "maintainer"

	Env    = "env"
	AppEnv = "appenv"

	// Deprecated
	Cmd = "cmd"

	// the following commands are the intenal implementations for kube commands
	Add  = "add"
	Arg  = "arg"
	Copy = "copy"
	From = "from"
	Run  = "run"
)

const (
	LabelSupportedKubeVersionAlpha = "app.alpha.bingokube.bingosoft.net/supported-kube-version"
	LabelSupportedKubeCNIAlpha     = "cluster.bingokube.bingosoft.net/kube-cni"
	LabelSupportedKubeCSIAlpha     = "cluster.bingokube.bingosoft.net/kube-csi"
)

var (
	LabelKubeCNIPrefix = fmt.Sprintf("%s-", LabelSupportedKubeCNIAlpha)
	LabelKubeCSIPrefix = fmt.Sprintf("%s-", LabelSupportedKubeCSIAlpha)
)

// SupportedCommands is list of all Kubefile commands
var SupportedCommands = map[string]struct{}{
	Add:         {},
	Arg:         {},
	Copy:        {},
	From:        {},
	Label:       {},
	Maintainer:  {},
	Run:         {},
	App:         {},
	AppCmds:     {},
	Launch:      {},
	Cmds:        {},
	Cmd:         {},
	CNI:         {},
	CSI:         {},
	KUBEVERSION: {},
	Env:         {},
	AppEnv:      {},
}
