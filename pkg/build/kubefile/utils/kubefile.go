package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	pkgp "github.com/containers/buildah/pkg/parse"
	"github.com/moby/buildkit/frontend/dockerfile/shell"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/build/kubefile/cmds"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application/version"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imge "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"k8s.io/klog/v2"
)

// LegacyContext stores legacy information during the process of parsing.
// After the parsing ends, return the context to caller, and let the caller
// decide to clean.
type LegacyContext struct {
	files       []string
	directories []string
	// this is a map for appname to related-files
	// only used in test.
	apps2Files map[string][]string
}

type KubefileResult struct {
	// convert Kubefile to Dockerfile content line by line.
	Dockerfile string

	// RawCmds to launch cluster image
	// CMDS ["kubectl apply -f recommended.yaml"]
	RawCmds []string

	// LaunchedAppNames APP name list
	// LAUNCH ["myapp1","myapp2"]
	LaunchedAppNames []string

	// GlobalEnv is a set of key value pair.
	// set to cluster image some default parameters which is in global level.
	// user could overwrite it through v2.ClusterSpec at run stage.
	GlobalEnv map[string]string

	// AppEnv is a set of key value pair.
	// it is app level, only this app will be aware of its existence,
	// it is used to render app files, or as an environment variable for app startup and deletion commands
	// it takes precedence over GlobalEnv.
	AppEnvMap map[string]map[string]string

	// Applications structured APP instruction and register it to this map
	// APP myapp local://app.yaml
	Applications map[string]version.Interface

	// AppCmdsMap structured APPCMDS instruction and register it to this map
	// APPCMDS myapp ["kubectl apply -f app.yaml"]
	AppCmdsMap map[string][]string

	legacyContext LegacyContext
}

type pathFunc func(name string) string

type KubefileDecoder struct {
	appRootPathFunc pathFunc
	// path to build context
	buildContext string
	platform     string
	pullPolicy   string
	imageEngine  imge.ImageManager
}

func (kd *KubefileDecoder) ParseKubefile(rwc io.Reader) (*KubefileResult, error) {
	result, err := parse(rwc)
	if err != nil {
		return nil, fmt.Errorf("failed to parse dockerfile: %v", err)
	}

	mainNode := result.AST
	return kd.buildResult(mainNode)
}

func (kd *KubefileDecoder) buildResult(mainNode *Node) (*KubefileResult, error) {
	var (
		result = &KubefileResult{
			Applications: map[string]version.Interface{},
			AppCmdsMap:   map[string][]string{},
			GlobalEnv:    map[string]string{},
			AppEnvMap:    map[string]map[string]string{},
			legacyContext: LegacyContext{
				files:       []string{},
				directories: []string{},
				apps2Files:  map[string][]string{},
			},
			RawCmds:          []string{},
			LaunchedAppNames: []string{},
		}

		err error

		launchCnt = 0
		cmdsCnt   = 0
		cmdCnt    = 0
	)

	defer func() {
		if err != nil {
			if cleanErr := result.CleanLegacyContext(); cleanErr != nil {
				klog.Warning(cleanErr)
			}
		}
	}()

	// pre-action for commands
	// for FROM, it will try to pull the image, and get apps from "FROM" image
	// for LAUNCH, it will check if it's the last line
	for i, node := range mainNode.Children {
		cmd := node.Value
		if _, ok := cmds.SupportedCommands[cmd]; !ok {
			return nil, errors.Errorf("command %s is not supported", cmd)
		}

		switch cmd {
		case cmds.From:
			// process FROM aims to pull the image, and merge the applications from
			// the FROM image.
			if err = kd.processFrom(node, result); err != nil {
				return nil, fmt.Errorf("failed to process from: %v", err)
			}
		case cmds.Launch:
			launchCnt++
			if launchCnt > 1 {
				return nil, errors.New("only one launch could be specified")
			}
			if i != len(mainNode.Children)-1 {
				return nil, errors.New("launch should be the last instruction")
			}
		case cmds.Cmds:
			cmdsCnt++
			if cmdsCnt > 1 {
				return nil, errors.New("only one cmds could be specified")
			}
			if i != len(mainNode.Children)-1 {
				return nil, errors.New("cmds should be the last instruction")
			}

		case cmds.Cmd:
			cmdCnt++
			if cmdCnt > 1 {
				break
			}
			klog.Warningf("The cmd [%s] is about to be deprecated.", cmd)
		}

		if cmdCnt >= 1 && launchCnt == 1 {
			return nil, errors.New("cmd and launch are mutually exclusive")
		}

		if err = kd.processOnCmd(result, node); err != nil {
			return nil, err
		}
	}

	// check result validation
	// if no app type detected and no AppCmds exist for this app, will return error.
	for name, registered := range result.Applications {
		if registered.Type() != "" {
			continue
		}

		if _, ok := result.AppCmdsMap[name]; !ok {
			return nil, fmt.Errorf("app %s need to specify APPCMDS` if no app type was detected", name)
		}
	}

	// register app with app env list.
	for appName, appEnv := range result.AppEnvMap {
		app := result.Applications[appName]
		app.SetEnv(appEnv)
		result.Applications[appName] = app
	}

	// register app with app cmds.
	for appName, appCmds := range result.AppCmdsMap {
		app := result.Applications[appName]
		app.SetCmds(appCmds)
		result.Applications[appName] = app
	}

	return result, nil
}

func (kd *KubefileDecoder) processOnCmd(result *KubefileResult, node *Node) error {
	cmd := node.Value
	switch cmd {
	case cmds.Label, cmds.Maintainer, cmds.Add, cmds.Arg, cmds.From, cmds.Run:
		result.Dockerfile = mergeLines(result.Dockerfile, node.Original)
		return nil
	case cmds.Env:
		// update global env to dockerfile at the same,	for using it at build stage.
		result.Dockerfile = mergeLines(result.Dockerfile, node.Original)
		return kd.processGlobalEnv(node, result)
	case cmds.AppEnv:
		return kd.processAppEnv(node, result)
	case cmds.App:
		_, err := kd.processApp(node, result)
		return err
	case cmds.AppCmds:
		return kd.processAppCmds(node, result)
	case cmds.CNI:
		return kd.processCNI(node, result)
	case cmds.CSI:
		return kd.processCSI(node, result)
	case cmds.KUBEVERSION:
		return kd.processKubeVersion(node, result)
	case cmds.Launch:
		return kd.processLaunch(node, result)
	case cmds.Cmds:
		return kd.processCmds(node, result)
	case cmds.Copy:
		return kd.processCopy(node, result)
	case cmds.Cmd:
		return kd.processCmd(node, result)
	default:
		return fmt.Errorf("failed to recognize cmd: %s", cmd)
	}
}

func (kd *KubefileDecoder) processCNI(node *Node, result *KubefileResult) error {
	app, err := kd.processApp(node, result)
	if err != nil {
		return err
	}
	dockerFileInstruction := fmt.Sprintf(`LABEL %s%s="true"`, cmds.LabelKubeCNIPrefix, app.Name())
	result.Dockerfile = mergeLines(result.Dockerfile, dockerFileInstruction)
	return nil
}

func (kd *KubefileDecoder) processCSI(node *Node, result *KubefileResult) error {
	app, err := kd.processApp(node, result)
	if err != nil {
		return err
	}
	dockerFileInstruction := fmt.Sprintf(`LABEL %s%s="true"`, cmds.LabelKubeCSIPrefix, app.Name())
	result.Dockerfile = mergeLines(result.Dockerfile, dockerFileInstruction)
	return nil
}

func (kd *KubefileDecoder) processKubeVersion(node *Node, result *KubefileResult) error {
	kubeVersionValue := node.Next.Value
	dockerFileInstruction := fmt.Sprintf(`LABEL %s=%s`, cmds.LabelSupportedKubeVersionAlpha, strconv.Quote(kubeVersionValue))
	result.Dockerfile = mergeLines(result.Dockerfile, dockerFileInstruction)
	return nil
}

func (kd *KubefileDecoder) processCopy(node *Node, result *KubefileResult) error {
	if node.Next == nil || node.Next.Next == nil {
		return fmt.Errorf("line %d: invalid copy instruction: %s", node.StartLine, node.Original)
	}

	copySrc := node.Next.Value
	copyDest := node.Next.Next.Value
	// support ${arch} on Kubefile COPY instruction
	// For example:
	// if arch is amd64
	// `COPY ${ARCH}/* .` will be mutated to `COPY amd64/* .`
	// `COPY $ARCH/* .` will be mutated to `COPY amd64/* .`
	_, arch, _, err := pkgp.Platform(kd.platform)
	if err != nil {
		return fmt.Errorf("failed to parse platform: %v", err)
	}

	ex := shell.NewLex('\\')
	src, err := ex.ProcessWordWithMap(copySrc, map[string]string{"ARCH": arch})
	if err != nil {
		return fmt.Errorf("failed to render COPY instruction: %v", err)
	}

	tmpLine := strings.Join(append([]string{cmds.Copy}, src, copyDest), " ")
	result.Dockerfile = mergeLines(result.Dockerfile, tmpLine)

	return nil
}

func (kd *KubefileDecoder) processAppCmds(node *Node, result *KubefileResult) error {
	appNode := node.Next
	appName := appNode.Value

	if appName == "" {
		return errors.New("app name should be specified in the APPCMDS instruction")
	}

	tmpPrefix := fmt.Sprintf("%s %s", strings.TrimSpace(strings.ToUpper(cmds.AppCmds)), strings.TrimSpace(appName))
	appCmdsStr := strings.TrimSpace(strings.TrimPrefix(node.Original, tmpPrefix))

	var appCmds []string
	if err := json.Unmarshal([]byte(appCmdsStr), &appCmds); err != nil {
		return errors.Wrapf(err, `the APPCMDS value should be format: APPCMDS appName ["executable","param1","param2","..."]`)
	}

	// check whether the app name exist
	var appExisted bool
	for existAppName := range result.Applications {
		if existAppName == appName {
			appExisted = true
		}
	}
	if !appExisted {
		return fmt.Errorf("the specified app name(%s) for `APPCMDS` should be exist", appName)
	}

	result.AppCmdsMap[appName] = appCmds
	return nil
}

func (kd *KubefileDecoder) processAppEnv(node *Node, result *KubefileResult) error {
	var (
		appName = ""
		envList []string
	)

	// first node value is the command
	for ptr := node.Next; ptr != nil; ptr = ptr.Next {
		val := ptr.Value
		// record the first word to be the app name
		if appName == "" {
			appName = val
			continue
		}
		envList = append(envList, val)
	}

	if appName == "" {
		return errors.New("app name should be specified in the APPENV instruction")
	}

	if _, ok := result.Applications[appName]; !ok {
		return fmt.Errorf("the specified app name(%s) for `APPENV` should be exist", appName)
	}

	tmpEnv := make(map[string]string)
	for _, elem := range envList {
		var kv []string
		if kv = strings.SplitN(elem, "=", 2); len(kv) != 2 {
			continue
		}
		tmpEnv[kv[0]] = kv[1]
	}

	appEnv := result.AppEnvMap[appName]
	if appEnv == nil {
		appEnv = make(map[string]string)
	}

	for k, v := range tmpEnv {
		appEnv[k] = v
	}

	result.AppEnvMap[appName] = appEnv
	return nil
}

func (kd *KubefileDecoder) processGlobalEnv(node *Node, result *KubefileResult) error {
	valueList := strings.SplitN(node.Original, "ENV ", 2)
	if len(valueList) != 2 {
		return fmt.Errorf("line %d: invalid ENV instruction: %s", node.StartLine, node.Original)
	}
	envs := valueList[1]

	for _, elem := range strings.Split(envs, " ") {
		if elem == "" {
			continue
		}

		var kv []string
		if kv = strings.SplitN(elem, "=", 2); len(kv) != 2 {
			continue
		}
		result.GlobalEnv[kv[0]] = kv[1]
	}

	return nil
}

func (kd *KubefileDecoder) processCmd(node *Node, result *KubefileResult) error {
	original := node.Original
	cmd := strings.Split(original, "CMD ")
	node.Next.Value = cmd[1]
	result.RawCmds = append(result.RawCmds, node.Next.Value)
	return nil
}

func (kd *KubefileDecoder) processCmds(node *Node, result *KubefileResult) error {
	cmdsNode := node.Next
	for iter := cmdsNode; iter != nil; iter = iter.Next {
		result.RawCmds = append(result.RawCmds, iter.Value)
	}
	return nil
}

func (kd *KubefileDecoder) processLaunch(node *Node, result *KubefileResult) error {
	appNode := node.Next
	for iter := appNode; iter != nil; iter = iter.Next {
		appName := iter.Value
		appName = strings.TrimSpace(appName)
		if _, ok := result.Applications[appName]; !ok {
			return errors.Errorf("application %s does not exist in the image", appName)
		}
		result.LaunchedAppNames = append(result.LaunchedAppNames, appName)
	}

	return nil
}

func (kd *KubefileDecoder) processFrom(node *Node, result *KubefileResult) error {
	var (
		platform  = pkgp.DefaultPlatform()
		flags     = node.Flags
		imageNode = node.Next
	)
	if len(flags) > 0 {
		f, err := parseListFlag(flags[0])
		if err != nil {
			return err
		}
		if f.flag != "platform" {
			return errors.Errorf("flag %s is not available in FROM", f.flag)
		}
		platform = f.items[0]
	}

	if imageNode == nil || len(imageNode.Value) == 0 {
		return errors.Errorf("image should be specified in the FROM")
	}
	img := imageNode.Value
	if img == "scratch" {
		return nil
	}

	id, err := kd.imageEngine.Pull(&image.PullOpts{
		SkipTLSVerify: true,
		PullPolicy:    kd.pullPolicy,
		Image:         img,
		Platform:      platform,
	})
	if err != nil {
		return fmt.Errorf("failed to pull image %s: %v", img, err)
	}

	imageSpec, err := kd.imageEngine.Inspect(&image.InspectOpts{ImageNameOrID: id})
	if err != nil {
		return fmt.Errorf("failed to get image-extension %s: %s", img, err)
	}

	for _, app := range imageSpec.ImageExtension.Applications {
		theApp := app
		result.Applications[app.Name()] = theApp
	}

	return nil
}

// CleanLegacyContext 清除构建镜像残留的上下文
func (kr *KubefileResult) CleanLegacyContext() error {
	var (
		lc  = kr.legacyContext
		err error
	)

	for _, f := range lc.files {
		err = os.Remove(f)
	}

	for _, dir := range lc.directories {
		err = os.RemoveAll(dir)
	}
	// wrap 处理err
	return errors.Wrap(err, "failed to clean legacy context")
}

// NewKubefileDecoder init Kubefile decoder
func NewKubefileDecoder(rootPath string, buildOpts image.BuildOpts, e imge.ImageManager, platform string) *KubefileDecoder {
	return &KubefileDecoder{
		appRootPathFunc: func(name string) string {
			return makeItDir(filepath.Join(rootPath, name))
		},
		buildContext: buildOpts.ContextDir,
		platform:     platform,
		pullPolicy:   buildOpts.PullPolicy,
		imageEngine:  e,
	}
}
