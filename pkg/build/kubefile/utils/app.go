package utils

import (
	"fmt"
	"os"
	"strings"

	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/build/kubefile/cmds"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application/version"
)

func (kd *KubefileDecoder) processApp(node *Node, result *KubefileResult) (version.Interface, error) {
	var (
		appName     = ""
		localFiles  []string
		remoteFiles []string
		filesToCopy []string
	)

	// first node value is the command
	for ptr := node.Next; ptr != nil; ptr = ptr.Next {
		val := ptr.Value
		// record the first word to be the app name
		if appName == "" {
			appName = val
			continue
		}
		switch {
		//
		case isLocal(val):
			localFiles = append(localFiles, trimLocal(val))
		case isRemote(val):
			remoteFiles = append(remoteFiles, val)
		default:
			return nil, errors.New("source schema should be specified with {https://} or {http://} or {local://} in APP")
		}
	}

	if appName == "" {
		return nil, errors.New("app name should be specified in the app cmd")
	}

	// TODO clean the app directory first before putting files into it.
	// this will rely on the storage interface
	if len(localFiles) > 0 {
		filesToCopy = append(filesToCopy, localFiles...)
	}

	// for the remote files
	// 1. create a temp dir under the build context
	// 2. download remote files to the temp dir
	// 3. append the temp files to filesToCopy
	if len(remoteFiles) > 0 {
		tmpDir, err := os.MkdirTemp(kd.buildContext, "kubepilot-remote-files")
		if err != nil {
			return nil, errors.Errorf("failed to create remote context: %s", err)
		}

		files, err := downloadRemoteFiles(tmpDir, remoteFiles)
		if err != nil {
			return nil, err
		}

		filesToCopy = append(filesToCopy, files...)
		// append it to the legacy.
		// it will be deleted by CleanContext
		result.legacyContext.directories = append(result.legacyContext.directories, tmpDir)
	}

	destDir := kd.appRootPathFunc(appName)
	tmpLine := strings.Join(append([]string{cmds.Copy}, append(filesToCopy, destDir)...), " ")
	result.Dockerfile = mergeLines(result.Dockerfile, tmpLine)
	result.legacyContext.apps2Files[appName] = append([]string{}, filesToCopy...)

	return makeItAsApp(appName, filesToCopy, result)
}

func makeItAsApp(appName string, filesToJudge []string, result *KubefileResult) (version.Interface, error) {
	appType, err := getApplicationType(filesToJudge)
	if err != nil {
		return nil, fmt.Errorf("failed to judge the application type for %s: %v", appName, err)
	}

	launchFiles, err := getApplicationFiles(appName, appType, filesToJudge)
	if err != nil {
		return nil, fmt.Errorf("failed to get app (%s)launch files: %v", appName, err)
	}

	v1App := v1beta1.NewBetaApplication(
		appName,
		appType,
		launchFiles,
	).(*v1beta1.Application)
	result.Applications[v1App.Name()] = v1App
	return v1App, nil
}

func downloadRemoteFiles(shadowDir string, files []string) ([]string, error) {
	var (
		downloaded []string
		err        error
	)

	for _, src := range files {
		var filePath string
		filePath, err = getFileFromURL(src, "", shadowDir)
		if err != nil {
			return nil, errors.Errorf("failed to download file %s, %s", src, err)
		}
		downloaded = append(downloaded, filePath)
	}
	return downloaded, nil
}
