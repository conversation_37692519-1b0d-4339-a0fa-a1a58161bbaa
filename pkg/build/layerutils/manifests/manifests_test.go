package manifest

/* func TestListImages(t *testing.T) {
	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			"test list manifests images",
			args{"my_cluster"},
			[]string{"registry.k8s.io/etcd:3.4.13-0", "registry.k8s.io/kube-apiserver:v1.19.7", "registry.k8s.io/kube-controller-manager:v1.19.7", "registry.k8s.io/kube-scheduler:v1.19.7"},
			false,
		},
	}
	manifests, _ := NewManifests()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := manifests.ListImages(tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("ListImages() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			sort.Strings(got)
			sort.Strings(tt.want)
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>rf("ListImages() got = %v, want %v", got, tt.want)
			}
		})
	}
}
*/
