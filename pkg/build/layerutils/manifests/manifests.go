package manifest

import (
	"fmt"
	"os"
	"path/filepath"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/build/layerutils"
)

type Manifests struct{}

// ListImages List all the containers images in manifest files
func (manifests *Manifests) ListImages(yamlFile string) ([]string, error) {
	var list []string

	yamlBytes, err := os.ReadFile(filepath.Clean(yamlFile))
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %s", err)
	}

	images := layerutils.DecodeImages(string(yamlBytes))
	if len(images) != 0 {
		list = append(list, images...)
	}
	return list, nil
}

func NewManifests() (layerutils.Interface, error) {
	return &Manifests{}, nil
}
