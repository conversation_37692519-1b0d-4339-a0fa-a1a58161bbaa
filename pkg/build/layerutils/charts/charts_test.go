package charts

import (
	"reflect"
	"sort"
	"testing"
)

func TestListImages(t *testing.T) {
	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			"test list charts images",
			args{"./testcharts/apps"},
			[]string{"nginx:apps_release", "nginx:app1_release", "nginx:app2_test"},
			false,
		},
	}
	charts, _ := NewCharts()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := charts.ListImages(tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("ListImages() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			sort.Strings(got)
			sort.Strings(tt.want)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ListImages() got = %v, want %v", got, tt.want)
			}
		})
	}
}
