package charts

import (
	"fmt"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/build/layerutils"
)

type Charts struct{}

// ListImages List all the containers images in helm charts
// TODO Current logic will lead to leave the unreserved image
// The image will be ignored if it has been written in the yaml(image: docker.io/xxx)
func (charts *Charts) ListImages(chartPath string) ([]string, error) {
	var list []string
	images, err := GetImageList(chartPath)
	if err != nil {
		return list, fmt.Errorf("failed to get images chart path(%s), err: %s", chartPath, err)
	}
	if len(images) != 0 {
		list = append(list, images...)
	}
	return list, nil
}

func NewCharts() (layerutils.Interface, error) {
	return &Charts{}, nil
}
