package charts

import (
	"reflect"
	"sort"
	"testing"
)

func TestGetImageList(t *testing.T) {
	type args struct {
		chartPath string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			"test get image list in chart",
			args{"./testcharts/apps"},
			[]string{"nginx:apps_release", "nginx:app1_release", "nginx:app2_test"},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			images, err := GetImageList(tt.args.chartPath)
			if (err != nil) != tt.wantErr {
				t.E<PERSON>rf("GetImageList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			sort.Strings(images)
			sort.Strings(tt.want)
			if !reflect.DeepEqual(images, tt.want) {
				t.Errorf("GetImageList() error get %v, want %v", images, tt.want)
				return
			}
		})
	}
}
