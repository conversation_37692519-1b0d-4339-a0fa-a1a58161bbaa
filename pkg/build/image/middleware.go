package image

import (
	"fmt"
	"path/filepath"

	entv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/image/store"
)

type ImgSection struct {
	Registry string   `json:"registry,omitempty"`
	Username string   `json:"username,omitempty"`
	Password string   `json:"password,omitempty"`
	Images   []string `json:"images,omitempty"`
}

type MiddlewarePuller struct {
	puller   store.DefaultImageSaver
	platform entv1beta1.Platform
}

func NewMiddlewarePuller(platform entv1beta1.Platform) MiddlewarePuller {
	return MiddlewarePuller{
		platform: platform,
		puller:   store.DefaultImageSaver{},
	}
}

func (m MiddlewarePuller) PullWithImageSection(rootfs string, imageSectionList []ImgSection) error {
	auth := make(store.ImageListWithAuth, 0)
	for _, section := range imageSectionList {
		if len(section.Images) == 0 {
			continue
		}
		if section.Username == "" || section.Password == "" {
			return fmt.Errorf("must set username and password at imageListWithAuth.yaml")
		}

		domainToImages, err := normalizedImageListWithAuth(section)
		if err != nil {
			return err
		}

		auth = append(auth, store.Section{
			Registry: section.Registry,
			Username: section.Username,
			Password: section.Password,
			Images:   domainToImages,
		})
	}

	if len(auth) == 0 {
		return nil
	}

	return m.puller.SaveImagesWithAuth(auth, filepath.Join(rootfs, constants.RegistryDirName), m.platform)
}

func normalizedImageListWithAuth(sec ImgSection) (map[string][]store.Named, error) {
	domainToImages := make(map[string][]store.Named)
	for _, image := range sec.Images {
		named, err := store.ParseNormalizedNamed(image, sec.Registry)
		if err != nil {
			return nil, fmt.Errorf("unable parse image name: %v", err)
		}
		domainToImages[named.Domain()+named.Repo()] = append(domainToImages[named.Domain()+named.Repo()], named)
	}
	return domainToImages, nil
}
