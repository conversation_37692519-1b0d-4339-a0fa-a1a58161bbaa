package image

import (
	"context"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	disreference "github.com/docker/distribution/reference"
	"github.com/pkg/errors"
	apiv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/build/layerutils/charts"
	manifest "gitlab.bingosoft.net/bingokube/kubepilot/pkg/build/layerutils/manifests"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application"
	entv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/image/store"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/strs"
	"golang.org/x/sync/errgroup"
	"k8s.io/klog/v2"
)

var (
	copyToManifests   = "manifests"
	copyToChart       = "charts"
	copyToImageList   = "imageList"
	copyToApplication = "application"
)

type parseContainerImageStringSliceFunc func(srcPath string) ([]string, error)
type parseContainerImageListFunc func(srcPath string) ([]*entv1beta1.ContainerImage, error)

var parseContainerImageStringSliceFuncMap = map[string]func(srcPath string) ([]string, error){
	copyToManifests:   parseYamlImages,
	copyToChart:       parseChartImages,
	copyToImageList:   parseRawImageList,
	copyToApplication: WrapParseContainerImageList2StringSlice(extractApplicationImages),
}

var parseContainerImageListFuncMap = map[string]func(srcPath string) ([]*entv1beta1.ContainerImage, error){
	copyToManifests:   WrapParseStringSlice2ContainerImageList(parseYamlImages),
	copyToChart:       WrapParseStringSlice2ContainerImageList(parseChartImages),
	copyToImageList:   WrapParseStringSlice2ContainerImageList(parseRawImageList),
	copyToApplication: extractApplicationImages,
}

type Registry struct {
	platform apiv1beta1.Platform
	puller   store.ImageStore
}

func NewRegistry(platform apiv1beta1.Platform) *Registry {
	ctx := context.Background()
	return &Registry{
		platform: platform,
		puller:   store.NewImageSaver(ctx),
	}
}

// SaveImages 将cluster image中涉及的docker镜像都拉取下来保存缓存到临时文件夹`registry`中
// 该镜像包括charts包的和manifest/imageList文件中的镜像列表
func (r *Registry) SaveImages(rootfs string, containerImages []string) error {
	return r.puller.StoreImages(containerImages, filepath.Join(rootfs, constants.RegistryDirName), r.platform)
}

func WrapParseStringSlice2ContainerImageList(parseFunc parseContainerImageStringSliceFunc) func(srcPath string) ([]*entv1beta1.ContainerImage, error) {
	return func(srcPath string) ([]*entv1beta1.ContainerImage, error) {
		images, err := parseFunc(srcPath)
		if err != nil {
			return nil, err
		}
		var containerImageList []*entv1beta1.ContainerImage
		for _, image := range images {
			containerImageList = append(containerImageList, &entv1beta1.ContainerImage{
				Image:   image,
				AppName: "",
			})
		}
		return containerImageList, nil
	}
}

func WrapParseContainerImageList2StringSlice(parseFunc parseContainerImageListFunc) func(srcPath string) ([]string, error) {
	return func(srcPath string) ([]string, error) {
		containerImageList, err := parseFunc(srcPath)
		if err != nil {
			return nil, err
		}
		return entv1beta1.GetImageSliceFromContainerImageList(containerImageList), nil
	}
}

// ParseContainerImageList 提取容器镜像
func ParseContainerImageList(srcPath string) ([]*entv1beta1.ContainerImage, error) {
	eg, _ := errgroup.WithContext(context.Background())

	var containerImageList []*entv1beta1.ContainerImage
	for t, p := range parseContainerImageListFuncMap {
		dispatchType := t
		parse := p
		eg.Go(func() error {
			parsedImageList, err := parse(srcPath)
			if err != nil {
				return fmt.Errorf("failed to parse images from %s: %v", dispatchType, err)
			}
			for _, image := range parsedImageList {
				img, err := disreference.ParseNormalizedNamed(image.Image)
				if err != nil {
					continue
				}
				containerImageList = append(containerImageList, &entv1beta1.ContainerImage{
					Image:    img.String(),
					AppName:  image.AppName,
					Platform: image.Platform,
				})
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return nil, err
	}
	return containerImageList, nil
}

func ParseContainerImageSlice(srcPath string) ([]string, error) {
	eg, _ := errgroup.WithContext(context.Background())

	var images []string
	for t, p := range parseContainerImageStringSliceFuncMap {
		dispatchType := t
		parse := p
		eg.Go(func() error {
			ima, err := parse(srcPath)
			if err != nil {
				return fmt.Errorf("failed to parse images from %s: %v", dispatchType, err)
			}
			images = append(images, ima...)
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return nil, err
	}
	return images, nil
}

// extractApplicationImages Extract images from application
func extractApplicationImages(srcPath string) ([]*entv1beta1.ContainerImage, error) {
	applicationPath := filepath.Clean(filepath.Join(srcPath, application.AppRootRelPath))

	if !file.IsFileExist(applicationPath) {
		return nil, nil
	}

	var (
		containerImageList []*entv1beta1.ContainerImage
		err                error
	)

	entries, err := os.ReadDir(applicationPath)
	if err != nil {
		return nil, fmt.Errorf("failed read from path %s: %v", applicationPath, err)
	}
	for _, entry := range entries {
		name := entry.Name()
		appPath := filepath.Join(applicationPath, name)
		if entry.IsDir() {
			if !isChartArtifactEnough(appPath) {
				imagesTmp, err := parseApplicationKubeImages(appPath)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to parse container image list for app(%s) with type(%s)",
						name, application.KubeApp)
				}
				for _, image := range imagesTmp {
					containerImageList = append(containerImageList, &entv1beta1.ContainerImage{
						Image:   image,
						AppName: name,
					})
				}
				continue
			}

			imagesTmp, err := extractApplicationHelmImages(appPath)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to parse container image list for app(%s) with type(%s)",
					name, application.HelmApp)
			}
			for _, image := range imagesTmp {
				containerImageList = append(containerImageList, &entv1beta1.ContainerImage{
					Image:   image,
					AppName: name,
				})
			}
		}
	}

	return containerImageList, nil
}

// extractApplicationHelmImages Extract images from app helm charts
// 从helm包中的文件夹中提取涉及到的镜像
func extractApplicationHelmImages(helmPath string) ([]string, error) {
	if !file.IsFileExist(helmPath) {
		return nil, nil
	}

	var images []string

	imageSearcher, err := charts.NewCharts()
	if err != nil {
		return nil, err
	}

	err = filepath.Walk(helmPath, func(path string, f fs.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !f.IsDir() {
			return nil
		}

		if isChartArtifactEnough(path) {
			imgs, err := imageSearcher.ListImages(path)
			if err != nil {
				return err
			}

			images = append(images, imgs...)
			return filepath.SkipDir
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return FormatImages(images), nil
}

func parseApplicationKubeImages(kubePath string) ([]string, error) {
	if !file.IsFileExist(kubePath) {
		return nil, nil
	}
	var images []string
	imageSearcher, err := manifest.NewManifests()
	if err != nil {
		return nil, err
	}

	err = filepath.Walk(kubePath, func(path string, f fs.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if f.IsDir() {
			return nil
		}

		ext := strings.ToLower(filepath.Ext(f.Name()))
		if ext != ".yaml" && ext != ".yml" && ext != ".tmpl" {
			return nil
		}

		ima, err := imageSearcher.ListImages(path)

		if err != nil {
			return err
		}
		images = append(images, ima...)
		return nil
	})

	if err != nil {
		return nil, err
	}
	return FormatImages(images), nil
}

func parseChartImages(srcPath string) ([]string, error) {
	chartsPath := filepath.Join(srcPath, copyToChart)
	if !file.IsFileExist(chartsPath) {
		return nil, nil
	}

	var images []string
	imageSearcher, err := charts.NewCharts()
	if err != nil {
		return nil, err
	}

	err = filepath.Walk(chartsPath, func(path string, f fs.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !f.IsDir() {
			return nil
		}

		if isChartArtifactEnough(path) {
			ima, err := imageSearcher.ListImages(path)
			if err != nil {
				return err
			}
			images = append(images, ima...)
			return filepath.SkipDir
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return FormatImages(images), nil
}

func parseYamlImages(srcPath string) ([]string, error) {
	manifestsPath := filepath.Join(srcPath, copyToManifests)
	if !file.IsFileExist(manifestsPath) {
		return nil, nil
	}
	var images []string

	imageSearcher, err := manifest.NewManifests()
	if err != nil {
		return nil, err
	}

	err = filepath.Walk(manifestsPath, func(path string, f fs.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if f.IsDir() {
			return nil
		}

		ext := strings.ToLower(filepath.Ext(f.Name()))
		if ext != ".yaml" && ext != ".yml" && ext != ".tmpl" {
			return nil
		}

		ima, err := imageSearcher.ListImages(path)

		if err != nil {
			return err
		}
		images = append(images, ima...)
		return nil
	})

	if err != nil {
		return nil, err
	}
	return FormatImages(images), nil
}

func parseRawImageList(srcPath string) ([]string, error) {
	imageListFilePath := filepath.Join(srcPath, copyToManifests, copyToImageList)
	if !file.IsFileExist(imageListFilePath) {
		return nil, nil
	}

	images, err := rw.NewFileReader(imageListFilePath).ReadLines()
	if err != nil {
		return nil, fmt.Errorf("failed to read file content %s:%v", imageListFilePath, err)
	}
	klog.V(5).Infof("List of images from manifest/imageList: [%s]", images)
	res := FormatImages(images)

	return res, nil
}

var isChartArtifactEnough = func(path string) bool {
	return file.IsFileExist(filepath.Join(path, "Chart.yaml")) &&
		file.IsFileExist(filepath.Join(path, "values.yaml")) &&
		file.IsFileExist(filepath.Join(path, "templates"))
}

func FormatImages(images []string) []string {
	var res []string
	for _, img := range images {
		tmpImg := strings.TrimSpace(img)
		tmpImg = strings.Trim(tmpImg, `'"`)
		tmpImg = strings.TrimSpace(tmpImg)
		if strings.HasPrefix(tmpImg, "#") || tmpImg == "" {
			continue
		}
		res = append(res, tmpImg)
	}
	res = strs.RemoveDuplicate(res)
	return res
}
