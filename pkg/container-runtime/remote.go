package containerruntime

import (
	"fmt"
	"net"
	"path/filepath"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
)

type RemoteInstaller struct {
	RuntimeOpts
	envs   map[string]string
	rootfs string
	driver infradriver.InfraDriver
}

func (d *RemoteInstaller) InstallOn(hosts []net.IP) error {
	installCmd := fmt.Sprintf("bash %s", filepath.Join(d.rootfs, constants.Scripts, d.getInstallScriptName()))
	for _, ip := range hosts {
		err := d.driver.CmdAsync(ip, d.envs, installCmd)
		if err != nil {
			return fmt.Errorf("failed to install %s: execute command(%s) on host (%s): error(%v)", d.Type, installCmd, ip, err)
		}
	}
	return nil
}

func (d *RemoteInstaller) UnInstallFrom(hosts []net.IP) error {
	cleanCmd := fmt.Sprintf("bash %s", filepath.Join(d.rootfs, constants.Scripts, d.getUnInstallScriptName()))
	for _, ip := range hosts {
		err := d.driver.CmdAsync(ip, d.envs, cleanCmd)
		if err != nil {
			return fmt.Errorf("failed to uninstall %s: execute command(%s) on host (%s): error(%v)", d.Type, cleanCmd, ip, err)
		}
	}
	return nil
}

func (d *RemoteInstaller) GetInfo() (RuntimeOpts, error) {
	return d.RuntimeOpts, nil
}

func (d *RemoteInstaller) getInstallScriptName() string {
	return fmt.Sprintf("%s.sh", d.Type)
}

func (d *RemoteInstaller) getUnInstallScriptName() string {
	return fmt.Sprintf("uninstall-%s.sh", d.Type)
}
