package containerruntime

import (
	"fmt"
	"path/filepath"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
)

type RuntimeOpts struct {
	ctv1beta1.ContainerRuntimeConfig
	CgroupDriver   string
	CRISocket      string
	CertsDir       string
	ConfigFilePath string
}

func NewInstaller(conf ctv1beta1.ContainerRuntimeConfig, driver infradriver.InfraDriver) (Installer, error) {
	switch conf.Type {
	case constants.Docker, "":
		conf.Type = constants.Docker
		ret := &RemoteInstaller{
			rootfs: driver.GetClusterRootfsPath(),
			driver: driver,
			envs:   driver.GetClusterEnv(),
			RuntimeOpts: RuntimeOpts{
				CertsDir:               constants.DefaultDockerCertsDir,
				CRISocket:              constants.DefaultDockerCRISocket,
				ContainerRuntimeConfig: conf,
				ConfigFilePath:         filepath.Join(file.GetHomeDir(), ".docker", constants.DockerConfigFileName),
			},
		}
		ret.RuntimeOpts.CgroupDriver = constants.DefaultCgroupDriver
		if cd, ok := ret.envs[constants.CgroupDriverArg]; ok && cd != "" {
			ret.RuntimeOpts.CgroupDriver = cd
		}

		return ret, nil
	case constants.Containerd:
		ret := &RemoteInstaller{
			rootfs: driver.GetClusterRootfsPath(),
			driver: driver,
			envs:   driver.GetClusterEnv(),
			RuntimeOpts: RuntimeOpts{
				CertsDir:               constants.DefaultContainerdCertsDir,
				CRISocket:              constants.DefaultContainerdCRISocket,
				ContainerRuntimeConfig: conf,
			},
		}
		ret.RuntimeOpts.CgroupDriver = constants.DefaultCgroupDriver
		if cd, ok := ret.envs[constants.CgroupDriverArg]; ok && cd != "" {
			ret.RuntimeOpts.CgroupDriver = cd
		}

		return ret, nil
	default:
		return nil, fmt.Errorf("invalid container runtime type")
	}
}
