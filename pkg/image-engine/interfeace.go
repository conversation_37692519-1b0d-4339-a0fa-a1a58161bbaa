package imageengine

import (
	"github.com/containers/common/libimage"
	"github.com/opencontainers/go-digest"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	entv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine/buildah"
)

var (
	_ ImageManager = &buildah.Engine{}
)

// ImageManager 定义拓展OCI archive镜像文件的基本行为，
// 实现方式可以有多种(Buildah、Skopeo、Podman等)
type ImageManager interface {
	Build(opts *image.BuildOpts) (string, error)

	Load(opts *image.LoadOpts) error

	CreateContainer(opts *image.FromOpts) (string, error)

	CreateWorkingContainer(opts *image.BuildRootfsOpts) (string, error)

	Mount(opts *image.MountOpts) ([]image.JSONMount, error)

	Copy(opts *image.CopyOpts) error

	Commit(opts *image.CommitOpts) (string, error)

	Config(opts *image.ConfigOpts) error

	Login(opts *image.LoginOpts) error

	Logout(opts *image.LogoutOpts) error

	Push(opts *image.PushOpts) error

	Pull(opts *image.PullOpts) (string, error)

	Images(opts *image.ImagesOpts) error

	Save(opts *image.SaveOpts) error

	RemoveImage(opts *image.RemoveImageOpts) error

	RemoveContainer(opts *image.RemoveContainerOpts) error

	Tag(opts *image.TagOpts) error

	Inspect(opts *image.InspectOpts) (*entv1beta1.ImageSpec, error)

	LookupManifest(name string) (*libimage.ManifestList, error)

	CreateManifest(name string, opts *image.ManifestCreateOpts) (string, error)

	DeleteManifests(names []string, opts *image.ManifestDeleteOpts) error

	InspectManifest(name string, opts *image.ManifestInspectOpts) (*libimage.ManifestListData, error)

	PushManifest(name, destSpec string, opts *image.PushOpts) error

	AddToManifest(name string, imageNameOrIDList []string, opts *image.ManifestAddOpts) error

	RemoveFromManifest(name string, instanceDigest digest.Digest, opts *image.ManifestRemoveOpts) error
}
