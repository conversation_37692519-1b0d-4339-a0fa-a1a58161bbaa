package buildah

import (
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/containers/buildah"
	"github.com/containers/buildah/define"
	buildahcli "github.com/containers/buildah/pkg/cli"
	"github.com/containers/buildah/pkg/parse"
	"github.com/containers/buildah/util"
	"github.com/containers/common/pkg/config"
	oconfig "github.com/containers/ocicrypt/config"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"k8s.io/klog/v2"
)

// createContainerFromImage create a working container. This function is copied from
// "buildah from". This function takes args([]string{"$image"}), and create a working container
// based on $image, this will generate an empty dictionary, not a real rootfs. And this container is a fake container.

func (e *Engine) CreateContainer(opts *image.FromOpts) (string, error) {
	wrapper := &fromFlagsWrapper{
		FromAndBudResults: &buildahcli.FromAndBudResults{},
		UserNSResults:     &buildahcli.UserNSResults{},
		NameSpaceResults:  &buildahcli.NameSpaceResults{},
	}

	flags := e.Flags()
	fromAndBudFlags, err := buildahcli.GetFromAndBudFlags(wrapper.FromAndBudResults, wrapper.UserNSResults, wrapper.NameSpaceResults)
	if err != nil {
		return "", err
	}

	flags.AddFlagSet(&fromAndBudFlags)

	err = e.migrateFlags2BuildahFrom(opts)
	if err != nil {
		return "", err
	}

	return e.createContainerFromImage(opts)
}

func (e *Engine) createContainerFromImage(opts *image.FromOpts) (string, error) {
	defaultContainerConfig, err := config.Default()
	if err != nil {
		return "", errors.Wrapf(err, "failed to get container config")
	}

	if len(opts.Image) == 0 {
		return "", errors.Errorf("an image name (or \"scratch\") must be specified")
	}

	// TODO be aware of this, maybe this will incur platform problem.
	systemCxt := e.SystemContext()

	// TODO we do not support from remote currently
	// which is to make the policy pull-if-missing
	pullPolicy := define.PullNever

	store := e.ImgStore()

	commonOpts, err := parse.CommonBuildOptions(e.Command)
	if err != nil {
		return "", err
	}

	isolation, err := defaultIsolationOption()
	if err != nil {
		return "", err
	}

	namespaceOptions, networkPolicy := defaultNamespaceOptions()

	usernsOption, idmappingOptions, err := parse.IDMappingOptions(e.Command, isolation)
	if err != nil {
		return "", errors.Wrapf(err, "error parsing ID mapping options")
	}
	namespaceOptions.AddOrReplace(usernsOption...)

	// hardcode format here, user do not concern about this.
	format, err := getImageType(define.OCI)
	if err != nil {
		return "", err
	}

	capabilities, err := defaultContainerConfig.Capabilities("", []string{}, []string{})
	if err != nil {
		return "", err
	}

	commonOpts.Ulimit = append(defaultContainerConfig.Containers.DefaultUlimits, commonOpts.Ulimit...)

	options := buildah.BuilderOptions{
		FromImage:             opts.Image,
		Container:             "",
		ContainerSuffix:       "",
		PullPolicy:            pullPolicy,
		SystemContext:         systemCxt,
		DefaultMountsFilePath: "",
		Isolation:             isolation,
		NamespaceOptions:      namespaceOptions,
		ConfigureNetwork:      networkPolicy,
		CNIPluginPath:         "",
		CNIConfigDir:          "",
		IDMappingOptions:      idmappingOptions,
		Capabilities:          capabilities,
		CommonBuildOpts:       commonOpts,
		Format:                format,
		DefaultEnv:            defaultContainerConfig.GetDefaultEnv(),
		MaxPullRetries:        maxPullPushRetries,
		PullRetryDelay:        pullPushRetryDelay,
		OciDecryptConfig:      &oconfig.DecryptConfig{},
	}

	if !opts.Quiet {
		options.ReportWriter = os.Stderr
	}

	builder, err := buildah.NewBuilder(getContext(), store, options)
	if err != nil {
		return "", err
	}

	if err := onBuild(builder, opts.Quiet); err != nil {
		return "", err
	}

	return builder.ContainerID, builder.Save()
}

// CreateWorkingContainer will make a workingContainer with rootfs under /var/lib/containers/storage
// And then link rootfs to the DestDir
// And remember to call RemoveContainer to remove the link and remove the container(umount rootfs) manually.
func (e *Engine) CreateWorkingContainer(opts *image.BuildRootfsOpts) (containerID string, err error) {
	// TODO clean environment when it fails
	cid, err := e.CreateContainer(&image.FromOpts{
		Image: opts.ImageNameOrID,
		Quiet: false,
	})
	if err != nil {
		return "", err
	}

	mounts, err := e.Mount(&image.MountOpts{Containers: []string{cid}})
	if err != nil {
		return "", err
	}

	// remove destination dir if it exists, otherwise the Symlink will fail.
	if _, err = os.Stat(opts.DestDir); err == nil {
		return "", fmt.Errorf("destination directionay %s exists, you should remove it first", opts.DestDir)
	}

	mountPoint := mounts[0].MountPoint
	return cid, os.Symlink(mountPoint, opts.DestDir)
}

func (e *Engine) migrateFlags2BuildahFrom(opts *image.FromOpts) error {
	return nil
}

func onBuild(builder *buildah.Builder, quiet bool) error {
	ctr := 0
	for _, onBuildSpec := range builder.OnBuild() {
		ctr = ctr + 1
		commands := strings.Split(onBuildSpec, " ")
		command := strings.ToUpper(commands[0])
		args := commands[1:]
		if !quiet {
			fmt.Fprintf(os.Stderr, "STEP %d: %s\n", ctr, onBuildSpec)
		}
		switch command {
		case "ADD":
		case "COPY":
			dest := ""
			var srcs []string
			size := len(args)
			if size > 1 {
				dest = args[size-1]
				srcs = args[:size-1]
			}
			if err := builder.Add(dest, command == "ADD", buildah.AddAndCopyOptions{}, srcs...); err != nil {
				return err
			}
		case "ANNOTATION":
			annotation := strings.SplitN(args[0], "=", 2)
			if len(annotation) > 1 {
				builder.SetAnnotation(annotation[0], annotation[1])
			} else {
				builder.UnsetAnnotation(annotation[0])
			}
		case "CMD":
			builder.SetCmd(args)
		case "ENV":
			env := strings.SplitN(args[0], "=", 2)
			if len(env) > 1 {
				builder.SetEnv(env[0], env[1])
			} else {
				builder.UnsetEnv(env[0])
			}
		case "ENTRYPOINT":
			builder.SetEntrypoint(args)
		case "EXPOSE":
			builder.SetPort(strings.Join(args, " "))
		case "HOSTNAME":
			builder.SetHostname(strings.Join(args, " "))
		case "LABEL":
			label := strings.SplitN(args[0], "=", 2)
			if len(label) > 1 {
				builder.SetLabel(label[0], label[1])
			} else {
				builder.UnsetLabel(label[0])
			}
		case "MAINTAINER":
			builder.SetMaintainer(strings.Join(args, " "))
		case "ONBUILD":
			builder.SetOnBuild(strings.Join(args, " "))
		case "RUN":
			var stdout io.Writer
			if quiet {
				stdout = io.Discard
			}
			if err := builder.Run(args, buildah.RunOptions{Stdout: stdout}); err != nil {
				return err
			}
		case "SHELL":
			builder.SetShell(args)
		case "STOPSIGNAL":
			builder.SetStopSignal(strings.Join(args, " "))
		case "USER":
			builder.SetUser(strings.Join(args, " "))
		case "VOLUME":
			builder.AddVolume(strings.Join(args, " "))
		case "WORKINGDIR":
			builder.SetWorkDir(strings.Join(args, " "))
		default:
			return errors.Errorf("illegal command input %q; ignored", command)
		}
	}
	builder.ClearOnBuild()
	return nil
}

func (e *Engine) RemoveContainer(opts *image.RemoveContainerOpts) error {
	if len(opts.ContainerNamesOrIDs) == 0 && !opts.All {
		return fmt.Errorf("container name of id must be specified")
	}
	if len(opts.ContainerNamesOrIDs) > 0 && opts.All {
		return fmt.Errorf("all can't be true if the containers are specified")
	}

	var lastError error
	var delContainerErrStr = "error removing container"
	store := e.ImgStore()
	if opts.All {
		builders, err := buildah.OpenAllBuilders(store)
		if err != nil {
			return errors.Wrapf(err, "error reading build containers")
		}

		for _, builder := range builders {
			id := builder.ContainerID
			if err = builder.Delete(); err != nil {
				lastError = util.WriteError(os.Stderr, errors.Wrapf(err, "%s %q", delContainerErrStr, builder.Container), lastError)
				continue
			}
			klog.V(5).Infof("%s", id)
		}
	} else {
		for _, name := range opts.ContainerNamesOrIDs {
			builder, err := OpenBuilder(getContext(), store, name)
			if err != nil {
				lastError = util.WriteError(os.Stderr, errors.Wrapf(err, "%s %q", delContainerErrStr, name), lastError)
				continue
			}
			id := builder.ContainerID
			if err = builder.Delete(); err != nil {
				lastError = util.WriteError(os.Stderr, errors.Wrapf(err, "%s %q", delContainerErrStr, name), lastError)
				continue
			}
			klog.V(5).Infof("%s", id)
		}
	}
	return lastError
}
