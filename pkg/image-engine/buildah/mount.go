package buildah

import (
	"fmt"
	"os"

	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
)

func (e *Engine) Mount(opts *image.MountOpts) ([]image.JSONMount, error) {
	containers := opts.Containers
	if len(containers) == 0 {
		return []image.JSONMount{}, errors.Errorf("id/name of containers mube be specified")
	}

	store := e.ImgStore()
	var jsonMounts []image.JSONMount
	var lastError error
	// Do not allow to mount a graphdriver that is not vfs if we are creating the userns as part
	// of the mount command.
	// Differently, allow the mount if we are already in a userns, as the mount point will still
	// be accessible once "buildah mount" exits.
	if os.Geteuid() != 0 && store.GraphDriverName() != "vfs" {
		return []image.JSONMount{}, errors.Errorf("cannot mount using driver %s in rootless mode. You need to run it in a `buildah unshare` session", store.GraphDriverName())
	}

	for _, name := range containers {
		builder, err := OpenBuilder(getContext(), store, name)
		if err != nil {
			if lastError != nil {
				fmt.Fprintln(os.Stderr, lastError)
			}
			lastError = errors.Wrapf(err, "error reading build container %q", name)
			continue
		}
		mountPoint, err := builder.Mount(builder.MountLabel)
		if err != nil {
			if lastError != nil {
				fmt.Fprintln(os.Stderr, lastError)
			}
			lastError = errors.Wrapf(err, "error mounting %q container %q", name, builder.Container)
			continue
		}
		if len(containers) > 1 {
			jsonMounts = append(jsonMounts, image.JSONMount{Container: name, MountPoint: mountPoint})
			continue
		} else {
			jsonMounts = append(jsonMounts, image.JSONMount{MountPoint: mountPoint})
			continue
		}
	}

	return jsonMounts, nil
}
