package buildah

import (
	"context"

	"github.com/containers/common/libimage"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	eimg "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"k8s.io/klog/v2"
)

func (e *Engine) RemoveImage(opts *eimg.RemoveImageOpts) error {
	if len(opts.ImageNamesOrIDs) == 0 && !opts.Prune {
		return errors.Errorf("image name or ID must be specified")
	}
	if len(opts.ImageNamesOrIDs) > 0 && opts.Prune {
		return errors.Errorf("when using the --prune switch, you may not pass any images names or IDs")
	}
	options := &libimage.RemoveImagesOptions{
		Filters: []string{"readonly=false"},
	}

	if opts.Prune {
		options.Filters = append(options.Filters, "dangling=true")
	}
	options.Force = opts.Force

	// take it as image first
	rmiReports, rmiErrors := e.ImgRuntime().RemoveImages(context.Background(), opts.ImageNamesOrIDs, options)
	for _, r := range rmiReports {
		for _, u := range r.Untagged {
			klog.V(3).Infof("%s hasn't tagged", u)
		}
	}
	for _, r := range rmiReports {
		if r.Removed {
			klog.V(3).Infof("%s has been removed", r.ID)
		}
	}

	if len(rmiErrors) == 0 {
		return nil
	}

	// take it as manifestList and try again
	options.LookupManifest = true
	rmiReports, rmiErrors2 := e.ImgRuntime().RemoveImages(context.Background(), opts.ImageNamesOrIDs, options)
	for _, r := range rmiReports {
		for _, u := range r.Untagged {
			klog.V(3).Infof("%s hasn't tagged", u)
		}
	}
	for _, r := range rmiReports {
		if r.Removed {
			klog.V(3).Infof("%s has been removed", r.ID)
		}
	}

	if len(rmiErrors2) == 0 {
		return nil
	}

	var multiE *multierror.Error
	multiE = multierror.Append(multiE, append(rmiErrors, rmiErrors2...)...)

	return multiE.ErrorOrNil()
}
