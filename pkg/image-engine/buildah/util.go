package buildah

import (
	"context"
	"os"
	"path/filepath"
	"time"

	"github.com/containers/buildah"
	"github.com/containers/buildah/define"
	"github.com/containers/buildah/pkg/parse"
	"github.com/containers/common/pkg/umask"
	v5storage "github.com/containers/image/v5/storage"
	"github.com/containers/image/v5/types"
	"github.com/containers/storage"
	"github.com/containers/storage/pkg/unshare"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
)

// FindKubefile tries to find a Kubefile within the provided `path`.
func FindKubefile(path string) (foundFile string, err error) {
	// Test for existence of the file
	target, err := os.Stat(path)
	if err != nil {
		return "", errors.Wrap(err, "finding Kubefile")
	}

	switch mode := target.Mode(); {
	case mode.IsDir():
		// If the path is a real directory, we assume a Kubefile within it
		kubefile := filepath.Join(path, "Kubefile")

		// Test for existence of the Kubefile file
		file, err := os.Stat(kubefile)
		if err != nil {
			return "", errors.Wrap(err, "cannot find Kubefile in context directory")
		}

		// The file exists, now verify the correct mode
		if mode = file.Mode(); mode.IsRegular() {
			foundFile = kubefile
		} else {
			return "", errors.Errorf("assumed Kubefile %q is not a file", kubefile)
		}

	case mode.IsRegular():
		// If the context dir is a file, we assume this as Kubefile
		foundFile = path
	}

	return foundFile, nil
}

const (
	maxPullPushRetries = 3
	pullPushRetryDelay = 2 * time.Second
)

func getStore(configurations *image.EngineGlobalConfig) (storage.Store, error) {
	options, err := storage.DefaultStoreOptions(unshare.IsRootless(), unshare.GetRootlessUID())
	if err != nil {
		return nil, err
	}

	if configurations != nil {
		if len(configurations.GraphRoot) > 0 {
			options.GraphRoot = configurations.GraphRoot
		}
		if len(configurations.RunRoot) > 0 {
			options.RunRoot = configurations.RunRoot
		}
	}

	// Do not allow to mount a graphdriver that is not vfs if we are creating the userns as part
	// of the mount command.
	// Differently, allow the mount if we are already in a userns, as the mount point will still
	// be accessible once "buildah mount" exits.
	if os.Geteuid() != 0 && options.GraphDriverName != "vfs" {
		return nil, errors.Errorf("cannot mount using driver %s in rootless mode. You need to run it in a `buildah unshare` session", options.GraphDriverName)
	}

	umask.Check()

	store, err := storage.GetStore(options)
	if store != nil {
		v5storage.Transport.SetStore(store)
	}
	return store, err
}

func OpenBuilder(ctx context.Context, store storage.Store, name string) (builder *buildah.Builder, err error) {
	if name != "" {
		builder, err = buildah.OpenBuilder(store, name)
		if os.IsNotExist(errors.Cause(err)) {
			options := buildah.ImportOptions{
				Container: name,
			}
			builder, err = buildah.ImportBuilder(ctx, store, options)
		}
	}
	if err != nil {
		return nil, err
	}
	if builder == nil {
		return nil, errors.Errorf("error finding build container")
	}
	return builder, nil
}

func openImage(ctx context.Context, sc *types.SystemContext, store storage.Store, name string) (builder *buildah.Builder, err error) {
	options := buildah.ImportFromImageOptions{
		Image:         name,
		SystemContext: sc,
	}
	builder, err = buildah.ImportBuilderFromImage(ctx, store, options)
	if err != nil {
		return nil, err
	}
	if builder == nil {
		return nil, errors.Errorf("error mocking up build configuration")
	}
	return builder, nil
}

// getContext returns a context.
func getContext() context.Context {
	return context.TODO()
}

func getImageType(format string) (string, error) {
	switch format {
	case define.OCI:
		return define.OCIv1ImageManifest, nil
	case define.DOCKER:
		return define.Dockerv2ImageManifest, nil
	default:
		return "", errors.Errorf("unrecognized image type %q", format)
	}
}

func defaultIsolationOption() (define.Isolation, error) {
	return parse.IsolationOption("")
}

func defaultNamespaceOptions() (namespaceOptions define.NamespaceOptions, networkPolicy define.NetworkConfigurationPolicy) {
	options := make(define.NamespaceOptions, 0, 7)
	policy := define.NetworkEnabled
	options.AddOrReplace(define.NamespaceOption{
		Name: "network",
		Host: true,
	})

	return options, policy
}
