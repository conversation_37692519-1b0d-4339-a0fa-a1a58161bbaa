package buildah

import (
	"fmt"
	"sort"
	"strings"

	"github.com/containers/buildah"
	ociv1 "github.com/opencontainers/image-spec/specs-go/v1"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/build/kubefile/cmds"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	"k8s.io/apimachinery/pkg/util/json"
)

func (e *Engine) Inspect(opts *image.InspectOpts) (*v1beta1.ImageSpec, error) {
	if len(opts.ImageNameOrID) == 0 {
		return nil, errors.Errorf("image name or image id must be specified")
	}

	var (
		builder *buildah.Builder
		err     error
	)

	ctx := getContext()
	store := e.ImgStore()
	newSystemCxt := e.SystemContext()
	name := opts.ImageNameOrID

	builder, err = openImage(ctx, newSystemCxt, store, name)
	if err != nil {
		return nil, err
	}

	builderInfo := buildah.GetBuildInfo(builder)
	var manifest = ociv1.Manifest{}
	if err = json.Unmarshal([]byte(builderInfo.Manifest), &manifest); err != nil {
		return nil, errors.Wrapf(err, "failed to get manifest")
	}

	if len(manifest.Annotations) != 0 {
		delete(manifest.Annotations, v1beta1.KubepilotImageExtension)
		delete(manifest.Annotations, v1beta1.KubepilotImageContainerImageList)
	}

	imageExtension, err := getImageExtensionFromAnnotations(builderInfo.ImageAnnotations)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get %s in image %s", v1beta1.KubepilotImageExtension, opts.ImageNameOrID)
	}

	imageExtension.Labels = handleImageLabelOutput(builderInfo.OCIv1.Config.Labels)

	// NOTE: avoid duplicate content output
	builderInfo.OCIv1.Config.Labels = nil

	containerImageList, err := getContainerImagesFromAnnotations(builderInfo.ImageAnnotations)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get %s in image %s", v1beta1.KubepilotImageContainerImageList, opts.ImageNameOrID)
	}

	result := &v1beta1.ImageSpec{
		ID:                 builderInfo.FromImageID,
		Name:               builderInfo.FromImage,
		Digest:             builderInfo.FromImageDigest,
		ManifestV1:         manifest,
		OCIv1:              builderInfo.OCIv1,
		ImageExtension:     imageExtension,
		ContainerImageList: containerImageList,
	}

	return result, nil
}

func getImageExtensionFromAnnotations(annotations map[string]string) (v1beta1.ImageExtension, error) {
	extension := v1beta1.ImageExtension{}
	extensionStr := annotations[v1beta1.KubepilotImageExtension]
	if len(extensionStr) == 0 {
		return extension, fmt.Errorf("%s does not exist", v1beta1.KubepilotImageExtension)
	}

	if err := json.Unmarshal([]byte(extensionStr), &extension); err != nil {
		return extension, errors.Wrapf(err, "failed to unmarshal %v", v1beta1.KubepilotImageExtension)
	}
	return extension, nil
}

func getContainerImagesFromAnnotations(annotations map[string]string) ([]*v1beta1.ContainerImage, error) {
	var containerImageList []*v1beta1.ContainerImage
	annotationStr := annotations[v1beta1.KubepilotImageContainerImageList]
	if len(annotationStr) == 0 {
		return nil, nil
	}

	if err := json.Unmarshal([]byte(annotationStr), &containerImageList); err != nil {
		return nil, errors.Wrapf(err, "failed to unmarshal %v", v1beta1.KubepilotImageContainerImageList)
	}
	return containerImageList, nil
}

func handleImageLabelOutput(labels map[string]string) map[string]string {
	if len(labels) == 0 {
		return labels
	}

	var result = make(map[string]string)
	var supportedCNI []string
	var supportedCSI []string
	for k, v := range labels {
		if strings.HasPrefix(k, cmds.LabelKubeCNIPrefix) {
			supportedCNI = append(supportedCNI, strings.TrimPrefix(k, cmds.LabelKubeCNIPrefix))
			continue
		}
		if strings.HasPrefix(k, cmds.LabelKubeCSIPrefix) {
			supportedCSI = append(supportedCSI, strings.TrimPrefix(k, cmds.LabelKubeCSIPrefix))
			continue
		}
		result[k] = v
	}

	if len(supportedCNI) != 0 {
		sort.Strings(supportedCNI)
		supportedCNIJSON, _ := json.Marshal(supportedCNI)
		result[cmds.LabelSupportedKubeCNIAlpha] = string(supportedCNIJSON)
	}
	if len(supportedCSI) != 0 {
		sort.Strings(supportedCSI)
		supportedCSIJSON, _ := json.Marshal(supportedCSI)
		result[cmds.LabelSupportedKubeCSIAlpha] = string(supportedCSIJSON)
	}

	return result
}
