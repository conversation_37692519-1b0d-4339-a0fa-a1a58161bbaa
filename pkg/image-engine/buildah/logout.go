package buildah

import (
	"os"

	"github.com/containers/common/pkg/auth"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
)

func (e *Engine) Logout(opts *image.LogoutOpts) error {
	if len(opts.Domain) == 0 {
		return errors.Errorf("domain is empty,registry must be given")
	}

	systemCxt := e.SystemContext()
	return auth.Logout(systemCxt, &auth.LogoutOptions{
		AuthFile:           systemCxt.AuthFilePath,
		All:                opts.All,
		AcceptRepositories: true,
		Stdout:             os.Stdout,
	}, []string{opts.Domain})
}
