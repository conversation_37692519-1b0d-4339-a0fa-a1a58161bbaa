package buildah

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	"github.com/containers/common/libimage"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"k8s.io/klog/v2"
)

func (e *Engine) Load(opts *image.LoadOpts) error {
	imageSrc := opts.Input
	if _, err := os.Stat(imageSrc); err != nil {
		return err
	}

	loadOpts := &libimage.LoadOptions{}
	if !opts.Quiet {
		loadOpts.Writer = os.Stderr
	}

	srcFile, err := os.Open(filepath.Clean(imageSrc))
	if err != nil {
		return fmt.Errorf("failed to open %s, err : %v", imageSrc, err)
	}

	defer func() {
		if err = srcFile.Close(); err != nil {
			klog.Errorf("failed to close file: %v", err)
		}
	}()
	tempDir, err := os.MkdirTemp(opts.TmpDir, "kubepilot-load-tmp")
	if err != nil {
		return fmt.Errorf("failed to create %s, err: %v", tempDir, err)
	}

	defer func() {
		if err = os.RemoveAll(tempDir); err != nil {
			klog.Errorf("failed to delete %s: %v", tempDir, err)
		}
	}()

	// 解压tar文件
	if _, err = file.Decompress(srcFile, tempDir, file.Options{Compress: false}); err != nil {
		return err
	}

	metaFile := filepath.Join(tempDir, constants.DefaultMetadataName)
	if _, err = os.Stat(metaFile); err != nil {
		// assume it is single image to load
		return e.loadOneImage(imageSrc, loadOpts)
	}

	// get manifestName
	metaBytes, err := os.ReadFile(filepath.Clean(metaFile))
	if err != nil {
		return err
	}

	manifestName := string(metaBytes)
	// delete it if manifestName is already used
	_, err = e.ImgRuntime().LookupManifestList(manifestName)
	if err == nil {
		klog.Warningf("%s is already in use, will delete it", manifestName)
		delErr := e.DeleteManifests([]string{manifestName}, &image.ManifestDeleteOpts{})
		if delErr != nil {
			return fmt.Errorf("%s is already in use: %v", manifestName, delErr)
		}
	}

	// walk through temp dir to load each instance
	var instancesIDs []string
	// Walk函数会遍历root指定的目录下的文件树，对每一个该文件树中的目录和文件都会调用walkFn，包括root自身.
	if err = filepath.Walk(tempDir, func(path string, f fs.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// The file must have a tar suffix
		if !strings.HasSuffix(f.Name(), ".tar") {
			return nil
		}

		instanceSrc := filepath.Join(tempDir, f.Name())
		if err = e.loadOneImage(instanceSrc, loadOpts); err != nil {
			return fmt.Errorf("unable load %s from %s: %v", f.Name(), imageSrc, err)
		}
		instancesIDs = append(instancesIDs, strings.TrimSuffix(f.Name(), ".tar"))
		return nil

	}); err != nil {
		return fmt.Errorf("failed to load image instance %v", err)
	}

	// create a new manifest and add instance to it.
	if _, err = e.CreateManifest(manifestName, &image.ManifestCreateOpts{}); err != nil {
		return fmt.Errorf("failed to create new manifest %s :%v ", manifestName, err)
	}

	if err = e.AddToManifest(manifestName, instancesIDs, &image.ManifestAddOpts{}); err != nil {
		return fmt.Errorf("failed to add new image to %s :%v ", manifestName, err)
	}

	return nil
}
