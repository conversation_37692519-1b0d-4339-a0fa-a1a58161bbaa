package buildah

import (
	"fmt"
	"os"

	"github.com/containers/buildah"
	"github.com/containers/buildah/define"
	"github.com/containers/buildah/pkg/parse"
	"github.com/containers/common/pkg/auth"
	"github.com/containers/image/v5/types"
	"github.com/pkg/errors"
	entimg "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
)

func (e *Engine) Pull(opts *entimg.PullOpts) (string, error) {
	if len(opts.Image) == 0 {
		return "", errors.Errorf("an image name must be specified")
	}
	systemCxt := e.SystemContext()
	store := e.ImgStore()
	if err := auth.CheckAuthFile(systemCxt.AuthFilePath); err != nil {
		return "", err
	}

	// we need to new a systemContext instead of taking the systemContext of e,
	// because pullOption does not export platform option
	newSystemCxt := initSystemContext()

	_os, arch, variant, err := parse.Platform(opts.Platform)
	if err != nil {
		return "", errors.Errorf("failed to init platform from %s: %v", opts.Platform, err)
	}
	newSystemCxt.OSChoice = _os
	newSystemCxt.ArchitectureChoice = arch
	newSystemCxt.VariantChoice = variant
	newSystemCxt.OCIInsecureSkipTLSVerify = opts.SkipTLSVerify
	newSystemCxt.DockerInsecureSkipTLSVerify = types.NewOptionalBool(opts.SkipTLSVerify)

	policy, ok := define.PolicyMap[opts.PullPolicy]
	if !ok {
		return "", fmt.Errorf("unsupported pull policy %q", opts.PullPolicy)
	}
	options := buildah.PullOptions{
		Store:         store,
		SystemContext: newSystemCxt,
		// consider export this option later
		AllTags:      false,
		ReportWriter: os.Stderr,
		MaxRetries:   maxPullPushRetries,
		RetryDelay:   pullPushRetryDelay,
		PullPolicy:   policy,
	}

	if opts.Quiet {
		options.ReportWriter = nil // Turns off logging output
	}

	id, err := buildah.Pull(getContext(), opts.Image, options)
	if err != nil {
		return "", err
	}

	return id, nil
}
