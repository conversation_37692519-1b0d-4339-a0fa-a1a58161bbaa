package buildah

import (
	"fmt"
	"time"

	"github.com/containers/buildah"
	buildahcli "github.com/containers/buildah/pkg/cli"
	"github.com/containers/buildah/pkg/parse"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"k8s.io/klog/v2"
)

func (e *Engine) Copy(opts *image.CopyOpts) error {
	if len(opts.Container) == 0 {
		return errors.Errorf("container id is empty")
	}
	if len(opts.SourcesRel2CxtDir) == 0 {
		return errors.Errorf("src must be specified")
	}
	if len(opts.DestinationInContainer) == 0 {
		return errors.Errorf("destination in container must be specified")
	}

	name := opts.Container
	dest := opts.DestinationInContainer
	store := e.ImgStore()

	var idMappingOptions *buildah.IDMappingOptions
	contextdir := opts.ContextDir
	if opts.IgnoreFile != "" && contextdir == "" {
		return errors.Errorf("--ignorefile option requires that you specify a context dir using --contextdir")
	}

	builder, err := OpenBuilder(getContext(), store, name)
	if err != nil {
		return errors.Wrapf(err, "error reading build container %q", name)
	}

	builder.ContentDigester.Restart()

	options := buildah.AddAndCopyOptions{
		ContextDir:       contextdir,
		IDMappingOptions: idMappingOptions,
	}
	if opts.ContextDir != "" {
		var excludes []string

		excludes, options.IgnoreFile, err = parse.ContainerIgnoreFile(options.ContextDir, opts.IgnoreFile)
		if err != nil {
			return err
		}
		options.Excludes = excludes
	}

	if err = builder.Add(dest, false, options, opts.SourcesRel2CxtDir...); err != nil {
		return errors.Wrapf(err, "error adding content to container %q", builder.Container)
	}

	contentType, digest := builder.ContentDigester.Digest()
	if !opts.Quiet {
		klog.Infof("%s", digest.Hex())
	}
	if contentType != "" {
		contentType = contentType + ":"
	}
	conditionallyAddHistory(builder, opts, "/bin/sh -c #(nop) %s %s%s", "COPY", contentType, digest.Hex())

	return builder.Save()
}

func conditionallyAddHistory(builder *buildah.Builder, opts *image.CopyOpts, createdByFmt string, args ...interface{}) {
	if opts.AddHistory || buildahcli.DefaultHistory() {
		now := time.Now().UTC()
		created := &now
		createdBy := fmt.Sprintf(createdByFmt, args...)
		builder.AddPrependedEmptyLayer(created, createdBy, "", "")
	}
}
