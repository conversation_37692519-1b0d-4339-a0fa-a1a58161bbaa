package buildah

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/containers/common/pkg/umask"
	as "github.com/containers/image/v5/storage"
	"github.com/containers/storage"
	"github.com/containers/storage/pkg/unshare"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
)

const (
	policyAbsPath     = "/etc/containers/policy.json"
	registriesAbsPath = "/etc/containers/registries.conf"
	storageConfPath   = "/etc/containers/storage.conf"

	buildahEtcRegistriesConf = `
[registries.search]
registries = ['docker.io']

# Registries that do not use TLS when pulling images or uses self-signed
# certificates.
[registries.insecure]
registries = []

[registries.block]
registries = []
`

	builadhEtcPolicy = `
{
    "default": [
	{
	    "type": "insecureAcceptAnything"
	}
    ],
    "transports":
	{
	    "docker-daemon":
		{
		    "": [{"type":"insecureAcceptAnything"}]
		}
	}
}`

	kubepilotAuth = `
{
	"auths": {}
}
`

	buildahStorageConf = `
# storage.conf is the configuration file for all tools
# that share the containers/storage libraries
# See man 5 containers-storage.conf for more information
# The "container storage" table contains all of the server options.
[storage]

# Default Storage Driver
driver = "overlay"

# Temporary storage location
runroot = "/var/run/containers/storage"

# Primary Read/Write location of container storage
graphroot = "/var/lib/containers/storage"

[storage.options]
# Storage options to be passed to underlying storage drivers

# Size is used to set a maximum size of the container image.  Only supported by
# certain container storage drivers.
size = ""

[storage.options.thinpool]

# log_level sets the log level of devicemapper.
# 0: LogLevelSuppress 0 (Default)
# 2: LogLevelFatal
# 3: LogLevelErr
# 4: LogLevelWarn
# 5: LogLevelNotice
# 6: LogLevelInfo
# 7: LogLevelDebug
# log_level = "7"`
)

// TODO do we have an util or unified local storage accessing pattern?
func writeFileIfNotExist(path string, content []byte) error {
	_, err := os.Stat(path)
	if err != nil {
		err = os.MkdirAll(filepath.Dir(path), 0750)
		if err != nil {
			return err
		}

		err = os.WriteFile(path, content, 0600)
		if err != nil {
			return err
		}
	}
	return nil
}

// generateBuildahConfiguration init buildah configuration about policy storage registrry
func generateBuildahConfiguration() error {
	if err := writeFileIfNotExist(policyAbsPath, []byte(builadhEtcPolicy)); err != nil {
		return fmt.Errorf("unbale to gen policy config: %v", err)
	}
	if err := writeFileIfNotExist(registriesAbsPath, []byte(buildahEtcRegistriesConf)); err != nil {
		return fmt.Errorf("unbale to gen registry config: %v", err)
	}

	storageAbsPath := "/etc/containers/storage.conf"
	if err := writeFileIfNotExist(storageAbsPath, []byte(buildahStorageConf)); err != nil {
		return fmt.Errorf("unbale to gen storage config: %v", err)
	}

	// TODO: 是不是不应该放在这里？
	defaultAuthPath := file.DefaultAuthFilePath()
	if err := writeFileIfNotExist(defaultAuthPath, []byte(kubepilotAuth)); err != nil {
		return fmt.Errorf("unbale to gen kubepilot auth: %v", err)
	}

	return nil
}

// initStore init storage store
func initStore(config *image.EngineGlobalConfig) (storage.Store, error) {
	options, err := storage.DefaultStoreOptions(unshare.IsRootless(), unshare.GetRootlessUID())
	if err != nil {
		return nil, errors.Errorf("unbale to set default storage options: %v", err)
	}

	if config != nil {
		if len(config.GraphRoot) > 0 {
			options.GraphRoot = config.GraphRoot
		}
		if len(config.RunRoot) > 0 {
			options.RunRoot = config.RunRoot
		}
	}

	// 如果我们在挂载命令中创建用户，则不允许挂载非vfs的图形驱动程序.
	// 不同的是，如果我们已经是用户，允许挂载，因为一旦“buildah mount”退出，挂载点仍然可以访问.
	if os.Geteuid() != 0 && options.GraphDriverName != "vfs" {
		return nil, errors.Errorf("it Not allow mount the driver %s with using in rootless mode. unless run it in a `buildah unshare` session", options.GraphDriverName)
	}
	umask.Check()

	store, err := storage.GetStore(options)
	if store != nil {
		as.Transport.SetStore(store)
	}
	return store, err
}
