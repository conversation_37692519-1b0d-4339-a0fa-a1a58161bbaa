package buildah

import (
	"strings"

	"github.com/containers/buildah"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
)

func (e *Engine) Config(opts *image.ConfigOpts) error {
	if len(opts.ContainerID) == 0 {
		return errors.Errorf("container ID must be specified")
	}
	name := opts.ContainerID

	ctx := getContext()
	store := e.ImgStore()
	builder, err := OpenBuilder(ctx, store, name)
	if err != nil {
		return errors.Wrapf(err, "error reading build container %q", name)
	}

	if err := updateConfig(builder, opts); err != nil {
		return err
	}
	return builder.Save()
}

func updateConfig(builder *buildah.Builder, iopts *image.ConfigOpts) error {
	if len(iopts.Annotations) != 0 {
		for _, annotationSpec := range iopts.Annotations {
			annotation := strings.SplitN(annotationSpec, "=", 2)
			switch {
			case len(annotation) > 1:
				builder.SetAnnotation(annotation[0], annotation[1])
			case annotation[0] == "-":
				builder.ClearAnnotations()
			case strings.HasSuffix(annotation[0], "-"):
				builder.UnsetAnnotation(strings.TrimSuffix(annotation[0], "-"))
			default:
				builder.SetAnnotation(annotation[0], "")
			}
		}
	}
	return nil
}
