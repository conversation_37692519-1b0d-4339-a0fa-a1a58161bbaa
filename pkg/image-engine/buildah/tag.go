package buildah

import (
	"errors"
	"fmt"

	"github.com/containers/common/libimage"
	eimg "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
)

func (e *Engine) Tag(opts *eimg.TagOpts) error {
	name := opts.ImageNameOrID
	if len(name) == 0 {
		return errors.New("at least the image name or id should be specified")
	}
	if len(opts.Tags) == 0 {
		return errors.New("at least one new tag should be provided")
	}

	lookupOptions := &libimage.LookupImageOptions{ManifestList: true}
	existImage, _, err := e.ImgRuntime().LookupImage(name, lookupOptions)
	if err != nil {
		return fmt.Errorf("failed to lookup image: %v", err)
	}
	for _, tag := range opts.Tags {
		if err = existImage.Tag(tag); err != nil {
			return err
		}
	}

	return nil
}
