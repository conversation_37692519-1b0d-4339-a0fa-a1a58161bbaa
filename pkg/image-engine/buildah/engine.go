package buildah

import (
	"path/filepath"
	"runtime"

	"github.com/BurntSushi/toml"
	"github.com/containers/buildah/pkg/parse"
	"github.com/containers/common/libimage"
	"github.com/containers/image/v5/types"
	"github.com/containers/storage"
	"github.com/containers/storage/drivers/overlay"
	stype "github.com/containers/storage/types"
	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"k8s.io/klog/v2"
)

type Engine struct {
	*cobra.Command
	libimgRuntime *libimage.Runtime
	imgRepo       storage.Store
}

// ImgRuntime return the images runtime
func (e *Engine) ImgRuntime() *libimage.Runtime {
	return e.libimgRuntime
}

// ImgStore return storage objects operator
func (e *Engine) ImgStore() storage.Store {
	return e.imgRepo
}

// SystemContext return images context
func (e *Engine) SystemContext() *types.SystemContext {
	return e.libimgRuntime.SystemContext()
}

func checkOverlaySupported() {
	conf := stype.TomlConfig{}
	if _, err := toml.DecodeFile(storageConfPath, &conf); err != nil {
		klog.Warningf("failed to decode storage.conf, which may incur problems: %v", err)
		return
	}

	if conf.Storage.RunRoot == "" || conf.Storage.GraphRoot == "" {
		klog.Warning("runroot or graphroot is empty")
		return
	}

	// 需要检查注册的 overlay和overlay2 驱动程序.
	// 否则，会出现“覆盖”不支持的问题。这个问题与底层库问题有关,所以得检查
	if _, err := overlay.SupportsNativeOverlay(filepath.Join(conf.Storage.GraphRoot, "overlay"),
		filepath.Join(conf.Storage.RunRoot, "overlay")); err != nil {
		klog.Warningf("detect there is no native overlay supported: %v", err)
	}
}

func initSystemContext() *types.SystemContext {
	return &types.SystemContext{
		DockerRegistryUserAgent:           "Buildah/1.30.0", // 该版本尽量与rootfs中的二进制版本对应
		AuthFilePath:                      file.DefaultAuthFilePath(),
		BigFilesTemporaryDir:              parse.GetTempDir(),
		OSChoice:                          runtime.GOOS,
		ArchitectureChoice:                runtime.GOARCH,
		DockerInsecureSkipTLSVerify:       types.NewOptionalBool(false),
		OCIInsecureSkipTLSVerify:          false,
		DockerDaemonInsecureSkipTLSVerify: false,
	}
}

func NewBuildahImgEngine(configurations image.EngineGlobalConfig) (*Engine, error) {
	if err := generateBuildahConfiguration(); err != nil {
		return nil, err
	}

	checkOverlaySupported()
	// store
	store, err := initStore(&configurations)
	if err != nil {
		return nil, err
	}
	// system context
	systemctx := initSystemContext()
	imgRuntime, err := libimage.RuntimeFromStore(store, &libimage.RuntimeOptions{SystemContext: systemctx})
	if err != nil {
		return nil, err
	}

	return &Engine{
		Command:       &cobra.Command{},
		libimgRuntime: imgRuntime,
		imgRepo:       store,
	}, nil
}
