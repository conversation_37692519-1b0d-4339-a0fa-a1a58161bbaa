package buildah

import (
	"context"
	"os"
	"time"

	"github.com/containers/common/pkg/auth"
	"github.com/containers/image/v5/types"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
)

func (e *Engine) Login(opts *image.LoginOpts) error {
	if len(opts.Domain) == 0 {
		return errors.Errorf("please specify a registry to login to")
	}

	systemCxt := e.SystemContext()
	systemCxt.OCIInsecureSkipTLSVerify = opts.SkipTLSVerify
	systemCxt.DockerInsecureSkipTLSVerify = types.NewOptionalBool(opts.SkipTLSVerify)
	loginCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return auth.Login(loginCtx,
		systemCxt,
		&auth.LoginOptions{
			AuthFile:           systemCxt.AuthFilePath,
			Password:           opts.Password,
			Username:           opts.Username,
			Stdout:             os.<PERSON>,
			AcceptRepositories: true,
		},
		[]string{opts.Domain})
}
