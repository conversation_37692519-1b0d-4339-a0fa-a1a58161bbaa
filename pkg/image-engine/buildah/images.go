package buildah

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/containers/buildah/pkg/formats"
	"github.com/containers/common/libimage"
	"github.com/docker/go-units"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"k8s.io/klog/v2"
)

func (e *Engine) Images(opts *image.ImagesOpts) error {
	runtime := e.ImgRuntime()
	options := &libimage.ListImagesOptions{}
	if !opts.All {
		options.Filters = append(options.Filters, "intermediate=false")
	}

	// TODO add some label to identify kubepilot image and oci image.
	images, err := runtime.ListImages(getContext(), []string{}, options)
	if err != nil {
		return err
	}

	imageOpts := imageOptions{
		all:       opts.All,
		digests:   opts.Digests,
		json:      opts.JSON,
		noHeading: opts.NoHeading,
		truncate:  !opts.NoTrunc,
		quiet:     opts.Quiet,
		history:   opts.History,
	}

	if opts.JSON {
		return formatImagesJSON(images, imageOpts)
	}

	return formatImages(images, imageOpts)
}

func outputHeader(opts imageOptions) string {
	if opts.format != "" {
		return strings.Replace(opts.format, `\t`, "\t", -1)
	}
	if opts.quiet {
		return formats.IDString
	}
	format := "table {{.Name}}\t{{.Tag}}\t"
	if opts.noHeading {
		format = "{{.Name}}\t{{.Tag}}\t"
	}

	if opts.digests {
		format += "{{.Digest}}\t"
	}
	format += "{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"
	if opts.readOnly {
		format += "\t{{.ReadOnly}}"
	}
	if opts.history {
		format += "\t{{.History}}"
	}
	return format
}

func formatImagesJSON(images []*libimage.Image, opts imageOptions) error {
	var jsonImages []jsonImage
	for _, img := range images {
		// Copy the base data over to the output param.
		size, err := img.Size()
		if err != nil {
			return err
		}
		created := img.Created()
		jsonImages = append(jsonImages,
			jsonImage{
				CreatedAtRaw: created,
				Created:      created.Unix(),
				CreatedAt:    units.HumanDuration(time.Since(created)) + " ago",
				Digest:       img.Digest().String(),
				ID:           TruncateID(img.ID(), opts.truncate),
				Names:        img.Names(),
				ReadOnly:     img.IsReadOnly(),
				Size:         formattedSize(size),
			})
	}

	data, err := json.MarshalIndent(jsonImages, "", "    ")
	if err != nil {
		return err
	}
	klog.V(5).Infof("%s", string(data))
	return nil
}

type imagesSorted []imageOutputParams

func (a imagesSorted) Less(i, j int) bool {
	return a[i].CreatedAtRaw.After(a[j].CreatedAtRaw)
}

func (a imagesSorted) Len() int {
	return len(a)
}

func (a imagesSorted) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}

func formatImages(images []*libimage.Image, opts imageOptions) error {
	var outputData imagesSorted

	for _, img := range images {
		var outputParam imageOutputParams
		size, err := img.Size()
		if err != nil {
			return err
		}
		created := img.Created()
		outputParam.CreatedAtRaw = created
		outputParam.Created = created.Unix()
		outputParam.CreatedAt = units.HumanDuration(time.Since(created)) + " ago"
		outputParam.Digest = img.Digest().String()
		outputParam.ID = TruncateID(img.ID(), opts.truncate)
		outputParam.Size = formattedSize(size)
		outputParam.ReadOnly = img.IsReadOnly()

		repoTags, err := img.NamedRepoTags()
		if err != nil {
			return err
		}

		nameTagPairs, err := libimage.ToNameTagPairs(repoTags)
		if err != nil {
			return err
		}

		for _, pair := range nameTagPairs {
			newParam := outputParam
			newParam.Name = pair.Name
			newParam.Tag = pair.Tag
			newParam.History = formatHistory(img.NamesHistory(), pair.Name, pair.Tag)
			outputData = append(outputData, newParam)
			if opts.quiet {
				break
			}
		}
	}

	sort.Sort(outputData)
	out := formats.StdoutTemplateArray{Output: imagesToGeneric(outputData), Template: outputHeader(opts), Fields: imagesHeader}
	return formats.Writer(out).Out()
}

func formatHistory(history []string, name, tag string) string {
	if len(history) == 0 {
		return none
	}
	// Skip the first history entry if already existing as name
	if fmt.Sprintf("%s:%s", name, tag) == history[0] {
		if len(history) == 1 {
			return none
		}
		return strings.Join(history[1:], ", ")
	}
	return strings.Join(history, ", ")
}

func TruncateID(id string, truncate bool) string {
	if !truncate {
		return "sha256:" + id
	}

	if idTruncLength := 12; len(id) > idTruncLength {
		return id[:idTruncLength]
	}
	return id
}

func imagesToGeneric(templParams []imageOutputParams) (genericParams []interface{}) {
	if len(templParams) > 0 {
		for _, v := range templParams {
			genericParams = append(genericParams, interface{}(v))
		}
	}
	return genericParams
}

func formattedSize(size int64) string {
	suffixes := [5]string{"B", "KB", "MB", "GB", "TB"}

	count := 0
	formattedSize := float64(size)
	for formattedSize >= 1000 && count < 4 {
		formattedSize /= 1000
		count++
	}
	return fmt.Sprintf("%.3g %s", formattedSize, suffixes[count])
}
