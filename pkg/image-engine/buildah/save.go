package buildah

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"

	"github.com/containers/common/libimage"
	"github.com/containers/common/libimage/manifests"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	eimg "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/archive"
	utilfile "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
	"k8s.io/klog/v2"
)

// Save image as tar file, if image is multi-arch image, will save all its instances and manifest name as tar file.
func (e *Engine) Save(opts *eimg.SaveOpts) error {
	imageNameOrID := opts.ImageNameOrID
	imageTar := opts.Output
	store := e.ImgStore()

	if len(imageNameOrID) == 0 {
		return errors.New("image name or id must be specified")
	}
	if opts.Compress && (opts.Format != constants.OCIManifestDir && opts.Format != constants.V2s2ManifestDir) {
		return errors.New("--compress can only be set when --format is either 'oci-dir' or 'docker-dir'")
	}

	img, _, err := e.ImgRuntime().LookupImage(imageNameOrID, &libimage.LookupImageOptions{
		ManifestList: true,
	})
	if err != nil {
		return err
	}

	isManifest, err := img.IsManifestList(getContext())
	if err != nil {
		return err
	}

	if !isManifest {
		return e.saveOneImage(imageNameOrID, opts.Format, imageTar, opts.Compress)
	}

	// save multi-arch images :including each platform images and manifest.
	var pathsToCompress []string

	fs := utilfile.NewFileSystem()

	if err = fs.MkdirAll(filepath.Dir(imageTar)); err != nil {
		return fmt.Errorf("failed to create %s, err: %v", imageTar, err)
	}

	file, err := os.Create(filepath.Clean(imageTar))
	if err != nil {
		return fmt.Errorf("failed to create %s, err: %v", imageTar, err)
	}

	defer func() {
		if err = file.Close(); err != nil {
			klog.Errorf("failed to close file: %v", err)
		}
	}()

	tempDir, err := os.MkdirTemp(opts.TmpDir, "kubepilot-save-tmp")
	if err != nil {
		return fmt.Errorf("failed to create %s, err: %v", tempDir, err)
	}

	defer func() {
		if err = os.RemoveAll(tempDir); err != nil {
			klog.Warningf("failed to delete %s: %v", tempDir, err)
		}
	}()

	// save each platform images
	imageName := img.Names()[0]
	klog.Infof("image %q is a manifest list, looking up matching instance to save", imageNameOrID)
	manifestList, err := e.ImgRuntime().LookupManifestList(imageName)
	if err != nil {
		return err
	}

	_, list, err := manifests.LoadFromImage(store, manifestList.ID())
	if err != nil {
		return err
	}

	for _, instanceDigest := range list.Instances() {
		images, err := store.ImagesByDigest(instanceDigest)
		if err != nil {
			return err
		}

		if len(images) == 0 {
			return fmt.Errorf("no image matched with digest %s", instanceDigest)
		}

		instance := images[0]
		instanceTar := filepath.Join(tempDir, instance.ID+".tar")

		// if instance has "Names", use the first one as saved name
		instanceName := instance.ID
		if len(instance.Names) > 0 {
			instanceName = instance.Names[0]
		}

		if err = e.saveOneImage(instanceName, opts.Format, instanceTar, opts.Compress); err != nil {
			return err
		}

		pathsToCompress = append(pathsToCompress, instanceTar)
	}

	// save imageName to metadata file
	metaFile := filepath.Join(tempDir, constants.DefaultMetadataName)
	if err = rw.NewAtomicWriter(metaFile).WriteFile([]byte(imageName)); err != nil {
		return fmt.Errorf("failed to write temp file %s, err: %v ", metaFile, err)
	}
	pathsToCompress = append(pathsToCompress, metaFile)

	// tar all materials
	tarReader, err := archive.TarWithRootDir(pathsToCompress...)
	if err != nil {
		return fmt.Errorf("failed to get tar reader for %s, err: %s", imageNameOrID, err)
	}
	defer func() {
		if err = tarReader.Close(); err != nil {
			klog.Errorf("failed to close file: %v", err)
		}
	}()

	_, err = io.Copy(file, tarReader)

	return err
}

func (e *Engine) saveOneImage(imageNameOrID, format, path string, compress bool) error {
	saveOptions := &libimage.SaveOptions{
		CopyOptions: libimage.CopyOptions{
			DirForceCompress:            compress,
			OciAcceptUncompressedLayers: false,
			RemoveSignatures:            true,
		},
	}

	names := []string{imageNameOrID}
	return e.ImgRuntime().Save(context.Background(), names, format, path, saveOptions)
}
