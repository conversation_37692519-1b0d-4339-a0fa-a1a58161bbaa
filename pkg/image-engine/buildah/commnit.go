package buildah

import (
	"os"
	"time"

	"github.com/containers/buildah"
	"github.com/containers/buildah/define"
	"github.com/containers/buildah/util"
	"github.com/containers/image/v5/pkg/shortnames"
	v5storage "github.com/containers/image/v5/storage"
	ts "github.com/containers/image/v5/transports/alltransports"
	"github.com/containers/image/v5/types"
	"github.com/containers/storage"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"k8s.io/klog/v2"
)

func (e *Engine) Commit(opts *image.CommitOpts) (string, error) {
	var dest types.ImageReference
	if len(opts.ContainerID) == 0 {
		return "", errors.Errorf("container ID must be specified")
	}
	if len(opts.Image) == 0 && len(opts.Manifest) == 0 {
		return "", errors.Errorf("image name should be specified")
	}

	name := opts.ContainerID
	img := opts.Image
	manifest := opts.Manifest
	compress := define.Gzip
	if opts.DisableCompression {
		compress = define.Uncompressed
	}

	format, err := getImageType(opts.Format)
	if err != nil {
		return "", err
	}

	ctx := getContext()
	store := e.ImgStore()
	builder, err := OpenBuilder(ctx, store, name)
	if err != nil {
		return "", errors.Wrapf(err, "error reading build container %q", name)
	}

	systemCxt := e.SystemContext()

	// If the user specified an image, we may need to massage it a bit if
	// no transport is specified.
	// TODO we support commit to local image only, we'd better limit the input of name
	if img != "" {
		dest, err = imageReference(img, store, systemCxt)
		if err != nil {
			return "", err
		}
	}

	options := buildah.CommitOptions{
		PreferredManifestType: format,
		Manifest:              manifest,
		Compression:           compress,
		SystemContext:         systemCxt,
		Squash:                opts.Squash,
	}
	if opts.Timestamp != 0 {
		timestamp := time.Unix(opts.Timestamp, 0).UTC()
		options.HistoryTimestamp = &timestamp
	}

	if !opts.Quiet {
		options.ReportWriter = os.Stderr
	}
	id, ref, _, err := builder.Commit(ctx, dest, options)
	if err != nil {
		return "", util.GetFailureCause(err, errors.Wrapf(err, "error committing container %q to %q", builder.Container, img))
	}
	if ref != nil && id != "" {
		klog.V(5).Infof("wrote image %s with ID %s", ref, id)
	} else if ref != nil {
		klog.V(5).Infof("wrote image %s", ref)
	} else if id != "" {
		klog.V(5).Infof("wrote image with ID %s", id)
	} else {
		klog.V(5).Infof("wrote image")
	}

	if opts.Rm {
		return id, builder.Delete()
	}
	return id, nil
}

func imageReference(image string, store storage.Store, systemCxt *types.SystemContext) (types.ImageReference, error) {
	dest, err := ts.ParseImageName(image)
	if err == nil {
		return dest, nil
	}

	candidates, err := shortnames.ResolveLocally(systemCxt, image)
	if err != nil {
		return nil, err
	}
	if len(candidates) == 0 {
		return nil, errors.Errorf("no candidate tags for target image name %q", image)
	}
	dest, err = v5storage.Transport.ParseStoreReference(store, candidates[0].String())
	if err != nil {
		return nil, errors.Wrapf(err, "error parsing target image name %q", image)
	}

	return dest, nil
}
