package buildah

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/containers/buildah/define"
	"github.com/containers/buildah/imagebuildah"
	buildahcli "github.com/containers/buildah/pkg/cli"
	"github.com/containers/buildah/pkg/parse"
	pkgutil "github.com/containers/buildah/pkg/util"
	"github.com/containers/buildah/util"
	"github.com/containers/common/libimage"
	"github.com/containers/common/pkg/auth"
	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	"k8s.io/klog/v2"
)

func (e *Engine) Build(opts *image.BuildOpts) (string, error) {
	// The following block is to init buildah default options.
	// And call migrateFlags2BuildahBuild to set flags based on kubepilot build options.
	wrapper := &buildFlagsOpts{
		BudResults:        &buildahcli.BudResults{},
		LayerResults:      &buildahcli.LayerResults{},
		FromAndBudResults: &buildahcli.FromAndBudResults{},
		NameSpaceResults:  &buildahcli.NameSpaceResults{},
		UserNSResults:     &buildahcli.UserNSResults{},
	}

	flags := e.Flags()
	buildFlags := buildahcli.GetBudFlags(wrapper.BudResults)
	buildFlags.StringVar(&wrapper.Runtime, "runtime", util.Runtime(), "`path` to an alternate runtime. Use BUILDAH_RUNTIME environment variable to override.")

	layerFlags := buildahcli.GetLayerFlags(wrapper.LayerResults)
	fromAndBudFlags, err := buildahcli.GetFromAndBudFlags(wrapper.FromAndBudResults, wrapper.UserNSResults, wrapper.NameSpaceResults)
	if err != nil {
		return "", fmt.Errorf("failed to setup From and Build flags: %v", err)
	}

	flags.AddFlagSet(&buildFlags)
	flags.AddFlagSet(&layerFlags)
	flags.AddFlagSet(&fromAndBudFlags)
	flags.SetNormalizeFunc(buildahcli.AliasFlags)

	if err = e.migrateFlags2Wrapper(opts, wrapper); err != nil {
		return "", err
	}

	opt, kubeFiles, err := e.wrapper2Options(opts, wrapper)
	if err != nil {
		return "", err
	}

	return e.build(getContext(), kubeFiles, opt)
}

func (e *Engine) loadOneImage(imageSrc string, loadOpts *libimage.LoadOptions) error {
	loadedImages, err := e.ImgRuntime().Load(context.Background(), imageSrc, loadOpts)
	if err != nil {
		return err
	}

	klog.Infof("Loaded image: " + strings.Join(loadedImages, "\nLoaded image: "))
	return nil
}

// this function aims to set buildah configuration based on cluster image engine flags.
func (e *Engine) migrateFlags2Wrapper(opts *image.BuildOpts, wrapper *buildFlagsOpts) error {
	flags := e.Flags()
	// imageengine cache related flags
	// cache intermediate layers during build, it is enabled when len(opts.Platforms) <= 1 and "no-cache" is false
	wrapper.Layers = len(opts.Platforms) <= 1 && !opts.NoCache
	wrapper.NoCache = opts.NoCache
	// tags. Like -t kubernetes:v1.16
	wrapper.Tag = []string{opts.Tag}
	// Hardcoded for network configuration.
	// check parse.NamespaceOptions for detailed logic.
	// this network setup for stage container, especially for RUN wget and so on.
	// so I think we can set as host network.
	err := flags.Set("network", "host")
	if err != nil {
		return err
	}

	// use tmp dockerfile as build file
	wrapper.File = []string{opts.DockerFilePath}
	wrapper.Pull = opts.PullPolicy
	wrapper.Label = append(wrapper.Label, opts.Labels...)
	wrapper.Annotation = append(wrapper.Annotation, opts.Annotations...)
	return nil
}

func (e *Engine) wrapper2Options(opts *image.BuildOpts, wrapper *buildFlagsOpts) (define.BuildOptions, []string, error) {
	output := ""
	tags := wrapper.Tag
	if len(tags) > 0 {
		output = tags[0]
		tags = tags[1:]
	}
	if e.Flag("manifest").Changed {
		for _, tag := range tags {
			if tag == wrapper.Manifest {
				return define.BuildOptions{}, []string{}, errors.New("the same name must not be specified for both '--tag' and '--manifest'")
			}
		}
	}

	args, err := parseArgs(opts.BuildArgs)
	if err != nil {
		return define.BuildOptions{}, nil, err
	}

	systemCxt := e.SystemContext()

	if err = auth.CheckAuthFile(systemCxt.AuthFilePath); err != nil {
		return define.BuildOptions{}, []string{}, err
	}
	tempAuthFile, cleanTmpFile :=
		pkgutil.MirrorToTempFileIfPathIsDescriptor(systemCxt.AuthFilePath)
	if cleanTmpFile {
		defer func() {
			_ = os.Remove(tempAuthFile)
		}()
	}

	// Allow for --pull, --pull=true, --pull=false, --pull=never, --pull=always
	// --pull-always and --pull-never.  The --pull-never and --pull-always options
	// will not be documented.
	pullPolicy := define.PullIfMissing
	if strings.EqualFold(strings.TrimSpace(wrapper.Pull), "true") {
		pullPolicy = define.PullIfNewer
	}
	if wrapper.PullAlways || strings.EqualFold(strings.TrimSpace(wrapper.Pull), "always") {
		pullPolicy = define.PullAlways
	}
	if wrapper.PullNever || strings.EqualFold(strings.TrimSpace(wrapper.Pull), "never") {
		pullPolicy = define.PullNever
	}
	klog.V(5).Infof("Pull Policy for pull [%v]", pullPolicy)

	format, err := getImageType(wrapper.Format)
	if err != nil {
		return define.BuildOptions{}, []string{}, err
	}

	layers := wrapper.Layers

	contextDir := opts.ContextDir

	// Nothing provided, we assume the current working directory as build
	// context
	if len(contextDir) == 0 {
		contextDir, err = os.Getwd()
		if err != nil {
			return define.BuildOptions{}, []string{}, errors.Wrapf(err, "unable to choose current working directory as build context")
		}
	} else {
		// It was local.  Use it as is.
		contextDir, err = filepath.Abs(contextDir)
		if err != nil {
			return define.BuildOptions{}, []string{}, errors.Wrapf(err, "error determining path to directory")
		}
	}

	kubefiles := getKubeFiles(wrapper.File)
	if len(kubefiles) == 0 {
		kubefile, err := FindKubefile(contextDir)
		if err != nil {
			return define.BuildOptions{}, []string{}, err
		}
		kubefiles = append(kubefiles, kubefile)
	}

	contextDir, err = filepath.EvalSymlinks(contextDir)
	if err != nil {
		return define.BuildOptions{}, []string{}, errors.Wrapf(err, "error evaluating symlinks in build context path")
	}

	var stdin io.Reader
	if wrapper.Stdin {
		stdin = os.Stdin
	}
	var stdout, stderr, reporter = os.Stdout, os.Stderr, os.Stderr
	if e.Flag("logfile").Changed {
		f, err := os.OpenFile(wrapper.Logfile, os.O_CREATE|os.O_TRUNC|os.O_WRONLY, 0600)
		if err != nil {
			return define.BuildOptions{}, []string{}, errors.Errorf("error opening logfile %q: %v", wrapper.Logfile, err)
		}
		defer func() {
			// this will incur GoSec warning
			_ = f.Close()
		}()
		klog.SetOutput(f)
		stdout = f
		stderr = f
		reporter = f
	}

	isolation, err := defaultIsolationOption()
	if err != nil {
		return define.BuildOptions{}, []string{}, err
	}

	var runtimeFlags []string
	for _, arg := range wrapper.RuntimeFlags {
		runtimeFlags = append(runtimeFlags, "--"+arg)
	}

	commonOpts, err := parse.CommonBuildOptions(e.Command)
	if err != nil {
		return define.BuildOptions{}, []string{}, err
	}

	namespaceOptions, networkPolicy := defaultNamespaceOptions()

	usernsOption, idmappingOptions, err := parse.IDMappingOptions(e.Command, isolation)
	if err != nil {
		return define.BuildOptions{}, []string{}, errors.Wrapf(err, "error parsing ID mapping options")
	}
	namespaceOptions.AddOrReplace(usernsOption...)

	platforms, err := parsePlatformsFromOptions(opts.Platforms)
	if err != nil {
		return define.BuildOptions{}, nil, err
	}

	var excludes []string
	if wrapper.IgnoreFile != "" {
		if excludes, _, err = parse.ContainerIgnoreFile(contextDir, wrapper.IgnoreFile); err != nil {
			return define.BuildOptions{}, []string{}, err
		}
	}

	var timestamp *time.Time
	if e.Command.Flag("timestamp").Changed {
		t := time.Unix(wrapper.Timestamp, 0).UTC()
		timestamp = &t
	}

	compression := define.Gzip
	if wrapper.DisableCompression {
		compression = define.Uncompressed
	}

	options := define.BuildOptions{
		AddCapabilities:         wrapper.CapAdd,
		AdditionalTags:          tags,
		AllPlatforms:            wrapper.AllPlatforms,
		Annotations:             wrapper.Annotation,
		Architecture:            systemCxt.ArchitectureChoice,
		Args:                    args,
		BlobDirectory:           wrapper.BlobCache,
		CNIConfigDir:            wrapper.CNIConfigDir,
		CNIPluginPath:           wrapper.CNIPlugInPath,
		CommonBuildOpts:         commonOpts,
		Compression:             compression,
		ConfigureNetwork:        networkPolicy,
		ContextDirectory:        contextDir,
		DefaultMountsFilePath:   "",
		Devices:                 wrapper.Devices,
		DropCapabilities:        wrapper.CapDrop,
		Err:                     stderr,
		ForceRmIntermediateCtrs: wrapper.ForceRm,
		From:                    wrapper.From,
		IDMappingOptions:        idmappingOptions,
		IIDFile:                 wrapper.Iidfile,
		In:                      stdin,
		Isolation:               isolation,
		IgnoreFile:              wrapper.IgnoreFile,
		Labels:                  wrapper.Label,
		Layers:                  layers,
		LogRusage:               wrapper.LogRusage,
		Manifest:                wrapper.Manifest,
		MaxPullPushRetries:      maxPullPushRetries,
		NamespaceOptions:        namespaceOptions,
		NoCache:                 wrapper.NoCache,
		OS:                      systemCxt.OSChoice,
		Out:                     stdout,
		Output:                  output,
		OutputFormat:            format,
		PullPolicy:              pullPolicy,
		PullPushRetryDelay:      pullPushRetryDelay,
		Quiet:                   wrapper.Quiet,
		RemoveIntermediateCtrs:  wrapper.Rm,
		ReportWriter:            reporter,
		Runtime:                 wrapper.Runtime,
		RuntimeArgs:             runtimeFlags,
		RusageLogFile:           wrapper.RusageLogFile,
		SignBy:                  wrapper.SignBy,
		SignaturePolicyPath:     wrapper.SignaturePolicy,
		Squash:                  wrapper.Squash,
		SystemContext:           systemCxt,
		Target:                  wrapper.Target,
		TransientMounts:         wrapper.Volumes,
		Jobs:                    &wrapper.Jobs,
		Excludes:                excludes,
		Timestamp:               timestamp,
		Platforms:               platforms,
		UnsetEnvs:               wrapper.UnsetEnvs,
	}

	if wrapper.Quiet {
		options.ReportWriter = io.Discard
	}

	return options, kubefiles, nil
}

func (e *Engine) build(cxt context.Context, kubefiles []string, options define.BuildOptions) (id string, err error) {
	id, ref, err := imagebuildah.BuildDockerfiles(cxt, e.ImgStore(), options, kubefiles...)
	if err == nil && options.Manifest != "" {
		klog.Warningf("manifest list id = %q, ref = %q", id, ref.String())
	}
	if err != nil {
		return "", fmt.Errorf("failed to build image %v: %v", options.AdditionalTags, err)
	}

	return id, nil
}

func parseArgs(buildArgs []string) (map[string]string, error) {
	res := map[string]string{}
	for _, arg := range buildArgs {
		kvs := strings.Split(arg, "=")
		if len(kvs) < 2 {
			return nil, errors.New("build args should be key=value")
		}

		res[kvs[0]] = kvs[1]
	}

	return res, nil
}

func getKubeFiles(files []string) []string {
	var kubefiles []string
	for _, f := range files {
		if f == "-" {
			kubefiles = append(kubefiles, "/dev/stdin")
		} else {
			kubefiles = append(kubefiles, f)
		}
	}
	return kubefiles
}

func parsePlatformsFromOptions(platformSpecs []string) (platforms []struct{ OS, Arch, Variant string }, err error) {
	var _os, arch, variant string

	for _, pf := range platformSpecs {
		if _os, arch, variant, err = parse.Platform(pf); err != nil {
			return nil, fmt.Errorf("unable to parse platform %q: %w", pf, err)
		}
		platforms = append(platforms, struct{ OS, Arch, Variant string }{_os, arch, variant})
	}

	return platforms, nil
}
