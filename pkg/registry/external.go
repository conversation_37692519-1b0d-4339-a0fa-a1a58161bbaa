package registry

import (
	"context"
	"fmt"
	"net"
	"os"
	"strconv"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	containerruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/container-runtime"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/shellcommand"
	"golang.org/x/sync/errgroup"
	"k8s.io/klog/v2"
	k8snet "k8s.io/utils/net"

	"github.com/containers/common/pkg/auth"
)

type externalConfigurator struct {
	containerRuntimeOpts containerruntime.RuntimeOpts
	infraDriver          infradriver.InfraDriver
	endpoint             string
	username             string
	password             string
}

func (e *externalConfigurator) GetRegistryInfo() Options {
	clusterRegistry := e.infraDriver.GetClusterRegistry()
	return Options{
		External: clusterRegistry.ExternalRegistry,
	}
}

func (e *externalConfigurator) InstallOn(masters, nodes []net.IP) error {
	hosts := append(masters, nodes...)
	var (
		username        = e.username
		password        = e.password
		endpoint        = e.endpoint
		tmpAuthFilePath = "/tmp/config.json"
		// todo we need this config file when kubelet pull images from registry. while, we could optimize the logic here.
		remoteKubeletAuthFilePath = "/var/lib/kubelet/config.json"
	)

	klog.V(5).Infof("start set alias [%s] for all nodes", e.endpoint)
	// TODO: 需要提前设置hosts解析规则，否则后面用到的镜像将拉取失败
	if err := e.setHostAlias(append(masters, nodes...)); err != nil {
		return fmt.Errorf("failed to set hostalias: %v", err)
	}

	if username == "" || password == "" {
		return nil
	}

	err := auth.Login(context.TODO(),
		nil,
		&auth.LoginOptions{
			AuthFile:           tmpAuthFilePath,
			Password:           password,
			Username:           username,
			Stdout:             os.Stdout,
			AcceptRepositories: true,
		},
		[]string{endpoint})
	if err != nil {
		return err
	}

	defer func() {
		if err = os.Remove(tmpAuthFilePath); err != nil {
			klog.V(5).Infof("failed to remove tmp registry auth file:%s", tmpAuthFilePath)
		}
	}()

	if err = e.copy2RemoteHosts(tmpAuthFilePath, e.containerRuntimeOpts.ConfigFilePath, hosts); err != nil {
		return err
	}

	return e.copy2RemoteHosts(tmpAuthFilePath, remoteKubeletAuthFilePath, hosts)
}

func (e *externalConfigurator) SetHostAlias(nodes []net.IP) error {
	return e.setHostAlias(nodes)
}

// SetHostAlias 当集群使用外部仓库的时候，为每个节点设置外部仓库的域名解析规则
func (e *externalConfigurator) setHostAlias(nodes []net.IP) error {
	eg, _ := errgroup.WithContext(context.Background())

	for i := range nodes {
		master := nodes[i]
		eg.Go(func() error {
			cmd := shellcommand.CommandSetHostAlias(ExternalRegistryDomain(e.infraDriver), ExternalRegistryVIP(e.infraDriver))
			if err := e.infraDriver.CmdAsync(master, nil, cmd); err != nil {
				return fmt.Errorf("failed to config nodes hosts file: %v", err)
			}
			return nil
		})
	}
	return eg.Wait()
}

func (e *externalConfigurator) copy2RemoteHosts(src, dest string, hosts []net.IP) error {
	f := func(host net.IP) error {
		err := e.infraDriver.Copy(host, src, dest)
		if err != nil {
			return fmt.Errorf("failed to copy local file %s to remote %s : %v", src, dest, err)
		}
		return nil
	}

	return e.infraDriver.Execute(hosts, f)
}

func (e *externalConfigurator) UninstallFrom(masters, nodes []net.IP) error {
	if e.username == "" || e.password == "" {
		return nil
	}
	hosts := append(masters, nodes...)
	// TODO: use sdk to logout instead of shell cmd
	logoutCmd := fmt.Sprintf("docker logout %s ", e.endpoint)
	// nolint
	if e.containerRuntimeOpts.Type != constants.Docker {
		logoutCmd = fmt.Sprintf("nerdctl logout %s ", e.endpoint)
	}

	for _, host := range hosts {
		err := e.infraDriver.CmdAsync(host, nil, logoutCmd)
		if err != nil {
			return fmt.Errorf("failed to delete registry configuration: %v", err)
		}
	}

	return nil
}

func (e *externalConfigurator) GetDriver() (Driver, error) {
	return newExternalRegistryDriver(e.endpoint), nil
}

func NewExternalConfigurator(regConfig *ctv1beta1.ExternalRegistry, containerRuntimeInfo containerruntime.RuntimeOpts, driver infradriver.InfraDriver) (Configurator, error) {
	domain := regConfig.Domain
	if regConfig.Port != 0 {
		domain = net.JoinHostPort(regConfig.Domain, strconv.Itoa(regConfig.Port))
	}
	return &externalConfigurator{
		endpoint:             domain,
		username:             regConfig.Username,
		password:             regConfig.Password,
		infraDriver:          driver,
		containerRuntimeOpts: containerRuntimeInfo,
	}, nil
}

// ExternalRegistryVIP 获取external regisgtry的vip地址
func ExternalRegistryVIP(infraDriver infradriver.InfraDriver) string {
	vip := constants.DefaultVIP
	if hosts := infraDriver.GetHostIPList(); len(hosts) > 0 && k8snet.IsIPv6(hosts[0]) {
		vip = constants.DefaultVIPForIPv6
	}
	if ipv4, ok := infraDriver.GetClusterEnv()[constants.EnvExternalRegistryVIP]; ok {
		klog.V(5).Infof("It will used external registry vip: %s", ipv4)
		vip = ipv4
	} else {
		klog.Warningf("An external remote repository was used, but no vip address for the external registry was provided")
	}

	return vip
}

// ExternalRegistryDomain return external registry domain
func ExternalRegistryDomain(driver infradriver.InfraDriver) string {
	domain := constants.DefaultRegistryDomain
	if addr, ok := driver.GetClusterEnv()[constants.EnvExternalRegistryDomain]; ok {
		domain = addr
	}

	return domain
}
