package registry

import (
	"fmt"

	"golang.org/x/crypto/bcrypt"
)

func GenerateHTTPBasicAuth(username, password string) (string, error) {
	if username == "" || password == "" {
		return "", fmt.Errorf("failed to generate HTTP basic authentication: registry username or password is empty")
	}
	pwdHash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("failed to generate registry password: %v", err)
	}
	return username + ":" + string(pwdHash), nil
}
