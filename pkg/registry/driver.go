package registry

import (
	"net"

	imagedistributor "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-distributor"
)

type Info struct {
	URL string
}

// Driver provide external interaction to work with registry.
type Driver interface {
	// UploadContainerImages2Registry 上传容器镜像文件到registry.
	UploadContainerImages2Registry() error
	// GetInfo return registry endpoint
	GetInfo() Info
}

type localRegistryDriver struct {
	dataDir     string
	endpoint    string
	distributor imagedistributor.Distributor
	deployHosts []net.IP
}

func (l localRegistryDriver) UploadContainerImages2Registry() error {
	return l.distributor.DistributeRegistry(l.deployHosts, l.dataDir)
}

func (l localRegistryDriver) GetInfo() Info {
	return Info{URL: l.endpoint}
}

func newLocalRegistryDriver(endpoint string, dataDir string, deployHosts []net.IP, distributor imagedistributor.Distributor) Driver {
	return localRegistryDriver{
		endpoint:    endpoint,
		distributor: distributor,
		dataDir:     dataDir,
		deployHosts: deployHosts,
	}
}

type externalRegistryDriver struct {
	endpoint string
}

func (l externalRegistryDriver) UploadContainerImages2Registry() error {
	// not implement currently
	return nil
}

func (l externalRegistryDriver) GetInfo() Info {
	return Info{URL: l.endpoint}
}

func newExternalRegistryDriver(endpoint string) Driver {
	return externalRegistryDriver{
		endpoint: endpoint,
	}
}
