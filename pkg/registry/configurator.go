package registry

import (
	"fmt"
	"net"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	containerruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/container-runtime"
	imagedistributor "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-distributor"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
)

type LocalRegistryInfo struct {
	*ctv1beta1.LocalRegistry
	DeployHosts []net.IP `json:"deployHosts,omitempty"`
	Vip         string   `json:"vip,omitempty"`
}

type Options struct { // nolint
	External *ctv1beta1.ExternalRegistry `json:"external,omitempty"`
	Local    LocalRegistryInfo           `json:"local,omitempty"`
}

// Configurator provide registry configuration management
type Configurator interface {
	// InstallOn will install registry configuration on each given hosts.
	InstallOn(masters, nodes []net.IP) error
	// UninstallFrom will uninstall registry configuration on each given hosts.
	UninstallFrom(masters, nodes []net.IP) error
	// GetDriver return driver
	GetDriver() (Driver, error)
	// GetRegistryInfo return registry info
	GetRegistryInfo() Options
	// SetHostAlias set host alias
	SetHostAlias(nodes []net.IP) error
}

// NewConfigurator 初始化registry
func NewConfigurator(deployHosts []net.IP, opts containerruntime.RuntimeOpts, regConfig ctv1beta1.Registry,
	infraDriver infradriver.InfraDriver, distributor imagedistributor.Distributor) (Configurator, error) {
	if regConfig.LocalRegistry != nil {
		// 本地registry配置
		return &localConfigurator{
			deployHosts:          deployHosts,
			infraDriver:          infraDriver,
			LocalRegistry:        regConfig.LocalRegistry,
			containerRuntimeOpts: opts,
			distributor:          distributor,
		}, nil
	}

	if regConfig.ExternalRegistry != nil {
		// 远程registry配置
		// 场景：多集群中的某些业务集群需要与管理集群共用registry的时候
		return NewExternalConfigurator(
			regConfig.ExternalRegistry,
			opts,
			infraDriver,
		)
	}

	return nil, fmt.Errorf("unsupported registry type")
}
