package sync

import (
	"context"
	"errors"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/containers/image/v5/copy"
	"github.com/containers/image/v5/docker"
	"github.com/containers/image/v5/docker/daemon"
	"github.com/containers/image/v5/signature"
	"github.com/containers/image/v5/transports/alltransports"
	"github.com/containers/image/v5/types"
	"github.com/docker/distribution/reference"
	dtype "github.com/docker/docker/api/types"
	"github.com/google/go-containerregistry/pkg/name"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
)

const (
	defaultPort = "5000"
)

// Options 包含了在注册表之间同步镜像的配置选项
type Options struct {
	// Source 源注册表的地址
	Source string

	// Target 目标注册表的地址
	Target string

	// SelectionOptions 指定要复制的镜像（例如，所有镜像，或仅标记的镜像）
	SelectionOptions []copy.ImageListSelection

	// OmitError 决定在发生错误时是否继续同步
	// 如果为 true，错误将被记录但不会导致同步停止
	OmitError bool

	// SystemContext 为镜像操作提供额外的上下文
	// 可以包括身份验证详情、TLS 设置等
	SystemContext *types.SystemContext
}

// Sync 将源registry镜像同步到目标registry
func Sync(ctx context.Context, opts *Options) error {
	src := opts.Source
	dst := opts.Target
	sys := opts.SystemContext

	searchRes, err := docker.SearchRegistry(ctx, sys, src, "", 1<<10)
	if err != nil {
		return fmt.Errorf("failed to search registry: %w", err)
	}
	if len(searchRes) == 0 {
		return nil
	}

	klog.Infof("syncing repos %v from %s to %s", searchRes, src, dst)
	for _, searchResult := range searchRes {
		if err := syncRepository(ctx, sys, src, dst, searchResult.Name, opts.OmitError, opts.SelectionOptions); err != nil {
			if !opts.OmitError {
				return err
			}
			klog.Errorf("Failed to sync repository %s: %v", searchResult.Name, err)
		}
	}
	return nil
}

// syncRepository 同步单个仓库的镜像
func syncRepository(ctx context.Context, sys *types.SystemContext, src, dst, repoName string, omitError bool, selectionOptions []copy.ImageListSelection) error {
	referenceName, err := parseRepositoryReference(fmt.Sprintf("%s/%s", src, repoName))
	if err != nil {
		return fmt.Errorf("failed to parse repository reference: %w", err)
	}

	imgRefs, err := imagesToCopyFromRepo(ctx, sys, referenceName)
	if err != nil {
		return fmt.Errorf("failed to get images to copy: %w", err)
	}

	for _, imgRef := range imgRefs {
		if err := syncImage(ctx, sys, src, dst, imgRef, omitError, selectionOptions); err != nil {
			return err
		}
	}
	return nil
}

// syncImage 同步单个镜像
func syncImage(ctx context.Context, sys *types.SystemContext, src, dst string, imgRef types.ImageReference, omitError bool, selectionOptions []copy.ImageListSelection) error {
	destSuffix := strings.TrimPrefix(imgRef.DockerReference().String(), src)
	destRef, err := docker.ParseReference(fmt.Sprintf("//%s", filepath.Join(dst, destSuffix)))
	if err != nil {
		return fmt.Errorf("failed to parse destination reference: %w", err)
	}

	for _, selection := range selectionOptions {
		klog.Infof("syncing %s with selection %v", destRef.DockerReference().String(), selection)
		if err := copyImageWithRetry(ctx, sys, destRef, imgRef, selection); err != nil {
			if strings.Contains(err.Error(), "manifest unknown") && selection == copy.CopyAllImages {
				return nil
			}
			if !omitError {
				return err
			}
			klog.V(5).Infof("Failed to copy image %s: %v", imgRef, err)
		}
	}
	return nil
}

// copyImageWithRetry 尝试复制镜像，并在失败时进行重试
//
// 该函数使用指数退避策略来重试镜像复制操作。它会创建一个策略上下文，
// 然后反复尝试复制镜像，直到成功或达到最大重试次数。
func copyImageWithRetry(ctx context.Context, sys *types.SystemContext, destRef, srcRef types.ImageReference, selection copy.ImageListSelection) error {
	policyContext, err := getPolicyContext()
	if err != nil {
		return fmt.Errorf("failed to get policy context: %w", err)
	}
	defer policyContext.Destroy()

	waitErr := wait.ExponentialBackoff(getDefaultRetryOptions(), func() (bool, error) {
		_, err := copy.Image(ctx, policyContext, destRef, srcRef, &copy.Options{
			SourceCtx:          sys,
			DestinationCtx:     sys,
			ImageListSelection: selection,
			ReportWriter:       os.Stdout,
		})
		if err != nil {
			klog.V(5).Infof("Copy attempt for image %s to %s failed with error: %v. Retrying...", srcRef, destRef, err)
			return false, nil
		}
		return true, nil
	})

	if waitErr != nil {
		return fmt.Errorf("failed to copy image %s to %s after multiple attempts: %w", srcRef, destRef, waitErr)
	}

	return nil

}

// ImageToRegistry 将外部镜像同步到一个指定的registry服务
func ImageToRegistry(ctx context.Context, sys *types.SystemContext, src types.ImageReference, dst string, selection copy.ImageListSelection) error {
	allSrcImage := src.DockerReference().String()
	repo := extractRepoName(allSrcImage)

	klog.Infof("syncing image from %s to %s", allSrcImage, dst)
	dstImage := strings.Join([]string{dst, repo}, "/")
	destRef, err := alltransports.ParseImageName(fmt.Sprintf("docker://%s", dstImage))
	if err != nil {
		return fmt.Errorf("invalid destination name %s: %w", dst, err)
	}

	klog.Infof("Starting image copy operation from %s to %s", src, destRef)
	if err := copyImageWithRetry(ctx, sys, destRef, src, selection); err != nil {
		return fmt.Errorf("failed to copy image from %s to %s: %w", src, destRef, err)
	}

	klog.Infof("Image copy operation from %s to %s completed successfully.", src, destRef)
	return nil
}

// extractRepoName 从完整的镜像名称中提取仓库名称。
// 如果输入的镜像名称包含域名或端口，函数将返回域名之后的部分。
// 如果输入不包含域名或端口，函数将返回空字符串。
//
// 示例:
//
//	输入: "registry.example.com:5000/myproject/myimage" -> 返回: "myproject/myimage"
//	输入: "docker.io/library/ubuntu" -> 返回: "library/ubuntu"
//	输入: "myimage" -> 返回: ""
func extractRepoName(allSrcImage string) string {
	// 使用 "/" 分割输入字符串，最多分割成两部分
	parts := strings.SplitN(allSrcImage, "/", 2)

	// 如果分割后有两个部分，并且第一部分包含 "." 或 ":" （表示可能是域名或带端口的地址）
	if len(parts) == 2 && (strings.ContainsRune(parts[0], '.') || strings.ContainsRune(parts[0], ':')) {
		// 返回第二部分，即域名或地址后面的部分
		return parts[1]
	}

	// 如果不满足上述条件，返回空字符串
	return ""
}

// ConvertImageToReference 将镜像名称转换为 ImageReference
func ConvertImageToReference(sys *types.SystemContext, img string, auth map[string]dtype.AuthConfig) (types.ImageReference, error) {
	transport := alltransports.TransportFromImageName(img)
	if transport != nil && transport.Name() == "containers-storage" {
		klog.Infof("Using containers-storage as image transport")
		srcRef, err := alltransports.ParseImageName(img)
		if err != nil {
			return nil, fmt.Errorf("invalid source name %s: %v", img, err)
		}
		return srcRef, nil
	}
	if transport != nil && transport.Name() == "docker-daemon" {
		klog.Infof("Using docker-daemon as image transport")
		srcRef, err := alltransports.ParseImageName(img)
		if err != nil {
			return nil, fmt.Errorf("invalid source name %s: %v", img, err)
		}
		return srcRef, nil
	}

	src, err := name.ParseReference(img)
	if err != nil {
		return nil, fmt.Errorf("ref invalid source name %s: %v", img, err)
	}
	reg := src.Context().RegistryStr()
	info, ok := auth[reg]
	if sys != nil && ok {
		sys.DockerAuthConfig = &types.DockerAuthConfig{
			Username:      info.Username,
			Password:      info.Password,
			IdentityToken: info.IdentityToken,
		}
	}
	image := src.Name()
	srcRef, err := alltransports.ParseImageName(fmt.Sprintf("docker://%s", image))
	if err != nil {
		return nil, fmt.Errorf("invalid source name %s: %v", src, err)
	}
	return srcRef, nil
}

// getDefaultRetryOptions 默认的重试策略配置
func getDefaultRetryOptions() wait.Backoff {
	return wait.Backoff{
		Duration: 1 * time.Second,  // 初始等待时间为1秒
		Factor:   1.5,              // 每次重试后，等待时间增加50%
		Jitter:   0.1,              // 添加10%的随机抖动
		Steps:    5,                // 最多重试5次
		Cap:      10 * time.Second, // 单次等待时间最长为10秒
	}
}

// getPolicyContext 创建并返回一个新的策略上下文，该上下文用于签名和验证过程中应用安全策略
func getPolicyContext() (*signature.PolicyContext, error) {
	policy := &signature.Policy{
		Default: []signature.PolicyRequirement{
			signature.NewPRInsecureAcceptAnything(),
		},
		Transports: map[string]signature.PolicyTransportScopes{
			daemon.Transport.Name(): {
				"": signature.PolicyRequirements{signature.NewPRInsecureAcceptAnything()},
			},
		},
	}
	return signature.NewPolicyContext(policy)
}

// parseRepositoryReference 解析输入字符串为一个 reference.Named 对象，
// 并验证它是否只命名了一个仓库，而不是一个具体的镜像。
// 示例: docker.io/library/ubuntu
func parseRepositoryReference(input string) (reference.Named, error) {
	ref, err := reference.ParseNormalizedNamed(input)
	if err != nil {
		return nil, err
	}
	if !reference.IsNameOnly(ref) {
		return nil, errors.New("input names a reference, not a repository")
	}
	return ref, nil
}

// imagesToCopyFromRepo 从源仓库的标签构建一个镜像引用列表
func imagesToCopyFromRepo(ctx context.Context, sys *types.SystemContext, repoRef reference.Named) ([]types.ImageReference, error) {
	tags, err := getImageTags(ctx, sys, repoRef)
	if err != nil {
		return nil, err
	}

	var sourceReferences []types.ImageReference
	for _, tag := range tags {
		taggedRef, err := reference.WithTag(repoRef, tag)
		if err != nil {
			klog.Errorf("Error creating a tagged reference from registry tag %s:%s list: %v", repoRef.Name(), tag, err)
			continue
		}
		ref, err := docker.NewReference(taggedRef)
		if err != nil {
			return nil, fmt.Errorf("cannot obtain a valid image reference for transport %q and reference %s: %w", docker.Transport.Name(), taggedRef.String(), err)
		}
		sourceReferences = append(sourceReferences, ref)
	}
	return sourceReferences, nil
}

// getImageTags 列出仓库中的所有标签
func getImageTags(ctx context.Context, sysCtx *types.SystemContext, repoRef reference.Named) ([]string, error) {
	name := repoRef.Name()
	dockerRef, err := docker.NewReference(reference.TagNameOnly(repoRef))
	if err != nil {
		return nil, err
	}
	tags, err := docker.GetRepositoryTags(ctx, sysCtx, dockerRef)
	if err != nil {
		return nil, fmt.Errorf("error determining repository tag for %s: %v", name, err)
	}
	return tags, nil
}

// ParseRegistryAddress 解析给定的地址字符串，并根据需要附加端口号
func ParseRegistryAddress(s string, args ...string) string {
	if strings.Contains(s, ":") {
		return s
	}

	var portStr = defaultPort
	if len(args) > 0 {
		portStr = args[0]
	}

	if idx := strings.Index(portStr, ":"); idx >= 0 {
		portStr = portStr[idx+1:]
	}
	return net.JoinHostPort(s, portStr)
}
