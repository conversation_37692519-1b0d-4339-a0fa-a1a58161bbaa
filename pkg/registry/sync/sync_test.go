package sync

import (
	"context"
	"errors"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/containers/image/v5/copy"
	"github.com/containers/image/v5/signature"
	"github.com/containers/image/v5/types"
	"github.com/docker/distribution/reference"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestParseRegistryAddress(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		args     []string
		expected string
	}{
		{
			name:     "No port specified",
			input:    "example.com",
			args:     []string{},
			expected: "example.com:5000",
		},
		{
			name:     "Port already specified",
			input:    "example.com:8080",
			args:     []string{},
			expected: "example.com:8080",
		},
		{
			name:     "Custom port specified",
			input:    "example.com",
			args:     []string{"8080"},
			expected: "example.com:8080",
		},
		{
			name:     "Localhost without port",
			input:    "localhost",
			args:     []string{},
			expected: "localhost:5000",
		},
		{
			name:     "Custom port with colon prefix",
			input:    "registry.example.com",
			args:     []string{":9000"},
			expected: "registry.example.com:9000",
		},
		{
			name:     "Empty input",
			input:    "",
			args:     []string{},
			expected: ":5000",
		},
		{
			name:     "Multiple custom ports (should use first)",
			input:    "multi.example.com",
			args:     []string{"8080", "9090"},
			expected: "multi.example.com:8080",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ParseRegistryAddress(tt.input, tt.args...)
			assert.Equal(t, tt.expected, result, "ParseRegistryAddress(%q, %v) = %q; want %q", tt.input, tt.args, result, tt.expected)
		})
	}
}

func TestParseRepositoryReference(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expectedRef string
		expectError bool
	}{
		{
			name:        "Valid repository name",
			input:       "docker.io/library/ubuntu",
			expectedRef: "docker.io/library/ubuntu",
			expectError: false,
		},
		{
			name:        "Valid repository with port",
			input:       "localhost:5000/myrepo",
			expectedRef: "localhost:5000/myrepo",
			expectError: false,
		},
		{
			name:        "Valid repository with subdomain",
			input:       "registry.example.com/myproject/myrepo",
			expectedRef: "registry.example.com/myproject/myrepo",
			expectError: false,
		},
		{
			name:        "Invalid: includes tag",
			input:       "docker.io/library/ubuntu:latest",
			expectedRef: "",
			expectError: true,
		},
		{
			name:        "Invalid: includes digest",
			input:       "docker.io/library/ubuntu@sha256:1234567890abcdef",
			expectedRef: "",
			expectError: true,
		},
		{
			name:        "Invalid: malformed name",
			input:       "invalid/repo/name/",
			expectedRef: "",
			expectError: true,
		},
		{
			name:        "Invalid: empty string",
			input:       "",
			expectedRef: "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ref, err := parseRepositoryReference(tt.input)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, ref)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, ref)
				assert.Equal(t, tt.expectedRef, ref.String())
				assert.True(t, reference.IsNameOnly(ref))
			}
		})
	}
}

func TestExtractRepoName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Full image name with domain and port",
			input:    "registry.example.com:5000/myproject/myimage",
			expected: "myproject/myimage",
		},
		{
			name:     "Image name with domain",
			input:    "docker.io/library/ubuntu",
			expected: "library/ubuntu",
		},
		{
			name:     "Image name with subdomain",
			input:    "gcr.io/myproject/myimage",
			expected: "myproject/myimage",
		},
		{
			name:     "Local image name without domain",
			input:    "myimage",
			expected: "",
		},
		{
			name:     "Image name with localhost",
			input:    "localhost:5000/myimage",
			expected: "myimage",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "Image name with IP address",
			input:    "***********:5000/myimage",
			expected: "myimage",
		},
		{
			name:     "Image name with multiple slashes",
			input:    "registry.example.com/project/subproject/image",
			expected: "project/subproject/image",
		},
		{
			name:     "Image name with tag",
			input:    "registry.example.com/myimage:latest",
			expected: "myimage:latest",
		},
		{
			name:     "Image name with digest",
			input:    "registry.example.com/myimage@sha256:1234567890abcdef",
			expected: "myimage@sha256:1234567890abcdef",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractRepoName(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// MockImageCopier 是一个模拟的镜像复制器
type MockImageCopier struct {
	mock.Mock
}

func (m *MockImageCopier) Copy(ctx context.Context, policyContext *signature.PolicyContext, destRef, srcRef types.ImageReference, options *copy.Options) (copiedManifest []byte, retErr error) {
	args := m.Called(ctx, policyContext, destRef, srcRef, options)
	return args.Get(0).([]byte), args.Error(1)
}

func TestCopyImageWithRetry(t *testing.T) {
	// 创建一个模拟的复制器
	mockCopier := new(MockImageCopier)

	// 保存原始函数并在测试后恢复
	// 使用 gomonkey 来 patch copy.Image 函数
	patches := gomonkey.ApplyFunc(copy.Image, func(ctx context.Context, policyContext *signature.PolicyContext, destRef, srcRef types.ImageReference, options *copy.Options) ([]byte, error) {
		return mockCopier.Copy(ctx, policyContext, destRef, srcRef, options)
	})
	defer patches.Reset()

	// 创建测试用的上下文和引用
	ctx := context.Background()
	sys := &types.SystemContext{}
	destRef := types.ImageReference(nil)
	srcRef := types.ImageReference(nil)
	selection := copy.CopyAllImages

	// 测试用例1：成功复制镜像
	t.Run("SuccessfulCopy", func(t *testing.T) {
		mockCopier.On("Copy", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]byte{}, nil).Once()

		err := copyImageWithRetry(ctx, sys, destRef, srcRef, selection)

		assert.NoError(t, err)
		mockCopier.AssertExpectations(t)
	})

	// 测试用例2：失败后重试成功
	t.Run("RetrySuccess", func(t *testing.T) {
		mockCopier.On("Copy", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return([]byte{}, errors.New("temporary error")).Once()
		mockCopier.On("Copy", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return([]byte{}, nil).Once()

		err := copyImageWithRetry(ctx, sys, destRef, srcRef, selection)

		assert.NoError(t, err)
		mockCopier.AssertNumberOfCalls(t, "Copy", 2)
	})

	// 测试用例3：所有重试都失败
	t.Run("AllRetriesFail", func(t *testing.T) {
		mockCopier.On("Copy", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return([]byte{}, errors.New("failed to copy image")).Times(5)

		err := copyImageWithRetry(ctx, sys, destRef, srcRef, selection)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to copy image")
		mockCopier.AssertNumberOfCalls(t, "Copy", 5)
	})
}
