package save

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/containers/image/v5/copy"
	itype "github.com/containers/image/v5/types"
	"github.com/distribution/distribution/v3/configuration"
	v1 "github.com/opencontainers/image-spec/specs-go/v1"
	"k8s.io/apimachinery/pkg/util/wait"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry/handler"
	registrySync "gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry/sync"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/http"
	"golang.org/x/sync/errgroup"

	"k8s.io/klog/v2"
)

const (
	localhost = "127.0.0.1"
)

// SaveImages 保存镜像，以registry 数据目录结构保存
// TODO 目前只支持一次性所有架构镜像的导出，后续考虑支持指定架构镜像的导出
func (r *registryImage) SaveImages(ctx context.Context, images []string, dir string, platform v1.Platform) ([]string, error) {
	klog.V(5).Infof("Saving images: %+v for platform: %s", images,
		strings.Join([]string{platform.OS, platform.Architecture, platform.Variant}, ","))

	// 初始化配置
	config, err := r.initializeRegistry(dir)
	if err != nil {
		return nil, err
	}

	// 启动一个临时的registry服务
	registryCtx, cancelRegistry := context.WithCancel(ctx)
	defer cancelRegistry()
	go r.runRegistry(registryCtx, config)

	// 等待registry服务启动成功
	ep, err := r.waitForRegistry(ctx, config)
	if err != nil {
		return nil, err
	}

	// 设置系统上下文
	sys := r.setupSystemContext(platform)

	// 并行保存镜像
	return r.saveImagesParallel(ctx, images, sys, ep)
}

// runRegistry 启动registry服务
func (r *registryImage) runRegistry(ctx context.Context, config *configuration.Configuration) {
	wait.UntilWithContext(ctx, func(ctx context.Context) {
		if err := handler.Run(ctx, config); err != nil {
			klog.Errorf("Failed to start registry: %v", err)
		}
	}, time.Second*3)
}

// initializeRegistry 初始化registry配置
func (r *registryImage) initializeRegistry(dir string) (*configuration.Configuration, error) {
	config, err := handler.NewConfig(dir, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to create registry config: %w", err)
	}
	// 禁用访问日志
	config.Log.AccessLog.Disabled = true
	return config, nil
}

// waitForRegistry 等待registry服务启动
func (r *registryImage) waitForRegistry(ctx context.Context, config *configuration.Configuration) (string, error) {
	// 创建一个10秒超时的上下文
	probeCtx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()

	ep := registrySync.ParseRegistryAddress(localhost, config.HTTP.Addr)
	// 等待 registry 服务就绪
	if err := http.WaitUntilEndpointAlive(probeCtx, "http://"+ep); err != nil {
		return "", fmt.Errorf("registry failed to start: %w", err)
	}
	return ep, nil
}

// setupSystemContext 设置系统上下文
func (r *registryImage) setupSystemContext(platform v1.Platform) *itype.SystemContext {
	osChoice := platform.OS
	if osChoice == "" {
		osChoice = "linux"
	}

	return &itype.SystemContext{
		ArchitectureChoice:          platform.Architecture,
		OSChoice:                    osChoice,
		VariantChoice:               platform.Variant,
		DockerInsecureSkipTLSVerify: itype.OptionalBoolTrue,
	}
}

// saveImagesParallel 并行保存多个镜像
func (r *registryImage) saveImagesParallel(ctx context.Context, images []string, sys *itype.SystemContext, ep string) ([]string, error) {
	eg, egCtx := errgroup.WithContext(ctx)
	semaphore := make(chan struct{}, r.maxPullProcs)
	var outImages []string
	mu := &sync.Mutex{}

	for _, img := range images {
		img := img // Create a new variable for each iteration
		eg.Go(func() error {
			select {
			case semaphore <- struct{}{}:
				defer func() { <-semaphore }()
			case <-egCtx.Done():
				return egCtx.Err()
			}
			// 保存单个镜像
			err := r.saveImage(egCtx, sys, img, ep)
			if err != nil {
				return err
			}
			mu.Lock()
			// 将成功保存的镜像添加到列表
			outImages = append(outImages, img)
			mu.Unlock()
			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	return outImages, nil
}

// saveImage 保存单个镜像
func (r *registryImage) saveImage(ctx context.Context, sys *itype.SystemContext, img, ep string) error {
	// 将镜像名称转换为引用
	srcRef, err := registrySync.ConvertImageToReference(sys, img, r.auths)
	if err != nil {
		return fmt.Errorf("failed to convert image to reference: %w", err)
	}

	// 将镜像同步到 registry 服务
	if err := registrySync.ImageToRegistry(ctx, sys, srcRef, ep, copy.CopyAllImages); err != nil {
		return fmt.Errorf("failed to save image %s: %w", img, err)
	}

	return nil
}
