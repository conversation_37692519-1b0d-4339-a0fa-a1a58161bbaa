package save

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/containers/image/v5/copy"

	dockerType "github.com/docker/docker/api/types"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/containers/image/v5/types"
	"github.com/distribution/distribution/v3/configuration"
	v1 "github.com/opencontainers/image-spec/specs-go/v1"
	"github.com/stretchr/testify/assert"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry/handler"
	registrysync "gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry/sync"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/http"
)

func TestSaveImages(t *testing.T) {
	// Setup
	ctx := context.Background()
	images := []string{"image1:latest", "image2:v1"}
	dir := "/tmp/registry"
	platform := v1.Platform{OS: "linux", Architecture: "amd64"}

	r := &registryImage{
		maxPullProcs: 5,
		auths:        make(map[string]dockerType.AuthConfig),
	}

	// Test cases
	testCases := []struct {
		name           string
		setupMocks     func(patches *gomonkey.Patches)
		expectedError  bool
		expectedImages []string
	}{
		{
			// 该测试用例需要正确的go版本，例如1.18，1.19，但是1.22则不行，基本是gomonkey版本的问题
			name: "Successful save",
			setupMocks: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(handler.NewConfig, func(root string, port int) (*configuration.Configuration, error) {
					return &configuration.Configuration{}, nil
				})
				patches.ApplyFunc(http.WaitUntilEndpointAlive, func(ctx context.Context, endpoint string) error {
					return nil
				})
				patches.ApplyPrivateMethod(reflect.TypeOf(r), "saveImage", func(ctx context.Context, sys *types.SystemContext, img, ep string) error {
					return nil
				})
			},
			expectedError:  false,
			expectedImages: images,
		},
		{
			name: "Registry initialization failure",
			setupMocks: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(handler.NewConfig, func(string, int) (*configuration.Configuration, error) {
					return nil, fmt.Errorf("failed to create registry config")
				})
			},
			expectedError:  true,
			expectedImages: nil,
		},
		{
			name: "Registry startup failure",
			setupMocks: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(handler.NewConfig, func(string, int) (*configuration.Configuration, error) {
					return &configuration.Configuration{}, nil
				})
				patches.ApplyFunc(http.WaitUntilEndpointAlive, func(context.Context, string) error {
					return fmt.Errorf("registry failed to start")
				})
			},
			expectedError:  true,
			expectedImages: nil,
		},
		{
			name: "Partial save failure",
			setupMocks: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(handler.NewConfig, func(string, int) (*configuration.Configuration, error) {
					return &configuration.Configuration{}, nil
				})
				patches.ApplyFunc(http.WaitUntilEndpointAlive, func(context.Context, string) error {
					return nil
				})
				patches.ApplyPrivateMethod(reflect.TypeOf(r), "saveImage", func(ctx context.Context, sys *types.SystemContext, img, ep string) error {
					return fmt.Errorf("failed to save image")
				})
			},
			expectedError:  true,
			expectedImages: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mocks
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			tc.setupMocks(patches)

			// Execute
			savedImages, err := r.SaveImages(ctx, images, dir, platform)

			// Assert
			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			// Check saved images
			assert.ElementsMatch(t, tc.expectedImages, savedImages, "images not match")
		})
	}
}

func TestSetupSystemContext(t *testing.T) {
	r := &registryImage{}
	platform := v1.Platform{OS: "linux", Architecture: "amd64", Variant: "v8"}

	sysContext := r.setupSystemContext(platform)

	assert.Equal(t, "linux", sysContext.OSChoice)
	assert.Equal(t, "amd64", sysContext.ArchitectureChoice)
	assert.Equal(t, "v8", sysContext.VariantChoice)
	assert.Equal(t, types.OptionalBoolTrue, sysContext.DockerInsecureSkipTLSVerify)
}

func TestSaveImage(t *testing.T) {
	r := &registryImage{
		auths: make(map[string]dockerType.AuthConfig),
	}
	ctx := context.Background()
	sys := &types.SystemContext{}
	img := "example.com/image:latest"
	ep := "localhost:5000"

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyFunc(registrysync.ConvertImageToReference, func(*types.SystemContext, string, map[string]dockerType.AuthConfig) (types.ImageReference, error) {
		return nil, nil
	})
	patches.ApplyFunc(registrysync.ImageToRegistry, func(context.Context, *types.SystemContext, types.ImageReference, string, copy.ImageListSelection) error {
		return nil
	})

	err := r.saveImage(ctx, sys, img, ep)

	assert.NoError(t, err)
}
