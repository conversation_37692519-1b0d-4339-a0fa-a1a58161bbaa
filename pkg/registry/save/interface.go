package save

import (
	"context"

	"github.com/docker/docker/api/types"

	v1 "github.com/opencontainers/image-spec/specs-go/v1"
)

type Registry interface {
	// SaveImages 保存镜像
	SaveImages(ctx context.Context, images []string, dir string, platform v1.Platform) ([]string, error)
}

type registryImage struct {
	maxPullProcs int                         // 最大并发拉取镜像数量
	auths        map[string]types.AuthConfig // 镜像拉取的认证配置
}

func NewImageSaver(maxPullProcs int, auths map[string]types.AuthConfig) Registry {
	return newRegistrySaver(maxPullProcs, auths)
}

func newRegistrySaver(maxPullProcs int, auths map[string]types.AuthConfig) Registry {
	if auths == nil {
		auths = make(map[string]types.AuthConfig)
	}
	return &registryImage{
		maxPullProcs: maxPullProcs,
		auths:        auths,
	}
}
