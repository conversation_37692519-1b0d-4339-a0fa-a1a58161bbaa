package handler

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net"
	"net/http"

	"github.com/distribution/distribution/v3/configuration"
	dcontext "github.com/distribution/distribution/v3/context"
	"github.com/distribution/distribution/v3/registry/handlers"
	"github.com/distribution/distribution/v3/registry/listener"
	"k8s.io/klog/v2"

	log "github.com/sirupsen/logrus"
)

// tpl registry 服务配置模板
const tpl = `
version: 0.1
storage:
  filesystem:
    rootdirectory: %s
  maintenance:
    uploadpurging:
      enabled: false
http:
  addr: :%d
  secret: asecretforlocaldevelopment
`

// getFreePort 返回一个可用的随机端口
func getFreePort() (port int, err error) {
	var a *net.TCPAddr
	if a, err = net.ResolveTCPAddr("tcp", "localhost:0"); err == nil {
		var l *net.TCPListener
		if l, err = net.ListenTCP("tcp", a); err == nil {
			defer l.Close()
			return l.Addr().(*net.TCPAddr).Port, nil
		}
	}
	return
}

// NewConfig 根据给定的目录和端口生成一个新的 registry 配置对象
func NewConfig(root string, port int) (*configuration.Configuration, error) {
	if port <= 0 {
		var err error
		port, err = getFreePort()
		if err != nil {
			return nil, err
		}
	}

	s := fmt.Sprintf(tpl, root, port)
	rd := bytes.NewReader([]byte(s))
	return configuration.Parse(rd)
}

// configureLogging 根据配置初始化日志记录设置。
// tips：
// 1. 引用了 registry 包，且它的日志包为 logrus(与本项目使用的 klog 是不冲突的)
// 2. 为了避免产生过多的日志，因此这里需要重新设置 logrus 日志记录器
func configureLogging(ctx context.Context, config *configuration.Configuration) context.Context {
	logger := log.New()
	if config.Log.AccessLog.Disabled {
		logger.Out = io.Discard
	}
	var logLevel log.Level
	lvl, err := log.ParseLevel(string(config.Log.Level))
	if err != nil {
		logLevel = log.ErrorLevel
	} else {
		logLevel = lvl
	}
	logger.SetLevel(logLevel)
	logEntry := log.NewEntry(logger)
	ctx = dcontext.WithLogger(ctx, logEntry)
	dcontext.SetDefaultLogger(logEntry)
	return ctx
}

// New 使用给定的配置创建一个 registry 服务器实例
func New(ctx context.Context, config *configuration.Configuration) (*http.Server, error) {
	ctx = configureLogging(ctx, config)
	return &http.Server{
		Handler: handlers.NewApp(ctx, config),
	}, nil
}

// Run 运行 registry 服务
func Run(ctx context.Context, config *configuration.Configuration) error {
	srv, err := New(ctx, config)
	if err != nil {
		return err
	}

	ln, err := listener.NewListener(config.HTTP.Net, config.HTTP.Addr)
	if err != nil {
		return err
	}

	errCh := make(chan error, 1)
	go func() {
		errCh <- srv.Serve(ln)
	}()

	select {
	case e := <-errCh:
		return e
	case <-ctx.Done():
		err = srv.Shutdown(ctx)
		klog.Infof("Graceful shutdown completed with: %v", err)
		return nil
	}
}
