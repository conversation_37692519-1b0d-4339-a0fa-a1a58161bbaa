package handler

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetFreePort(t *testing.T) {
	port, err := getFreePort()
	assert.NoError(t, err)
	assert.Greater(t, port, 0)
	assert.Less(t, port, 65536)

	// Try to bind to the port to ensure it's actually free
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	assert.NoError(t, err)
	defer listener.Close()
}

func TestNewConfig(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "registry-test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	tests := []struct {
		name     string
		root     string
		port     int
		wantErr  bool
		checkErr func(*testing.T, error)
	}{
		{
			name: "Valid config with specified port",
			root: tempDir,
			port: 5000,
		},
		{
			name: "Valid config with random port",
			root: tempDir,
			port: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := NewConfig(tt.root, tt.port)
			if tt.wantErr {
				assert.Error(t, err)
				if tt.checkErr != nil {
					tt.checkErr(t, err)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, config)

				// Parse the port from config.HTTP.Addr
				_, portStr, err := net.SplitHostPort(config.HTTP.Addr)
				assert.NoError(t, err)
				actualPort, err := strconv.Atoi(portStr)
				assert.NoError(t, err)

				if tt.port != 0 {
					assert.Equal(t, tt.port, actualPort)
				} else {
					assert.Greater(t, actualPort, 0)
					assert.Less(t, actualPort, 65536)
				}
			}
		})
	}
}

func TestNew(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "registry-test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	config, err := NewConfig(tempDir, 0)
	require.NoError(t, err)

	ctx := context.Background()
	server, err := New(ctx, config)

	assert.NoError(t, err)
	assert.NotNil(t, server)
	assert.IsType(t, &http.Server{}, server)
}

func TestRun(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "registry-test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	config, err := NewConfig(tempDir, 0)
	require.NoError(t, err)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	errCh := make(chan error, 1)
	go func() {
		errCh <- Run(ctx, config)
	}()

	// Wait for the server to start
	time.Sleep(100 * time.Millisecond)

	// Parse the port from config.HTTP.Addr
	_, portStr, err := net.SplitHostPort(config.HTTP.Addr)
	require.NoError(t, err)
	port, err := strconv.Atoi(portStr)
	require.NoError(t, err)

	baseURL := fmt.Sprintf("http://localhost:%d", port)

	// Check if the server is running and responds to /v2/ endpoint
	t.Run("Server is running", func(t *testing.T) {
		resp, err := http.Get(baseURL + "/v2/")
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)
		resp.Body.Close()
	})

	// Verify graceful shutdown
	t.Run("Graceful shutdown", func(t *testing.T) {
		cancel() // Trigger shutdown

		select {
		case err := <-errCh:
			assert.NoError(t, err)
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for server to shut down")
		}

		// Verify server is no longer responding
		_, err := http.Get(baseURL + "/v2/")
		assert.Error(t, err)
	})
}
