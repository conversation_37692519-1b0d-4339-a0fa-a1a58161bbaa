package registry

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/pelletier/go-toml"
	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	containerruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/container-runtime"
	imagedistributor "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-distributor"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/shellcommand"
	"k8s.io/klog/v2"

	"github.com/containers/common/pkg/auth"
	"golang.org/x/sync/errgroup"
	k8snet "k8s.io/utils/net"
)

type localConfigurator struct {
	*ctv1beta1.LocalRegistry
	deployHosts          []net.IP
	containerRuntimeOpts containerruntime.RuntimeOpts
	infraDriver          infradriver.InfraDriver
	distributor          imagedistributor.Distributor
}

func (c *localConfigurator) GetRegistryInfo() Options {
	opts := Options{Local: LocalRegistryInfo{LocalRegistry: c.LocalRegistry}}
	if *c.LocalRegistry.HA {
		opts.Local.Vip = LocalRegistryVIP(c.infraDriver)
		opts.Local.DeployHosts = c.deployHosts
	} else {
		opts.Local.DeployHosts = append(opts.Local.DeployHosts, c.deployHosts[0])
	}
	return opts
}

func (c *localConfigurator) GetDriver() (Driver, error) {
	endpoint := net.JoinHostPort(c.Domain, strconv.Itoa(c.Port))
	dataDir := c.infraDriver.GetRegistryDataDir()
	return newLocalRegistryDriver(endpoint, dataDir, c.deployHosts, c.distributor), nil
}

func (c *localConfigurator) UninstallFrom(deletedMasters, deletedNodes []net.IP) error {
	// remove all registry configs on /etc/hosts
	all := append(deletedMasters, deletedNodes...)
	if err := c.removeRegistryConfig(all); err != nil {
		return err
	}
	if !*c.HA {
		return nil
	}
	// if current deployHosts is null,means clean all, just return.
	if len(c.deployHosts) == 0 {
		return nil
	}
	// if deletedMasters is nil, means no need to flush workers, just return
	if len(deletedMasters) == 0 {
		return nil
	}
	// flush ipvs policy on remain nodes
	// return c.buildLvsRule(c.deployHosts, unet.RemoveIPs(c.infraDriver.GetHostIPListByRole(consts.NODE), deletedNodes))
	return c.unSetHostAlias(deletedNodes)
}

// removeRegistryConfig 删除registry的配置
func (c *localConfigurator) removeRegistryConfig(hosts []net.IP) error {
	var uninstallCmd []string
	if c.RegistryConfig.Username != "" && c.RegistryConfig.Password != "" {
		// Todo use sdk to logout instead of shell cmd
		logoutCmd := fmt.Sprintf("docker logout %s ", net.JoinHostPort(c.Domain, strconv.Itoa(c.Port)))
		if c.containerRuntimeOpts.Type != constants.Docker {
			logoutCmd = fmt.Sprintf("nerdctl logout %s ", net.JoinHostPort(c.Domain, strconv.Itoa(c.Port)))
		}
		uninstallCmd = append(uninstallCmd, logoutCmd)
	}
	// 删除registry配置信息
	delRegistryConfiguration := func(host net.IP) error {
		err := c.infraDriver.CmdAsync(host, nil, strings.Join(uninstallCmd, "&&"))
		if err != nil {
			return fmt.Errorf("failed to delete registry configuration: %v", err)
		}
		return nil
	}

	return c.infraDriver.Execute(hosts, delRegistryConfiguration)
}

func (c *localConfigurator) InstallOn(masters, nodes []net.IP) error {
	hosts := append(masters, nodes...)
	klog.Infof("will install local private registry configuration on %+v\n", hosts)
	if err := c.configureRegistryNetwork(masters, nodes); err != nil {
		return err
	}

	if err := c.configureDaemonService(hosts); err != nil {
		return err
	}

	if err := c.configureAccessCredential(hosts); err != nil {
		return err
	}

	return nil
}

// configureRegistryNetwork 新增 "/etc/hosts" 中registry的域名解析规则
func (c *localConfigurator) configureRegistryNetwork(masters, nodes []net.IP) error {
	if !*c.HA {
		return c.configureSingletonHostsFile(append(masters, nodes...))
	}

	eg, _ := errgroup.WithContext(context.Background())

	for i := range masters {
		master := masters[i]
		eg.Go(func() error {
			// master节点上的registry域名规则还是master本身
			cmd := shellcommand.CommandSetHostAlias(c.Domain, master.String())
			if err := c.infraDriver.CmdAsync(master, nil, cmd); err != nil {
				return fmt.Errorf("failed to config masters hosts file: %v", err)
			}
			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		return err
	}

	// if masters is nil, means no need to flush old nodes
	// if len(masters) == 0 {
	// 	return c.buildLvsRule(c.deployHosts, nodes)
	// }
	// return c.buildLvsRule(c.deployHosts, c.infraDriver.GetHostIPListByRole(consts.NODE))

	return c.setHostAlias(nodes)
}

func (c *localConfigurator) SetHostAlias(clientHosts []net.IP) error {
	return c.setHostAlias(clientHosts)
}

// setHostAlias 为worker节点设置registry域名解析
func (c *localConfigurator) setHostAlias(clientHosts []net.IP) error {
	klog.V(5).Infof("start set host record for nodes [%s]", clientHosts)
	vip := LocalRegistryVIP(c.infraDriver)
	eg, _ := errgroup.WithContext(context.Background())

	for i := range clientHosts {
		n := clientHosts[i]
		eg.Go(func() error {
			if err := c.infraDriver.CmdAsync(n, nil, shellcommand.CommandSetHostAlias(c.Domain, vip)); err != nil {
				return fmt.Errorf("failed to config nodes hosts file cmd: %v", err)
			}
			return nil
		})
	}
	return eg.Wait()
}

// 删除registry域名解析
func (c *localConfigurator) unSetHostAlias(clientHosts []net.IP) error {
	klog.V(5).Infof("start delete host record for nodes [%s]", clientHosts)
	eg, _ := errgroup.WithContext(context.Background())

	for i := range clientHosts {
		n := clientHosts[i]
		eg.Go(func() error {
			if err := c.infraDriver.CmdAsync(n, nil, shellcommand.CommandUnSetHostAlias()); err != nil {
				return fmt.Errorf("failed to config nodes hosts file cmd: %v", err)
			}
			return nil
		})
	}
	return eg.Wait()
}

func (c *localConfigurator) configureSingletonHostsFile(hosts []net.IP) error {
	klog.V(5).Info("start configure single hosts file")
	// add registry ip to "/etc/hosts"
	f := func(host net.IP) error {
		err := c.infraDriver.CmdAsync(host, nil, shellcommand.CommandSetHostAlias(c.Domain, c.deployHosts[0].String()))
		if err != nil {
			return fmt.Errorf("failed to config cluster hosts file cmd: %v", err)
		}
		return nil
	}

	return c.infraDriver.Execute(hosts, f)
}

func (c *localConfigurator) configureRegistryCert(hosts []net.IP) error {
	// if deploy registry as InsecureMode ,skip to configure cert.
	if *c.Insecure {
		return nil
	}

	var (
		endpoint = net.JoinHostPort(c.Domain, strconv.Itoa(c.Port))
		caFile   = c.Domain + ".crt"
		src      = filepath.Join(c.infraDriver.GetClusterRootfsPath(), "certs", caFile)
		dest     = filepath.Join(c.containerRuntimeOpts.CertsDir, endpoint, caFile)
	)

	return c.copy2RemoteHosts(src, dest, hosts)
}

func (c *localConfigurator) configureAccessCredential(hosts []net.IP) error {
	var (
		username        = c.RegistryConfig.Username
		password        = c.RegistryConfig.Password
		endpoint        = net.JoinHostPort(c.Domain, strconv.Itoa(c.Port))
		tmpAuthFilePath = "/tmp/config.json"
		// todo we need this config file when kubelet pull images from registry. while, we could optimize the logic here.
		remoteKubeletAuthFilePath = "/var/lib/kubelet/config.json"
	)

	if username == "" || password == "" {
		return nil
	}

	if err := auth.Login(context.TODO(),
		nil,
		&auth.LoginOptions{
			AuthFile:           tmpAuthFilePath,
			Password:           password,
			Username:           username,
			Stdout:             os.Stdout,
			AcceptRepositories: true,
		},
		[]string{endpoint}); err != nil {
		return err
	}

	defer func() {
		if err := os.Remove(tmpAuthFilePath); err != nil {
			klog.Warningf("failed to remove tmp registry auth file:%s", tmpAuthFilePath)
		}
	}()

	if err := c.copy2RemoteHosts(tmpAuthFilePath, c.containerRuntimeOpts.ConfigFilePath, hosts); err != nil {
		return err
	}

	return c.copy2RemoteHosts(tmpAuthFilePath, remoteKubeletAuthFilePath, hosts)
}

func (c *localConfigurator) copy2RemoteHosts(src, dest string, hosts []net.IP) error {
	klog.V(5).Infof("start to copy local file [%s] to remote [%s]", src, dest)
	f := func(host net.IP) error {
		err := c.infraDriver.Copy(host, src, dest)
		if err != nil {
			return fmt.Errorf("failed to copy local file %s to remote %s : %v", src, dest, err)
		}
		return nil
	}

	return c.infraDriver.Execute(hosts, f)
}

func (c *localConfigurator) configureDaemonService(hosts []net.IP) error {
	var (
		src      string
		dest     string
		endpoint = net.JoinHostPort(c.Domain, strconv.Itoa(c.Port))
	)

	if endpoint == constants.DefaultRegistryURL {
		return nil
	}

	switch c.containerRuntimeOpts.Type {
	case constants.Docker:
		src = filepath.Join(c.infraDriver.GetClusterRootfsPath(), constants.Etc, "daemon.json")
		dest = "/etc/docker/daemon.json"
		if err := c.configureDockerDaemonService(endpoint, src); err != nil {
			return err
		}
	case constants.Containerd:
		src = filepath.Join(c.infraDriver.GetClusterRootfsPath(), constants.Etc, "hosts.toml")
		dest = filepath.Join(constants.DefaultContainerdCertsDir, endpoint, "hosts.toml")
		if err := c.configureContainerdDaemonService(endpoint, src); err != nil {
			return err
		}
	default:
		return fmt.Errorf("unsupport the runtime type [%s] now", c.containerRuntimeOpts.Type)
	}

	eg, _ := errgroup.WithContext(context.Background())

	// for docker: copy daemon.json to "/etc/docker/daemon.json"
	// for containerd : copy hosts.toml to "/etc/containerd/certs.d/${domain}:${port}/hosts.toml"
	for i := range hosts {
		ip := hosts[i]
		eg.Go(func() error {
			if err := c.infraDriver.Copy(ip, src, dest); err != nil {
				return err
			}
			if err := c.infraDriver.CmdAsync(ip, nil, "systemctl daemon-reload"); err != nil {
				return fmt.Errorf("faild to excute cmd [%s]: %v", "systemctl daemon-reload", err)
			}
			return nil
		})
	}
	return eg.Wait()
}

// configureDockerDaemonService configure doaemon service for docker
func (c *localConfigurator) configureDockerDaemonService(endpoint, daemonFile string) error {
	var daemonConf DaemonConfig

	b, err := os.ReadFile(filepath.Clean(daemonFile))
	if err != nil {
		return err
	}

	b = bytes.TrimSpace(b)
	// if config file is empty, only add registry config.
	if len(b) != 0 {
		if err = json.Unmarshal(b, &daemonConf); err != nil {
			return fmt.Errorf("failed to load %s to DaemonConfig: %v", daemonFile, err)
		}
	}
	daemonConf.RegistryMirrors = append(daemonConf.RegistryMirrors, "https://"+endpoint)

	content, err := json.MarshalIndent(daemonConf, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal daemonFile: %v", err)
	}

	return rw.NewCommonWriter(daemonFile).WriteFile(content)
}

// configureContainerdDaemonService configure daemon service for containerd
func (c *localConfigurator) configureContainerdDaemonService(endpoint, hostTomlFile string) error {
	var (
		caFile             = c.Domain + ".crt"
		registryCaCertPath = filepath.Join(c.containerRuntimeOpts.CertsDir, endpoint, caFile)
		url                = "https://" + endpoint
	)
	cfg := Hosts{
		Server: url,
		HostConfigs: map[string]HostFileConfig{
			url: {CACert: registryCaCertPath},
		},
	}
	bs, err := toml.Marshal(cfg)
	if err != nil {
		return fmt.Errorf("failed to marshal containerd hosts.toml file: %v", err)
	}

	return rw.NewCommonWriter(hostTomlFile).WriteFile(bs)
}

type Hosts struct {
	// Server specifies the default server. When `host` is
	// also specified, those hosts are tried first.
	Server string `toml:"server"`
	// HostConfigs store the per-host configuration
	HostConfigs map[string]HostFileConfig `toml:"host"`
}

type HostFileConfig struct {
	// CACert are the public key certificates for TLS
	// Accepted types
	// - string - Single file with certificate(s)
	// - []string - Multiple files with certificates
	CACert interface{} `toml:"ca"`
}

type DaemonConfig struct {
	AllowNonDistributableArtifacts []string          `json:"allow-nondistributable-artifacts,omitempty"`
	APICorsHeader                  string            `json:"api-cors-header,omitempty"`
	AuthorizationPlugins           []string          `json:"authorization-plugins,omitempty"`
	Bip                            string            `json:"bip,omitempty"`
	Bridge                         string            `json:"bridge,omitempty"`
	CgroupParent                   string            `json:"cgroup-parent,omitempty"`
	ClusterAdvertise               string            `json:"cluster-advertise,omitempty"`
	ClusterStore                   string            `json:"cluster-store,omitempty"`
	Containerd                     string            `json:"containerd,omitempty"`
	ContainerdNamespace            string            `json:"containerd-namespace,omitempty"`
	ContainerdPluginNamespace      string            `json:"containerd-plugin-namespace,omitempty"`
	DataRoot                       string            `json:"data-root,omitempty"`
	Debug                          bool              `json:"debug,omitempty"`
	DefaultCgroupnsMode            string            `json:"default-cgroupns-mode,omitempty"`
	DefaultGateway                 string            `json:"default-gateway,omitempty"`
	DefaultGatewayV6               string            `json:"default-gateway-v6,omitempty"`
	DefaultRuntime                 string            `json:"default-runtime,omitempty"`
	DefaultShmSize                 string            `json:"default-shm-size,omitempty"`
	DNS                            []string          `json:"dns,omitempty"`
	DNSOpts                        []string          `json:"dns-opts,omitempty"`
	DNSSearch                      []string          `json:"dns-search,omitempty"`
	ExecOpts                       []string          `json:"exec-opts,omitempty"`
	ExecRoot                       string            `json:"exec-root,omitempty"`
	Experimental                   bool              `json:"experimental,omitempty"`
	FixedCidr                      string            `json:"fixed-cidr,omitempty"`
	FixedCidrV6                    string            `json:"fixed-cidr-v6,omitempty"`
	Group                          string            `json:"group,omitempty"`
	Hosts                          []string          `json:"hosts,omitempty"`
	Icc                            bool              `json:"icc,omitempty"`
	Init                           bool              `json:"init,omitempty"`
	InitPath                       string            `json:"init-path,omitempty"`
	InsecureRegistries             []string          `json:"insecure-registries,omitempty"`
	IP                             string            `json:"ip,omitempty"`
	IPForward                      bool              `json:"ip-forward,omitempty"`
	IPMasq                         bool              `json:"ip-masq,omitempty"`
	Iptables                       bool              `json:"iptables,omitempty"`
	IP6Tables                      bool              `json:"ip6tables,omitempty"`
	Ipv6                           bool              `json:"ipv6,omitempty"`
	Labels                         []string          `json:"labels,omitempty"`
	LiveRestore                    bool              `json:"live-restore,omitempty"`
	LogDriver                      string            `json:"log-driver,omitempty"`
	LogLevel                       string            `json:"log-level,omitempty"`
	MaxConcurrentDownloads         int               `json:"max-concurrent-downloads,omitempty"`
	MaxConcurrentUploads           int               `json:"max-concurrent-uploads,omitempty"`
	MaxDownloadAttempts            int               `json:"max-download-attempts,omitempty"`
	Mtu                            int               `json:"mtu,omitempty"`
	NoNewPrivileges                bool              `json:"no-new-privileges,omitempty"`
	NodeGenericResources           []string          `json:"node-generic-resources,omitempty"`
	OomScoreAdjust                 int               `json:"oom-score-adjust,omitempty"`
	Pidfile                        string            `json:"pidfile,omitempty"`
	RawLogs                        bool              `json:"raw-logs,omitempty"`
	RegistryMirrors                []string          `json:"registry-mirrors,omitempty"`
	SeccompProfile                 string            `json:"seccomp-profile,omitempty"`
	SelinuxEnabled                 bool              `json:"selinux-enabled,omitempty"`
	ShutdownTimeout                int               `json:"shutdown-timeout,omitempty"`
	StorageDriver                  string            `json:"storage-driver,omitempty"`
	StorageOpts                    []string          `json:"storage-opts,omitempty"`
	SwarmDefaultAdvertiseAddr      string            `json:"swarm-default-advertise-addr,omitempty"`
	TLS                            bool              `json:"tls,omitempty"`
	Tlscacert                      string            `json:"tlscacert,omitempty"`
	Tlscert                        string            `json:"tlscert,omitempty"`
	Tlskey                         string            `json:"tlskey,omitempty"`
	Tlsverify                      bool              `json:"tlsverify,omitempty"`
	UserlandProxy                  bool              `json:"userland-proxy,omitempty"`
	UserlandProxyPath              string            `json:"userland-proxy-path,omitempty"`
	UsernsRemap                    string            `json:"userns-remap,omitempty"`
	ClusterStoreOpts               map[string]string `json:"cluster-store-opts,omitempty"`
	LogOpts                        *DaemonLogOpts    `json:"log-opts,omitempty"`
}

type DaemonLogOpts struct {
	CacheDisabled string `json:"cache-disabled,omitempty"`
	CacheMaxFile  string `json:"cache-max-file,omitempty"`
	CacheMaxSize  string `json:"cache-max-size,omitempty"`
	CacheCompress string `json:"cache-compress,omitempty"`
	Env           string `json:"env,omitempty"`
	Labels        string `json:"labels,omitempty"`
	MaxFile       string `json:"max-file,omitempty"`
	MaxSize       string `json:"max-size,omitempty"`
}

// LocalRegistryVIP 获取Local regisgtry的vip
func LocalRegistryVIP(infraDriver infradriver.InfraDriver) string {
	vip := constants.DefaultVIP
	if hosts := infraDriver.GetHostIPList(); len(hosts) > 0 && k8snet.IsIPv6(hosts[0]) {
		vip = constants.DefaultVIPForIPv6
	}
	// 与apiserver共用一个vip，只是端口不一样
	if ipv4, ok := infraDriver.GetClusterEnv()[constants.EnvIPvsVIPForIPv4]; ok {
		vip = ipv4
	}

	if ipv6, ok := infraDriver.GetClusterEnv()[constants.EnvIPvsVIPForIPv6]; ok {
		vip = ipv6
	}
	klog.V(5).Infof("success build vip for registry [%s]", vip)

	return vip
}
