package imagedistributor

import (
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strings"

	imgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	entimg "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image"
	imageengine "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image-engine"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
)

type buildAhMounter struct {
	imageEngine imageengine.ImageManager
	fs          file.Interface
}

func (b buildAhMounter) Mount(imageName string, platform imgv1beta1.Platform, dest string) (string, string, string, error) {
	mountDir := filepath.Join(dest,
		strings.ReplaceAll(imageName, "/", "_"),
		strings.Join([]string{platform.OS, platform.Architecture, platform.Variant}, "_"))

	imageID, err := b.imageEngine.Pull(&entimg.PullOpts{
		SkipTLSVerify: true,
		Quiet:         false,
		PullPolicy:    "missing",
		Image:         imageName,
		Platform:      platform.ToString(),
	})
	if err != nil {
		return "", "", "", err
	}
	if err = b.fs.MkdirAll(filepath.Dir(mountDir)); err != nil {
		return "", "", "", err
	}

	id, err := b.imageEngine.CreateWorkingContainer(&entimg.BuildRootfsOpts{
		DestDir:       mountDir,
		ImageNameOrID: imageID,
	})

	if err != nil {
		return "", "", "", err
	}
	return mountDir, id, imageID, nil
}

func (b buildAhMounter) Umount(mountDir, cid string) error {
	if err := b.fs.RemoveAll(mountDir); err != nil {
		return fmt.Errorf("failed to remove mount dir %s: %v", mountDir, err)
	}

	if err := b.imageEngine.RemoveContainer(&entimg.RemoveContainerOpts{
		ContainerNamesOrIDs: []string{cid},
	}); err != nil {
		return fmt.Errorf("failed to remove working container: %v", err)
	}

	return nil
}

func NewBuildAhMounter(imageEngine imageengine.ImageManager) Mounter {
	return buildAhMounter{
		imageEngine: imageEngine,
		fs:          file.NewFileSystem(),
	}
}

type ImagerMounter struct {
	Mounter
	rootDir       string
	hostsPlatform map[imgv1beta1.Platform][]net.IP
	fs            file.Interface
}

type ClusterImageMountInfo struct {
	// target hosts ip list, not all cluster ips.
	Hosts       []net.IP
	Platform    imgv1beta1.Platform
	MountDir    string
	ContainerID string
	ImageID     string
}

func (c ImagerMounter) Mount(imageName string) ([]ClusterImageMountInfo, error) {
	var imageMountInfos []ClusterImageMountInfo
	for platform, hosts := range c.hostsPlatform {
		mountDir, cid, imageID, err := c.Mounter.Mount(imageName, platform, c.rootDir)
		if err != nil {
			return nil, fmt.Errorf("failed to mount image %s with platform %s:%v", imageName, platform.ToString(), err)
		}
		imageMountInfos = append(imageMountInfos, ClusterImageMountInfo{
			Hosts:       hosts,
			Platform:    platform,
			MountDir:    mountDir,
			ContainerID: cid,
			ImageID:     imageID,
		})
	}

	return imageMountInfos, nil
}

func (c ImagerMounter) Umount(imageName string, imageMountInfo []ClusterImageMountInfo) error {
	for _, info := range imageMountInfo {
		err := c.Mounter.Umount(info.MountDir, info.ContainerID)
		if err != nil {
			return fmt.Errorf("failed to umount %s:%v", info.MountDir, err)
		}
	}

	// delete all mounted images
	if err := c.fs.RemoveAll(c.rootDir); err != nil {
		return err
	}
	return nil
}

func NewImageMounter(imageEngine imageengine.ImageManager, hostsPlatform map[imgv1beta1.Platform][]net.IP) (*ImagerMounter, error) {
	tempDir, err := os.MkdirTemp("", "kubepilot-mount-tmp")
	if err != nil {
		return nil, fmt.Errorf("failed to create tmp mount dir for kubepilot, err: %v", err)
	}

	c := &ImagerMounter{
		// TODO : user could set this value by env or kubepilot config
		rootDir:       tempDir,
		hostsPlatform: hostsPlatform,
		fs:            file.NewFileSystem(),
	}
	c.Mounter = NewBuildAhMounter(imageEngine)

	return c, nil
}
