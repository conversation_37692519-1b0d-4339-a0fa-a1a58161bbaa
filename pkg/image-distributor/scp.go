package imagedistributor

import (
	"context"
	"fmt"
	"net"
	"os"
	"path/filepath"

	confv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/config/v1beta1"
	clusterconfig "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-config"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/env"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
	"k8s.io/klog/v2"

	"golang.org/x/sync/errgroup"
)

const (
	RegistryDirName      = "registry"
	RootfsCacheDirName   = "image"
	RegistryCacheDirName = "registry"
)

type scpDistributor struct {
	configs          []confv1beta1.Config
	infraDriver      infradriver.InfraDriver
	imageMountInfo   []ClusterImageMountInfo
	registryCacheDir string
	rootfsCacheDir   string
	options          DistributeOption
}

// DistributeRegistry 分发文件到目标registry
func (s *scpDistributor) DistributeRegistry(dsthosts []net.IP, dataDir string) error {
	for _, info := range s.imageMountInfo {
		if !file.IsFileExist(filepath.Join(info.MountDir, RegistryDirName)) {
			continue
		}
		localCacheFile := filepath.Join(info.MountDir, info.ImageID)
		remoteCacheFile := filepath.Join(s.registryCacheDir, info.ImageID)
		eg, _ := errgroup.WithContext(context.Background())

		for _, host := range dsthosts {
			dst := host
			eg.Go(func() error {
				// 是否忽略缓存文件
				if !s.options.IgnoreCache {
					// detect if remote cache file is exist.
					existed, err := s.infraDriver.IsFileExist(dst, remoteCacheFile)
					if err != nil {
						return fmt.Errorf("failed to detect registry cache %s on host %s: %v", remoteCacheFile, dst.String(), err)
					}

					if existed {
						klog.V(5).Infof("cache %s hits on: %s, skip to do distribution, it will be not skip if set `--ignore-cache=true`", info.ImageID, dst.String())
						return nil
					}
				}

				// copy registry data
				if err := s.infraDriver.Copy(dst, filepath.Join(info.MountDir, RegistryDirName), dataDir); err != nil {
					return fmt.Errorf("failed to copy registry data %s: %v", info.MountDir, err)
				}

				// write cache flag
				if err := s.writeCacheFlag(localCacheFile, remoteCacheFile, dst); err != nil {
					return fmt.Errorf("failed to write registry cache %s on host %s: %v", remoteCacheFile, dst.String(), err)
				}

				return nil
			})
		}
		if err := eg.Wait(); err != nil {
			return err
		}
	}

	return nil
}

func (s *scpDistributor) Distribute(hosts []net.IP, dest string) error {
	for _, info := range s.imageMountInfo {
		if err := s.dumpConfigToRootfs(info.MountDir); err != nil {
			return err
		}
		// Render rootfs .tmpl by cluster Env
		if err := s.renderRootfs(info.MountDir); err != nil {
			return err
		}

		eg, _ := errgroup.WithContext(context.Background())
		localCacheFile := filepath.Join(info.MountDir, info.ImageID)
		remoteCacheFile := filepath.Join(s.rootfsCacheDir, info.ImageID)

		for _, ip := range info.Hosts {
			host := ip
			eg.Go(func() error {
				if !s.options.IgnoreCache {
					// detect if remote cache file is exist.
					existed, err := s.infraDriver.IsFileExist(host, remoteCacheFile)
					if err != nil {
						return fmt.Errorf("failed to detect rootfs cache %s on host %s: %v",
							remoteCacheFile, host.String(), err)
					}

					if existed {
						klog.V(5).Infof("cache %s hits on: %s, skip to do distribution", info.ImageID, host.String())
						return nil
					}
				}

				// copy rootfs data
				err := s.filterCopy(info.MountDir, dest, host)
				if err != nil {
					return fmt.Errorf("failed to copy rootfs files: %v", err)
				}

				// write cache flag
				err = s.writeCacheFlag(localCacheFile, remoteCacheFile, host)
				if err != nil {
					return fmt.Errorf("failed to write rootfs cache %s on host %s: %v",
						remoteCacheFile, host.String(), err)
				}

				return nil
			})
		}

		if err := eg.Wait(); err != nil {
			return err
		}
	}

	return nil
}

func (s *scpDistributor) DistributorApplicationDir(targetDir string, hosts []net.IP) error {
	if err := os.MkdirAll(filepath.Join(os.TempDir(), targetDir), os.ModePerm); err != nil {
		return err
	}
	eg, _ := errgroup.WithContext(context.Background())
	for _, ip := range hosts {
		host := ip
		eg.Go(func() error {
			if err := s.infraDriver.Copy(host, filepath.Join(os.TempDir(), targetDir), s.infraDriver.GetClusterRootfsPath()); err != nil {
				return err
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return err
	}
	return nil
}

func (s *scpDistributor) filterCopy(mountDir, dest string, host net.IP) error {
	files, err := os.ReadDir(mountDir)
	if err != nil {
		return fmt.Errorf("failed to read dir %s: %s", mountDir, err)
	}

	for _, f := range files {
		// skip registry directory
		if f.IsDir() && f.Name() == RegistryDirName {
			continue
		}

		if len(s.options.DistributionDir) != 0 && s.options.DistributionDir != f.Name() {
			continue
		}

		// copy rootfs data
		if err = s.infraDriver.Copy(host, filepath.Join(mountDir, f.Name()), filepath.Join(dest, f.Name())); err != nil {
			return fmt.Errorf("failed to copy rootfs files: %v", err)
		}
	}

	return nil
}

func (s *scpDistributor) dumpConfigToRootfs(mountDir string) error {
	return clusterconfig.NewConfiguration(mountDir).Dump(s.configs)
}

// renderRootfs Using cluster Env data to Render Rootfs files
func (s *scpDistributor) renderRootfs(mountDir string) error {
	var (
		renderEtc       = filepath.Join(mountDir, constants.Etc)
		renderChart     = filepath.Join(mountDir, constants.Charts)
		renderManifests = filepath.Join(mountDir, constants.Manifests)
		renderScripts   = filepath.Join(mountDir, constants.Scripts)
		// TODO: 目前plugins没有使用模版文件，但是可以作为拓展
		renderPlugins = filepath.Join(mountDir, constants.Plugins)
		renderData    = s.infraDriver.GetClusterEnv()
	)

	// 如果是安装组件,应该获取应用的环境变量渲染数据，而非集群的环境变量
	if s.options.IsApplication {
		for k, v := range s.infraDriver.GetApplicationEnv() {
			renderData[k] = v
		}
	}

	for _, dir := range []string{renderEtc, renderChart, renderManifests, renderScripts, renderPlugins} {
		// 基于集群的环境变量开始渲染该组件的 `etc`,`charts`,`manifest` 文件夹内的模版文件
		if file.IsFileExist(dir) {
			if err := env.RenderTemplate(dir, renderData); err != nil {
				return err
			}
		}
	}

	return nil
}

// writeCacheFlag : write image sha256ID to remote host.
// remoteCacheFile looks like: /var/lib/kubepilot/data/kube-cluster/rootfs/cache/registry/9eb6f8a1ca09559189dd1fed5e587b14
func (s *scpDistributor) writeCacheFlag(localCacheFile, remoteCacheFile string, host net.IP) error {
	if !file.IsFileExist(localCacheFile) {
		err := rw.NewCommonWriter(localCacheFile).WriteFile([]byte(""))
		if err != nil {
			return fmt.Errorf("failed to write local cache file %s: %v", localCacheFile, err)
		}
	}

	err := s.infraDriver.Copy(host, localCacheFile, remoteCacheFile)
	if err != nil {
		return fmt.Errorf("failed to copy rootfs cache file: %v", err)
	}

	klog.V(5).Infof("successfully write cache file %s on: %s", remoteCacheFile, host.String())
	return nil
}

func (s *scpDistributor) Restore(targetDir string, hosts []net.IP) error {
	if !s.options.Prune {
		return nil
	}

	rmRootfsCMD := fmt.Sprintf("rm -rf %s", targetDir)

	eg, _ := errgroup.WithContext(context.Background())
	for _, ip := range hosts {
		host := ip
		eg.Go(func() error {
			err := s.infraDriver.CmdAsync(host, nil, rmRootfsCMD)
			if err != nil {
				return fmt.Errorf("faild to delete rootfs on host [%s]: %v", host.String(), err)
			}
			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		return err
	}

	return nil
}

func NewScpDistributor(imageMountInfo []ClusterImageMountInfo, driver infradriver.InfraDriver, configs []confv1beta1.Config, options DistributeOption) (Distributor, error) {
	return &scpDistributor{
		configs:          configs,
		imageMountInfo:   imageMountInfo,
		infraDriver:      driver,
		registryCacheDir: filepath.Join(driver.GetClusterRootfsPath(), "cache", RegistryCacheDirName),
		rootfsCacheDir:   filepath.Join(driver.GetClusterRootfsPath(), "cache", RootfsCacheDirName),
		options:          options,
	}, nil
}
