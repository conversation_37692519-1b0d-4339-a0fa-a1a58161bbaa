package imagedistributor

import (
	"net"

	imgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
)

type Distributor interface {
	// Distribute each files under mounted cluster image directory to target hosts.
	Distribute(hosts []net.IP, dest string) error
	// DistributeRegistry each files under registry directory to target hosts.
	DistributeRegistry(deshosts []net.IP, dataDir string) error
	// DistributorApplicationDir 分发用于存放组件的空目录
	DistributorApplicationDir(targetDir string, hosts []net.IP) error
	// Restore will do some clean works via infra driver, like delete rootfs.
	Restore(targetDir string, hosts []net.IP) error
}

type Mounter interface {
	// Mount cluster image with specified platform, return mount Dir,container ID,image ID and err.
	Mount(imageName string, platform imgv1beta1.Platform, dest string) (string, string, string, error)

	// Umount delete all mounted directory and remove related working container.
	Umount(dir, containerID string) error
}
