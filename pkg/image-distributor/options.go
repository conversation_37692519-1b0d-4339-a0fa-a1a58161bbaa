// Copyright © 2023 Alibaba Group Holding Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package imagedistributor

// DistributeOption control some distribute logic.
type DistributeOption struct {
	// IgnoreCache: indicate that whether kubepilot use cache when distribute cluster image,
	// if not, will force sync cluster rootfs.
	// default is false.
	IgnoreCache bool

	// Prune: if it is true, will delete all cluster rootfs
	// default is false.
	Prune           bool
	DistributionDir string
	IsApplication   bool
}
