package shellcommand

import (
	"fmt"
)

const DefaultKubepilotHostAliasAnnotation = "#hostalias-set-by-kubepilot"

func CommandSetHostAlias(hostName, ip string) string {
	return fmt.Sprintf(`if grep " %s " /etc/hosts &>/dev/null;then sed -i "/\ %s\ /d" /etc/hosts; fi;echo "%s %s %s" >>/etc/hosts`, hostName, hostName, ip, hostName, DefaultKubepilotHostAliasAnnotation)
}

func CommandUnSetHostAlias() string {
	return fmt.Sprintf(`echo "$(sed "/%s/d" /etc/hosts)" > /etc/hosts`, DefaultKubepilotHostAliasAnnotation)
}
