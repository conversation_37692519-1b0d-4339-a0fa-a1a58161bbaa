package plugin

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	pgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/plugin/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	k8syaml "k8s.io/apimachinery/pkg/util/yaml"
)

// transferPluginsToHooks 转换插件为hooks
func transferPluginsToHooks(plugins []pgv1beta1.Plugin) (map[Phase]HookConfigList, error) {
	hooks := make(map[Phase]HookConfigList)

	for _, pluginConfig := range plugins {
		pluginConfig.Spec.Data = strings.TrimSuffix(pluginConfig.Spec.Data, "\n")
		hookType := HookType(pluginConfig.Spec.Type)

		_, ok := hookFactories[hookType]
		if !ok {
			return nil, fmt.Errorf("hook type: %s is not registered", hookType)
		}

		// 用"|"拆分pluginConfig.Spec.Action字段的值实现支持组合插件的操作
		phaseList := strings.Split(pluginConfig.Spec.Action, "|")
		for _, phase := range phaseList {
			if phase == "" {
				continue
			}
			hookConfig := HookConfig{
				Name:  pluginConfig.Name,
				Data:  pluginConfig.Spec.Data,
				Type:  hookType,
				Phase: Phase(phase),
				Scope: Scope(pluginConfig.Spec.Scope),
			}

			if _, ok = hooks[hookConfig.Phase]; !ok {
				// add new Phase
				hooks[hookConfig.Phase] = []HookConfig{hookConfig}
			} else {
				hooks[hookConfig.Phase] = append(hooks[hookConfig.Phase], hookConfig)
			}
		}
	}
	return hooks, nil
}

// LoadPluginsFromFile 从/var/lib/kubepilot/data/$cluster_name/rootfs/plugins 或者临时挂载文件 /tmp/xxx/plugins中加载插件
func LoadPluginsFromFile(pluginPath string) ([]pgv1beta1.Plugin, error) {
	_, err := os.Stat(pluginPath)
	if os.IsNotExist(err) {
		return nil, nil
	}

	files, err := os.ReadDir(pluginPath)
	if err != nil {
		return nil, fmt.Errorf("failed to ReadDir plugin dir %s: %v", pluginPath, err)
	}

	var plugins []pgv1beta1.Plugin
	for _, f := range files {
		if !file.Matcher(f.Name()) {
			continue
		}
		pluginFile := filepath.Join(pluginPath, f.Name())
		pluginList, err := DecodePluginFile(pluginFile)
		if err != nil {
			return nil, fmt.Errorf("failed to decode plugin file %s: %v", pluginFile, err)
		}
		plugins = append(plugins, pluginList...)
	}

	return plugins, nil
}

// DecodePluginFile 解析插件文件
func DecodePluginFile(pluginFile string) ([]pgv1beta1.Plugin, error) {
	var plugins []pgv1beta1.Plugin
	data, err := os.ReadFile(filepath.Clean(pluginFile))
	if err != nil {
		return nil, err
	}

	decoder := k8syaml.NewYAMLToJSONDecoder(bufio.NewReaderSize(bytes.NewReader(data), 4096))
	for {
		ext := runtime.RawExtension{}
		if err := decoder.Decode(&ext); err != nil {
			if err == io.EOF {
				return plugins, nil
			}
			return nil, err
		}

		ext.Raw = bytes.TrimSpace(ext.Raw)
		if len(ext.Raw) == 0 || bytes.Equal(ext.Raw, []byte("null")) {
			continue
		}
		metaType := metav1.TypeMeta{}
		if err = k8syaml.Unmarshal(ext.Raw, &metaType); err != nil {
			return nil, fmt.Errorf("failed to decode TypeMeta: %v", err)
		}

		var plu pgv1beta1.Plugin
		if err = k8syaml.Unmarshal(ext.Raw, &plu); err != nil {
			return nil, fmt.Errorf("failed to decode %s[%s]: %v", metaType.Kind, metaType.APIVersion, err)
		}

		plu.Spec.Data = strings.TrimSuffix(plu.Spec.Data, "\n")
		plugins = append(plugins, plu)
	}
}
