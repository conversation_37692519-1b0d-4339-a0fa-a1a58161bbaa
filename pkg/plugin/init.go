package plugin

import (
	"fmt"
	"net"
	"sort"
	"strings"

	pgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/plugin/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"k8s.io/klog/v2"
)

/*
Plugin的设计哲学: 类似webhook
某些场景比如需要kubepilot去修改主机名，或者升级内核，或者同步时间这些 "本不该" 由kubepilot去做的事情，
因为这会使得kubepilot的架构会被业务影响，毕竟业务是持续迭代的，那么我们可以启用插件的这种热插拔的方式来完成这些工作.
具体可以查看下面的demo：taint.yaml

Plugin中勾子函数的结构设计
 Action字段：action可以组合使用, 用`|`符号分开，如"PreInitHost | PostInitHost"

| 阶段名称              | 定义            | 适应的节点范围  |
| -------------------- | ---------------| -------------- |
| PreInstallCluster    | 在安装集群前执行 | master0        |
| PostInstallCluster   | 在集群安装后执行 | master0        |
| PreUnInstallCluster  | 在卸载集群前执行 | master0        |
| PostUnInstallCluster | 在卸载集群后执行 | master0        |
| PreScaleUpCluster    | 在添加节点前执行 | master0        |
| PostScaleUpCluster   | 在添加节点后执行 | master0        |
| UpgradeCluster       | 在升级集群时执行 | master0        |
| RollbackCluster      | 在回归集群时执行 | master0        |
| PreInitHost          | 在初始化前执行   | on role        |
| PostInitHost         | 在初始化后执行   | on role        |
| PreCleanHost         | 在清理节点前执行 | on role        |
| PostCleanHost        | 在清理节点后执行 | on role        |
| UpgradeHost          | 在升级节点时执行 | on role        |
----------------------------------------------------------

 Scope字段

| 节点范围scope | 定义                      ｜ demo                                        ｜
| -------------| ------------------------  | ---------------------------------------------|
| "值为空"      | 为空时默认在所有节点执行    ｜ scope:                                       |
| master       | 在所有的master节点上执行    | scope: master                                ｜
| node         | 在所有的node节点上执行      | scope: node                                  ｜
| "IP值"       ｜ 在IP为该值的节点上执行      | scope: ************,************,************｜
| "IP范围"     ｜ 在有连续IP的机器上执行      ｜scope: ************-************              |
| label        ｜ 在指定label得节点上执行    ｜ scope: node-role.kubernetes.io/master=       ｜
--------------------------------------------------------------------------------------------

---taint.yaml---:
apiVersion: bingokube.bingosoft.net/v1beta1
kind: Plugin
metadata:
  name: taint
spec:
  type: SHELL
  action: PostInstall #安装完集群后执行
  scope: node-role.kubernetes.io/master=
  data: |
     kubectl taint nodes --all node-role.kubernetes.io/master-
*/

const (
	ShellHook HookType = "SHELL"
)

const (
	PreInstallCluster    Phase = "pre-install"
	PostInstallCluster   Phase = "post-install"
	PreUnInstallCluster  Phase = "pre-uninstall"
	PostUnInstallCluster Phase = "post-uninstall"
	PreScaleUpCluster    Phase = "pre-scaleup"
	PostScaleUpCluster   Phase = "post-scaleup"
	UpgradeCluster       Phase = "upgrade"
	RollbackCluster      Phase = "rollback"
	PreInitHost          Phase = "pre-init-host"
	PostInitHost         Phase = "post-init-host"
	PreCleanHost         Phase = "pre-clean-host"
	PostCleanHost        Phase = "post-clean-host"
	UpgradeHost          Phase = "upgrade-host"
	PreInstallApp        Phase = "pre-install-app"
	PostInstallApp       Phase = "post-install-app"
	PreUninstallApp      Phase = "pre-uninstall-app"
	PostUninstallApp     Phase = "post-uninstall-app"
	PreUpgradeApp        Phase = "pre-upgrade-app"
	PostUpgradeApp       Phase = "post-upgrade-app"
)

type HookType string

type Scope string

type Phase string

const (
	ExtraOptionSkipWhenWorkspaceNotExists = "SkipWhenWorkspaceNotExists"
)

// HookFunc data 为脚本数据； onHosts为执行的目标节点；driver是执行data的驱动；
type HookFunc func(data string, onHosts []net.IP, driver infradriver.InfraDriver, extraOpts map[string]bool) error

var hookFactories = make(map[HookType]HookFunc)

// HookConfig tell us how to configure hooks for cluster
type HookConfig struct {
	// Name 勾子名字(因为会放在map中运行，所以会按照首字母顺序运行).
	Name string `json:"name,omitempty"`
	// Type 勾子类型(暂时支持SHELL,若需支持其它的,需要在init函数中注册)
	Type HookType `json:"type,omitempty"`
	// Data 勾子数据,也就是脚本内容(在安装过程中使用).
	Data string `json:"data,omitempty"`
	// Phase 什么时候运行勾子函数.
	Phase Phase `json:"Phase,omitempty"`
	// Scope 勾子数据将应用于集群中的哪些角色节点
	Scope Scope `json:"scope,omitempty"`
}

type HookConfigList []HookConfig

func (r HookConfigList) Len() int           { return len(r) }
func (r HookConfigList) Swap(i, j int)      { r[i], r[j] = r[j], r[i] }
func (r HookConfigList) Less(i, j int) bool { return r[i].Name < r[j].Name }

type Handler interface {
	// RunOnHosts 在指定的host上执行phase类型的勾子函数.
	RunOnHosts(phase Phase, hosts []net.IP) error
	// RunOnMaster0 在master0上执行phase类型的勾子函数.
	RunOnMaster0(master0 net.IP, phase Phase) error
	// RunScope 获取作用域的ip列表，支持使用'|'来指定多个作用域. 作用域的关系是`or`
	RunScope(scope Scope) []net.IP
}

type hookOptions struct {
	hooks       map[Phase]HookConfigList
	infraDriver infradriver.InfraDriver
}

// NewHookOptions init plugin hook options
func NewHookOptions(plugins []pgv1beta1.Plugin, infraDriver infradriver.InfraDriver) (Handler, error) {
	hooks, err := transferPluginsToHooks(plugins)
	if err != nil {
		return nil, err
	}
	return &hookOptions{
		hooks:       hooks,
		infraDriver: infraDriver,
	}, nil
}

func (i *hookOptions) RunOnHosts(phase Phase, hosts []net.IP) error {
	hookConfigList, ok := i.hooks[phase]
	if !ok {
		klog.V(5).Infof("no hooks found at phase: %s", phase)
		return nil
	}

	extraOpts := map[string]bool{}
	if phase == PostCleanHost || phase == PreCleanHost {
		extraOpts[ExtraOptionSkipWhenWorkspaceNotExists] = true
	}

	// sorted by hookConfig name in alphabetical order
	sort.Sort(hookConfigList)
	for _, hookConfig := range hookConfigList {
		var (
			targetHosts   []net.IP
			expectedHosts []net.IP
		)
		// 当插件没填写执行域的时候,默认为所有目标节点
		if len(hookConfig.Scope) == 0 {
			klog.Warningf("The plugin [%s] has no scope, it will run on [%v]", hookConfig.Name, hosts)
			expectedHosts = hosts
		} else {
			expectedHosts = i.RunScope(hookConfig.Scope)
		}

		// Make sure each host got from Scope is in the given host ip list.
		for _, expected := range expectedHosts {
			if unet.IsInIPList(expected, hosts) {
				targetHosts = append(targetHosts, expected)
			}
		}

		if len(targetHosts) == 0 {
			klog.V(5).Infof("no expected host found from hook %s", hookConfig.Name)
			continue
		}

		klog.Infof("start to run hook (%s) on host (%s)", hookConfig.Name, targetHosts)
		if err := hookFactories[hookConfig.Type](hookConfig.Data, targetHosts, i.infraDriver, extraOpts); err != nil {
			return fmt.Errorf("failed to run hook: %s", hookConfig.Name)
		}
	}

	return nil
}

func (i *hookOptions) RunOnMaster0(master0 net.IP, phase Phase) error {
	hookConfigList, ok := i.hooks[phase]
	if !ok {
		klog.V(5).Infof("no hooks found about the type phase: %s", phase)
		return nil
	}
	// sorted by hookConfig name in alphabetical order
	sort.Sort(hookConfigList)

	extraOpts := map[string]bool{}
	if phase == PreUnInstallCluster || phase == PostUnInstallCluster {
		extraOpts[ExtraOptionSkipWhenWorkspaceNotExists] = true
	}

	for _, hookConfig := range hookConfigList {
		klog.Infof("start to run hook (%s) on host (%s)", hookConfig.Name, master0)
		if err := hookFactories[hookConfig.Type](hookConfig.Data, []net.IP{master0}, i.infraDriver, extraOpts); err != nil {
			return fmt.Errorf("failed to run cluster hook: %s", hookConfig.Name)
		}
	}

	return nil
}

func (i *hookOptions) RunScope(scope Scope) []net.IP {
	var ret []net.IP
	scopes := strings.Split(string(scope), "|")
	for _, s := range scopes {
		hosts := i.infraDriver.GetHostIPListByRole(strings.TrimSpace(s))

		// remove duplicates
		for _, h := range hosts {
			if !unet.IsInIPList(h, ret) {
				ret = append(ret, h)
			}
		}
	}

	return ret
}

// Register different hook type with its HookFunc to hookFactories
func Register(name HookType, factory HookFunc) {
	if factory == nil {
		panic("hook factory is nil")
	}
	_, registered := hookFactories[name]
	if registered {
		panic(fmt.Sprintf("hook factory [%s] already registered", name))
	}

	hookFactories[name] = factory
}

func init() {
	// TODO 在这里注册需要支持的插件类型
	Register(ShellHook, NewShellHook())
}
