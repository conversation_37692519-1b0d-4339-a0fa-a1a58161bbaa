package plugin

import (
	"fmt"
	"net"
	"path/filepath"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"k8s.io/klog/v2"
)

// NewShellHook 初始化shell类型插件的执行载体
func NewShellHook() HookFunc {
	return func(cmd string, hosts []net.IP, driver infradriver.InfraDriver, extraOpts map[string]bool) error {
		targetPath := driver.GetClusterRootfsPath()
		if len(driver.GetApplicationName()) > 0 {
			targetPath = filepath.Join(targetPath, driver.GetApplicationName())
		}

		for _, ip := range hosts {
			klog.Infof("start to run hook on host [%s]", ip.String())
			wrappedCmd := fmt.Sprintf(constants.CdAndExecCmd, targetPath, cmd)
			if extraOpts[ExtraOptionSkipWhenWorkspaceNotExists] {
				wrappedCmd = fmt.Sprintf(constants.CdIfExistAndExecCmd, targetPath, targetPath, cmd)
			}

			if err := driver.CmdAsync(ip, driver.GetHostEnv(ip), wrappedCmd); err != nil {
				return fmt.Errorf("failed to run shell hook (%s) on host (%s): %v", wrappedCmd, ip.String(), err)
			}
		}

		return nil
	}
}
