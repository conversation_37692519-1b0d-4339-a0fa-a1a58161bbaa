package ssh

import (
	"fmt"
	"net"
	"time"

	clusterv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	imgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"k8s.io/klog/v2"

	"github.com/imdario/mergo"
)

type Interface interface {
	// Copy local files to remote host
	// scp -r /tmp root@***********:/root/tmp => Copy("***********","tmp","/root/tmp")
	// need check md5sum
	Copy(host net.IP, srcFilePath, dstFilePath string) error
	// CopyR copy remote host files to localhost
	CopyR(host net.IP, srcFilePath, dstFilePath string) error
	// CmdAsync exec command on remote host, and asynchronous return logs
	CmdAsync(host net.IP, env map[string]string, cmd ...string) error
	// Cmd exec command on remote host, and return combined standard output and standard error
	Cmd(host net.IP, env map[string]string, cmd string) ([]byte, error)
	// CmdToString exec command on remote host, and return spilt standard output and standard error
	CmdToString(host net.IP, env map[string]string, cmd, spilt string) (string, error)
	// IsFileExist check remote file exist or not
	IsFileExist(host net.IP, remoteFilePath string) (bool, error)
	// RemoteDirExist Remote file existence returns true, nil
	RemoteDirExist(host net.IP, remoteDirpath string) (bool, error)
	// GetPlatform Get remote platform
	GetPlatform(host net.IP) (imgv1beta1.Platform, error)
	// Ping Ping remote host
	Ping(host net.IP) error
}

type SSH struct {
	AlsoToStdout bool
	Encrypted    bool
	User         string
	Password     string
	Port         string
	PkFile       string
	PkPassword   string
	Timeout      *time.Duration
	LocalAddress []net.Addr
	Fs           file.Interface
}

func NewSSHClient(ssh *clusterv1beta1.SSH, alsoToStdout bool) Interface {
	if ssh.User == "" {
		ssh.User = constants.ROOT
	}
	address, err := unet.GetLocalHostAddresses()
	if err != nil {
		klog.Warningf("failed to get local address: %v", err)
	}
	return &SSH{
		AlsoToStdout: alsoToStdout,
		Encrypted:    ssh.Encrypted,
		User:         ssh.User,
		Password:     ssh.Passwd,
		Port:         ssh.Port,
		PkFile:       ssh.Pk,
		PkPassword:   ssh.PkPasswd,
		LocalAddress: address,
		Fs:           file.NewFileSystem(),
	}
}

// GetHostSSHClient is used to executed bash command and no std out to be printed.
func GetHostSSHClient(hostIP net.IP, cluster *clusterv1beta1.Cluster) (Interface, error) {
	for i := range cluster.Spec.Hosts {
		for _, ip := range cluster.Spec.Hosts[i].IPS {
			if hostIP.Equal(ip) {
				if err := mergo.Merge(&cluster.Spec.Hosts[i].SSH, &cluster.Spec.SSH); err != nil {
					return nil, err
				}
				return NewSSHClient(&cluster.Spec.Hosts[i].SSH, false), nil
			}
		}
	}
	return nil, fmt.Errorf("failed to get host ssh client: host ip %s not in hosts ip list", hostIP)
}

// NewStdoutSSHClient is used to show std out when execute bash command.
func NewStdoutSSHClient(hostIP net.IP, cluster *clusterv1beta1.Cluster) (Interface, error) {
	for i := range cluster.Spec.Hosts {
		for _, ip := range cluster.Spec.Hosts[i].IPS {
			if hostIP.Equal(ip) {
				if err := mergo.Merge(&cluster.Spec.Hosts[i].SSH, &cluster.Spec.SSH); err != nil {
					return nil, err
				}
				return NewSSHClient(&cluster.Spec.Hosts[i].SSH, true), nil
			}
		}
	}
	return nil, fmt.Errorf("failed to get host ssh client: host ip %s not in hosts ip list", hostIP)
}
