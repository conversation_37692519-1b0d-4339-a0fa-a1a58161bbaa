package ssh

import (
	"bufio"
	"fmt"
	"net"
	"strings"

	imgv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/platform"
	"k8s.io/klog/v2"

	"github.com/pkg/errors"
)

func (s *SSH) GetPlatform(host net.IP) (imgv1beta1.Platform, error) {
	if unet.IsLocalIP(host, s.LocalAddress) {
		return platform.GetDefaultPlatform(), nil
	}

	p := imgv1beta1.Platform{}
	archResult, err := s.CmdToString(host, nil, "uname -m", "")
	if err != nil {
		return p, err
	}
	osResult, err := s.CmdToString(host, nil, "uname", "")
	if err != nil {
		return p, err
	}
	p.OS = strings.ToLower(strings.TrimSpace(osResult))
	switch strings.ToLower(strings.TrimSpace(archResult)) {
	case "x86_64":
		p.Architecture = platform.AMD
	case "aarch64":
		p.Architecture = platform.ARM64
	case "armv7l":
		p.Architecture = "arm-v7"
	case "armv6l":
		p.Architecture = "arm-v6"
	default:
		return p, fmt.Errorf("unrecognized architecture: %s", archResult)
	}
	if p.Architecture != platform.AMD {
		p.Variant, err = s.getCPUVariant(p.OS, p.Architecture, host)
		if err != nil {
			return p, err
		}
	}
	remotePlatform, err := platform.Parse(platform.Format(p))
	if err != nil {
		return p, err
	}
	return platform.Normalize(remotePlatform), nil
}

func (s *SSH) getCPUInfo(host net.IP, pattern string) (info string, err error) {
	sftpClient, err := s.sftpConnect(host)
	if err != nil {
		return "", fmt.Errorf("failed to new sftp client: %v", err)
	}

	// open remote source file
	srcFile, err := sftpClient.Open("/proc/cpuinfo")
	if err != nil {
		return "", fmt.Errorf("failed to open /proc/cpuinfo: %v", err)
	}
	defer func() {
		if err = srcFile.Close(); err != nil {
			klog.Warningf("failed to close file: %v", err)
		}
	}()
	scanner := bufio.NewScanner(srcFile)
	for scanner.Scan() {
		newline := scanner.Text()
		list := strings.Split(newline, ":")

		if len(list) > 1 && strings.EqualFold(strings.TrimSpace(list[0]), pattern) {
			return strings.TrimSpace(list[1]), nil
		}
	}
	// Check whether the scanner encountered errors
	err = scanner.Err()
	if err != nil {
		return "", err
	}
	return "", errors.Wrapf(platform.ErrNotFound, "getCPUInfo for pattern: %s", pattern)
}

func (s *SSH) getCPUVariant(os, arch string, host net.IP) (string, error) {
	variant, err := s.getCPUInfo(host, "Cpu architecture")
	if err != nil {
		return "", err
	}
	model, err := s.getCPUInfo(host, "model name")
	if err != nil {
		if !strings.Contains(err.Error(), platform.ErrNotFound.Error()) {
			return "", err
		}
	}
	variant, model = platform.NormalizeArch(variant, model)
	return platform.GetCPUVariantByInfo(os, arch, variant, model), nil
}
