package env

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"text/template"
)

const templateSuffix = ".tmpl"

func base64encode(v string) string {
	return base64.StdEncoding.EncodeToString([]byte(v))
}

func base64decode(v string) string {
	data, err := base64.StdEncoding.DecodeString(v)
	if err != nil {
		return err.Error()
	}
	return string(data)
}

// defaultValue 获取给定值的字符串，如果该值为空或未定义，则返回默认值
// 模板样例：pkg/env/test/template/flannel-values.yaml.tmpl
func defaultValue(value interface{}, defaultVal string) string {
	if value == nil || value == "" {
		return defaultVal
	}
	return value.(string)
}

// IsJSONStr json格式的字符串
func IsJSONStr(str string) bool {
	var js json.RawMessage
	return json.Unmarshal([]byte(str), &js) == nil
}

// RenderTemplate using renderData got from clusterfile to render all the files in dir with ".tmpl" as suffix.
// The scope of renderData comes from cluster.spec.env
func RenderTemplate(dir string, renderData map[string]string) error {
	var data = make(map[string]interface{})
	return filepath.Walk(dir, func(path string, info os.FileInfo, errIn error) error {
		if errIn != nil {
			return errIn
		}
		if info.IsDir() || !strings.HasSuffix(info.Name(), templateSuffix) {
			return nil
		}
		writer, err := os.Create(strings.TrimSuffix(path, templateSuffix))
		if err != nil {
			return fmt.Errorf("failed to open file [%s] when render env: %v", path, err)
		}
		defer func() {
			_ = writer.Close()
		}()
		t, err := template.New(info.Name()).Funcs(template.FuncMap{
			"b64enc":  base64encode,
			"b64dec":  base64decode,
			"default": defaultValue,
		}).ParseFiles(path)
		if err != nil {
			return fmt.Errorf("failed to create template(%s): %v", path, err)
		}

		for k, v := range renderData {
			// json格式的字符串
			if IsJSONStr(v) {
				res := fmt.Sprintf("%s=%s", k, v)
				if err = ParseJSON(res, data); err != nil {
					return fmt.Errorf("failed to parse [%+v] strings in json format :%v", v, err)
				}
			} else {
				data[k] = v
			}
		}

		if err = t.Execute(writer, data); err != nil {
			return fmt.Errorf("failed to render env template(%s): %v", path, err)
		}
		return nil
	})
}

// ConvertJsonStrToMap 将json字符串转换为[]map[string]string
func ConvertJsonStrToMap(res string) ([]map[string]string, error) {
	var dst []map[string]string
	if !IsJSONStr(res) {
		return nil, fmt.Errorf("the input data not string json format")
	}
	if err := json.Unmarshal([]byte(res), &dst); err != nil {
		return nil, err
	}
	return dst, nil
}

// ConvertJsonToMap 将json字符串转换为map[string]string
func ConvertJsonToMap(res string) (map[string]string, error) {
	var dst map[string]string
	if err := json.Unmarshal([]byte(res), &dst); err != nil {
		return nil, err
	}
	return dst, nil
}

// WrapperShell :If target host already set env like DATADISK=/data in the clusterfile,
// This function will WrapperShell cmd like:
// Input shell: cat /etc/hosts
// Output shell: DATADISK=/data cat /etc/hosts
// it is convenient for user to get env in scripts
// The scope of env comes from cluster.spec.env and host.env
func WrapperShell(shell string, wrapperData map[string]string) string {
	env := getEnvFromData(wrapperData)

	if len(env) == 0 {
		return shell
	}
	return fmt.Sprintf("%s %s", strings.Join(env, " "), shell)
}

func getEnvFromData(wrapperData map[string]string) []string {
	var env []string
	for k, v := range wrapperData {
		env = append(env, fmt.Sprintf("export %s='%s';", k, v))
	}
	sort.Strings(env)
	return env
}
