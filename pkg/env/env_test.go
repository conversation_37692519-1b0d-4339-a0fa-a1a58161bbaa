package env

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_processor_WrapperShell(t *testing.T) {
	type args struct {
		wrapperData map[string]string
		shell       string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			"test WrapperShell ",
			args{
				wrapperData: map[string]string{
					"foo": "bar",
					"IP":  "127.0.0.1",
				},
				shell: "hostname",
			},
			"export IP=\"127.0.0.1\"; export foo=\"bar\"; hostname",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := WrapperShell(tt.args.shell, tt.args.wrapperData); got != tt.want {
				t.Errorf("WrapperShell() = %v, want %v", got, tt.want)
			}
		})
	}
}

type args struct {
	PodCIDR string
}

func Test_processor_RenderAll(t *testing.T) {
	type args struct {
		renderData map[string]string
		dir        string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			"test render dir",
			args{
				renderData: map[string]string{
					"PodCIDR": "**********/10",
					"SvcCIDR": "*********/16",
				},
				dir: "test/template",
			},
			false,
		},
		{
			name: "test kube-nvs file",
			args: args{
				renderData: map[string]string{
					"nvsconfig": `[{"ip":"*********","netcard":"enp1s0"},{"ip":"*********","netcard":"lo"}]`,
				},
				dir: "test/template",
			},
			wantErr: false,
		},
		{
			name: "test render default env",
			args: args{
				renderData: map[string]string{
					"flannel": `{"podCidr":"*******/21"}`,
				},
				dir: "test/template",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := RenderTemplate(tt.args.dir, tt.args.renderData); (err != nil) != tt.wantErr {
				t.Errorf("RenderAll() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestParseJson(t *testing.T) {
	// input := "outer.inner1=\"1\",outer.inner3=3,outer.inner4=true,outer.inner5=\"true\""
	input := `nvsconfig=[{"a":"1","b":2,"c":[1,2,3]},{"a":"2","b":3,"c":[4,5,6]}]`
	res := make(map[string]interface{})

	err := ParseJSON(input, res)
	assert.Nil(t, err)
	fmt.Println(res)
	assert.NotNil(t, res)
}

func TestConvertJson(t *testing.T) {
	jsonStr := `[
		{
			"hostManageIP":  "*************",
			"hostManageNic": "enp1s0",
			"innerNicIP":    "***********",
			"innerNic":      "enp2s0",
			"uplinkNic":     "enp10s0",
			"uplinkIP":      "**********",
			"role":          "master"
		},
		{
			"hostManageIP":  "*************",
			"hostManageNic": "enp1s1",
			"innerNicIP":    "***********",
			"innerNic":      "enp2s0",
			"uplinkNic":     "enp10s0",
			"uplinkIP":      "**********",
			"role":          "master"
		}
	]`

	dst := []map[string]interface{}{
		{
			"hostManageIP":  "*************",
			"hostManageNic": "enp1s0",
			"innerNicIP":    "***********",
			"innerNic":      "enp2s0",
			"uplinkNic":     "enp10s0",
			"uplinkIP":      "**********",
			"role":          "master",
		},
		{
			"hostManageIP":  "*************",
			"hostManageNic": "enp1s1",
			"innerNicIP":    "***********",
			"innerNic":      "enp2s0",
			"uplinkNic":     "enp10s0",
			"uplinkIP":      "**********",
			"role":          "master",
		},
	}

	m, err := ConvertJsonStrToMap(jsonStr)
	assert.Nil(t, err)
	assert.Equal(t, dst, m)
	fmt.Println(m)
}

func TestConvertMap(t *testing.T) {
	jsonStr := `
		{
			"hostManageIP":  "*************",
			"hostManageNic": "enp1s0",
			"innerNicIP":    "***********",
			"innerNic":      "enp2s0",
			"uplinkNic":     "enp10s0",
			"uplinkIP":      "**********",
			"role":          "master"
		}
`

	dst := map[string]string{
		"hostManageIP":  "*************",
		"hostManageNic": "enp1s0",
		"innerNicIP":    "***********",
		"innerNic":      "enp2s0",
		"uplinkNic":     "enp10s0",
		"uplinkIP":      "**********",
		"role":          "master",
	}

	m, err := ConvertJsonToMap(jsonStr)
	assert.Nil(t, err)
	assert.Equal(t, dst, m)
	fmt.Println(m)
}
