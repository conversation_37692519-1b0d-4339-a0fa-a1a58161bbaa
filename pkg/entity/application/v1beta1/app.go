package v1beta1

import (
	"fmt"
	"path/filepath"
	"strings"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application/version"
)

type Application struct {
	NameVar    string   `json:"name"`
	TypeVar    string   `json:"type,omitempty"`
	FilesVar   []string `json:"files,omitempty"`
	VersionVar string   `json:"version,omitempty"`

	// AppEnv is a set of key value pair.
	// it is app level, only this app will be aware of its existence,
	// it is used to render app files, or as an environment variable for app startup and deletion commands
	AppEnv map[string]string `json:"env,omitempty"`

	// AppCMDs defined from `appcmds` instruction
	AppCMDs []string `json:"cmds,omitempty"`
}

func (app *Application) SetEnv(appEnv map[string]string) {
	app.AppEnv = appEnv
}

func (app *Application) SetCmds(appCmds []string) {
	app.AppCMDs = appCmds
}

func (app *Application) Version() string {
	return app.VersionVar
}

func (app *Application) Name() string {
	return app.NameVar
}

func (app *Application) Type() string {
	return app.TypeVar
}

func (app *Application) Files() []string {
	return app.FilesVar
}

// GetAppLaunchCmd : Get the real app launch cmds values in the following order.
// 1. appcmds instructionx defined in kubefile.
// 2. generated default command based on app type
func GetAppLaunchCmd(appRoot string, app *Application) string {
	if len(app.AppCMDs) != 0 {
		var cmds []string
		cmds = append(cmds, []string{"cd", appRoot}...)
		cmds = append(cmds, "&&")
		cmds = append(cmds, app.AppCMDs...)
		return strings.Join(cmds, " ")
	}
	switch app.Type() {
	case application.KubeApp:
		var cmds []string
		for _, file := range app.FilesVar {
			cmds = append(cmds, fmt.Sprintf("kubectl apply -f %s", filepath.Join(appRoot, file)))
		}
		return strings.Join(cmds, " && ")
	case application.HelmApp:
		return fmt.Sprintf("helm install %s %s", app.Name(), appRoot)
	case application.ShellApp:
		var cmds []string
		for _, file := range app.FilesVar {
			cmds = append(cmds, fmt.Sprintf("bash %s", filepath.Join(appRoot, file)))
		}
		return strings.Join(cmds, " && ")
	default:
		return ""
	}
}

func NewBetaApplication(
	name string,
	appType string, files []string) version.Interface {
	return &Application{
		NameVar:    name,
		TypeVar:    appType,
		FilesVar:   files,
		VersionVar: "v1beta1",
	}
}
