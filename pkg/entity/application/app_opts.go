package application

import (
	"fmt"

	appv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/application/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	imageutils "gitlab.bingosoft.net/bingokube/kubepilot/pkg/image/util"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/strs"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

const (
	KubeApp        string = "kube"
	HelmApp        string = "helm"
	ShellApp       string = "shell"
	UnknownApp     string = "unknown"
	AppRootRelPath string = "application/apps/"
)

// InitApplication set flags to v1beta1.Application
func InitApplication(app *appv1beta1.Application, cmds, appNames, globalEnvs []string, image string) *appv1beta1.Application {
	var newApp *appv1beta1.Application
	componentName, _, _ := imageutils.ParseComponentFromImageName(image)
	if app != nil {
		newApp = app
	} else {
		newApp = &appv1beta1.Application{
			TypeMeta:   metav1.TypeMeta{APIVersion: appv1beta1.SchemeGroupVersion.String(), Kind: constants.ApplicationKind},
			ObjectMeta: metav1.ObjectMeta{UID: types.UID(componentName), Name: image}, Spec: appv1beta1.ApplicationSpec{},
		}
	}

	if len(cmds) > 0 {
		newApp.Spec.Cmds = cmds
	}

	if appNames != nil {
		newApp.Spec.LaunchApps = appNames
	}

	// add appEnvs from flag to application object.
	if len(globalEnvs) > 0 {
		// 一个集群镜像部署多实例,从环境变量中获取到实例名称和命名空间,并添加到Application
		envs := strs.ConvertStringSliceToMap(globalEnvs)
		if len(envs[constants.InstanceName]) > 0 && len(envs[constants.InstanceNameSpace]) > 0 {
			newApp.GenerateName = envs[constants.InstanceName]
			newApp.Namespace = envs[constants.InstanceNameSpace]
			newApp.UID = types.UID(fmt.Sprintf("%s-%s-%s", newApp.UID, newApp.GenerateName, newApp.Namespace))
		}
		newApp.Spec.Configs = []appv1beta1.ApplicationConfig{{Name: componentName, Env: strs.RemoveDuplicate(globalEnvs)}}
	}
	return newApp
}
