package commands

import (
	"path/filepath"

	"github.com/spf13/pflag"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/certs"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"k8s.io/klog/v2"
)

type RunFlags struct {
	// Masters master 节点
	Masters string
	// Nodes work节点
	Nodes string
	// User 节点的用户名
	User string
	// Password 节点密码
	Password string
	// Port 节点默认端口
	Port uint16
	// Pk Private key
	Pk string
	// PkPassword 从Private key渲染出来的密码
	PkPassword string
	// CustomEnv 自定义的环境变量
	CustomEnv []string
	// Mode 模式
	Mode string
	// ClusterFile cluster文件
	ClusterFile string
	// ClusterName 自定义的集群名称
	ClusterName string
	// Cmds 覆盖集群镜像中默认的cmd命令
	Cmds []string
	// AppNames 覆盖集群镜像中默认的App Names
	AppNames []string
	// IgnoreCache kubepilot是否使用缓存镜像，如果不是则强制同步cluster rootfs
	// 默认false
	IgnoreCache bool
	// DistributeMode 分发文件的模式(sftp,p2p)，默认是sftp
	DistributeMode string
	// Components 业务组件
	Components []string
	// Values 渲染clusterfile的values配置文件
	Values []string
}

func (r *RunFlags) InitRunCmdFlags(fs *pflag.FlagSet) {
	fs.StringVarP(&r.Masters, "masters", "m", "", "设置master节点信息,格式可以是数组或者IP范围")
	fs.StringVarP(&r.Nodes, "nodes", "n", "", "设置worker节点信息,格式可以是数组或者IP范围")
	fs.StringVarP(&r.User, "user", "u", "root", "填写服务器的用户名,默认建议root")
	fs.StringVarP(&r.Password, "passwd", "p", "", "填写服务器用户对应的密码")
	fs.Uint16Var(&r.Port, "port", 22, "服务器ssh的端口")
	fs.StringVar(&r.Pk, "pk", filepath.Join(file.GetHomeDir(), ".ssh", "id_rsa"), "服务器的密钥信息")
	fs.StringVar(&r.PkPassword, "pk-passwd", "", "服务器的私钥密码")
	fs.StringSliceVar(&r.Cmds, "cmds", []string{}, "该信息会覆盖集群镜像中的cmd命令")
	fs.StringSliceVarP(&r.Components, "components", "c", []string{}, "需要安装的业务组件的集群镜像(会按照输入顺序执行)")
	fs.StringSliceVar(&r.AppNames, "apps", nil, "该信息会覆盖集群镜像中的App名")
	fs.StringArrayVarP(&r.CustomEnv, "env", "e", []string{}, "设置自定义的环境变量 (`key1=value1,ipvsVIPv4=*************`)")
	fs.StringVarP(&r.ClusterFile, "Clusterfile", "f", "", "填写Clusterfile文件的地址信息")
	fs.StringVar(&r.ClusterName, "name", "kube-cluster", "自定义k8s集群名字")
	fs.StringVar(&r.Mode, "mode", string(constants.InstallImage), "处理集群镜像的方式--仅加载或者安装")
	fs.BoolVar(&r.IgnoreCache, "ignore-cache", true, "分发集群镜像的时候是否使用集群镜像的缓存数据")
	fs.StringVarP(&r.DistributeMode, "distribute", "d", "sftp", "镜像分发模式 (sftp,p2p)")
	fs.StringSliceVar(&r.Values, "values", []string{}, "指定Clusterfile的values配置文件，会根据values配置文件渲染clusterfile")
}

func (r *RunFlags) Validate() error {
	// 如果没有指定master节点且没有指定clusterfile，则使用本地IP作为默认master节点
	if len(r.Masters) == 0 && len(r.ClusterFile) == 0 {
		// 未填写master的IP地址，则以当前执行环境的服务器IP为master
		defaultIP, err := unet.GetLocalDefaultIP()
		if err != nil {
			return err
		}
		r.Masters = defaultIP
		klog.Warningf("Input has no master, the current node [%s] is used as the default master", defaultIP)
	}
	return unet.ValidateNodes(r.Masters, r.Nodes)
}

type ApplyFlags struct {
	Masters     string
	Nodes       string
	User        string
	Password    string
	Port        uint16
	Pk          string
	PkPassword  string
	ClusterFile string
	Mode        string
	CustomEnv   []string
	ForceDelete bool
	IgnoreCache bool
}

// InitApplyCmdFlags
// Deprecated
func (a *ApplyFlags) InitApplyCmdFlags(fs *pflag.FlagSet) {
	fs.BoolVar(&a.ForceDelete, "force", false, "是否强制重置指定的集群或者节点")
	fs.StringVarP(&a.ClusterFile, "Clusterfile", "f", "", "输入Clusterfile文件")
	fs.StringVarP(&a.Mode, "applyMode", "m", string(constants.InstallImage), "处理集群镜像的方式--仅加载或者安装")
	fs.StringSliceVarP(&a.CustomEnv, "env", "e", []string{}, "设置自定义的环境变量")
	// support merge clusterfile and flags, such as host ip and host auth info.
	fs.StringVar(&a.Masters, "masters", "", "设置master节点")
	fs.StringVar(&a.Nodes, "nodes", "", "设置worker节点")
	fs.StringVarP(&a.User, "user", "u", "root", "服务器用户")
	fs.StringVarP(&a.Password, "passwd", "p", "", "设置服务器用户密码(默认是填写root用户对应的密码)")
	fs.Uint16Var(&a.Port, "port", 22, "服务器ssh所用的端口 (默认使用: 22)")
	fs.StringVar(&a.Pk, "pk", filepath.Join(file.GetHomeDir(), ".ssh", "id_rsa"), "服务器的私钥")
	fs.StringVar(&a.PkPassword, "pk-passwd", "", "服务器私钥密码")
	fs.BoolVar(&a.IgnoreCache, "ignore-cache", false, "分发集群镜像的时候是否忽略缓存,若为false,则会使用rootfs旧的渲染文件(默认为:false)")
}

type JoinFlags struct {
	Masters     string
	Nodes       string
	User        string
	Password    string
	Port        uint16
	Pk          string
	PkPassword  string
	CustomEnv   []string
	IgnoreCache bool
}

// InitJoinCmdFlags init join cmd flags
func (j *JoinFlags) InitJoinCmdFlags(fs *pflag.FlagSet) {
	fs.StringVarP(&j.User, "user", "u", "root", "服务器用户")
	fs.StringVarP(&j.Password, "passwd", "p", "", "服务器密码 (默认是输入root对应的密码)")
	fs.Uint16Var(&j.Port, "port", 22, "服务器ssh所用的端口")
	fs.StringVar(&j.Pk, "pk", filepath.Join(file.GetHomeDir(), ".ssh", "id_rsa"), "服务器的私钥")
	fs.StringVar(&j.PkPassword, "pk-passwd", "", "服务器私钥密码")
	fs.StringArrayVarP(&j.CustomEnv, "env", "e", []string{}, "set custom environment variables")
	fs.StringVarP(&j.Masters, "masters", "m", "", "设置master节点 (支持ip数组/ip网段)")
	fs.StringVarP(&j.Nodes, "nodes", "n", "", "设置worker节点 (支持ip数组/ip网段)")
	fs.BoolVar(&j.IgnoreCache, "ignore-cache", true, "分发集群镜像的时候是否使用集群镜像的缓存数据")
}

type ResetFlags struct {
	Masters     string
	Nodes       string
	CustomEnv   []string
	ClusterFile string // 请注意 `Clusterfile`首字母是大写的，如`Kubefile`也是，主要是与k8s resource的格式对齐的同时，还能防止与宿主机相关文件冲突
	ResetAll    bool
	ForceDelete bool
	Prune       bool // 主要是用来确认是否清除集群的rootfs文件，若重置集群过程中有冲突，则建议清除,Prune来自docker/buildah的专业标识，表示清除不使用的镜像或者文件
	IgnoreError bool
	SkipPhases  []string // 跳过特定阶段，例如：container-runtime
}

func (r *ResetFlags) InitResetCmdFlags(fs *pflag.FlagSet) {
	fs.StringVarP(&r.Masters, "masters", "m", "", "需要reset的master节点")
	fs.StringVarP(&r.Nodes, "nodes", "n", "", "需要reset的worker节点")
	fs.StringVarP(&r.ClusterFile, "Clusterfile", "f", "", "通过Clusterfile对集群进行reset")
	fs.StringSliceVarP(&r.CustomEnv, "env", "e", []string{}, "自定义的环境变量")
	fs.BoolVar(&r.ForceDelete, "force", false, "是否强制reset集群 (默认是false)")
	fs.BoolVarP(&r.ResetAll, "all", "a", false, "是否重置整个集群 (默认是false)")
	fs.BoolVar(&r.Prune, "prune", false, "是否清空该集群的rootfs文件: 一般不清除,可以加快重装集群的速度,若是升级的reset则建议置为true (默认是false)")
	fs.BoolVar(&r.IgnoreError, "ignore-error", false, "是否忽略错误继续执行: 当节点无法连接时仍然继续执行删除操作 (默认是false)")
	fs.StringSliceVar(&r.SkipPhases, "skip-phases", []string{}, "跳过特定阶段，例如：container-runtime")
}

type MergeFlags struct {
	Masters    string
	Nodes      string
	Components []string
	User       string
	Password   string
	Port       uint16
	Pk         string
	PkPassword string
	CustomEnv  []string
	Cmds       []string
	AppNames   []string
}

type UninstallFlags struct {
	Components []string
	// CustomEnv 自定义的环境变量
	CustomEnv []string
}

func (r *UninstallFlags) InitUninstallCmdFlags(fs *pflag.FlagSet) {
	fs.StringSliceVarP(&r.Components, "components", "c", []string{}, "需要安装的业务组件的集群镜像(会按照输入顺序执行)")
	fs.StringArrayVarP(&r.CustomEnv, "env", "e", []string{}, "设置自定义的环境变量 (`key1=value1,storageClassName=ebs`)")
}

type UpgradeFlags struct {
	// CustomEnv 自定义的环境变量
	CustomEnv []string
	// DistributeMode 分发文件的模式(sftp,p2p)，默认是sftp
	DistributeMode string
	// IgnoreCache kubepilot是否使用缓存镜像，如果不是则强制同步cluster rootfs
	// 默认false
	IgnoreCache bool
	// Mode 模式 仅加载或者安装,默认安装
	Mode       string
	Components []string
	// 如果组件新旧版本一致,默认仅更新组件helm参数(仅分发etc目录)，若force参数为ture，则强制升级，则强制分发所有目录文件
	Force bool // 强制更新
}

func (r *UpgradeFlags) InitUpgradeCmdFlags(fs *pflag.FlagSet) {
	fs.StringSliceVarP(&r.Components, "components", "c", []string{}, "需要安装的业务组件的集群镜像(会按照输入顺序执行)")
	fs.StringArrayVarP(&r.CustomEnv, "env", "e", []string{}, "设置自定义的环境变量 (`key1=value1,storageClassName=ebs`)")
	fs.StringVarP(&r.Mode, "mode", "m", string(constants.InstallImage), "处理集群镜像的方式--仅加载或者安装")
	fs.BoolVar(&r.IgnoreCache, "ignore-cache", true, "分发集群镜像的时候是否使用集群镜像的缓存数据")
	fs.StringVarP(&r.DistributeMode, "distribute", "d", "sftp", "镜像分发模式 (sftp,p2p)")
	fs.BoolVar(&r.Force, "force", false, "如果组件新旧版本一致,默认仅更新组件参数，若force参数为ture，则强制升级")
}

type RollbackFlags struct {
	AppNames    []string
	IgnoreCache bool
}

// DistributionMode 镜像文件分发方式
type DistributionMode string

const (
	SFTPDistribution DistributionMode = "sftp"
	P2PDistribution  DistributionMode = "p2p"
)

type CertsFlags struct {
	AltNames []string // Alternative Name
	Wait     bool
}

func (c *CertsFlags) InitCertsFlags(fs *pflag.FlagSet) {
	fs.StringSliceVar(&c.AltNames, "alt-names", []string{}, "在certs证书中添加IP或者DNS domain,若证书中已存在该值则不做变更")
	fs.BoolVar(&c.Wait, "wait", true, "是否等待kube-apiserver重启成功")
}

type CertGenFlags struct {
	AltNames     []string
	NodeName     string
	ServiceCIDR  string
	NodeIP       string
	DNSDomain    string
	CertPath     string
	CertEtcdPath string
}

// InitCertGenFlags init certs generate cmd flags
func (c *CertGenFlags) InitCertGenFlags(fs *pflag.FlagSet) {
	fs.StringSliceVar(&c.AltNames, "alt-names", []string{}, "需要生成证书的域名或者IP地址")
	fs.StringVar(&c.NodeName, "node-name", "", "节点的hostname,例如: master0")
	fs.StringVar(&c.ServiceCIDR, "service-cidr", "", "填写service CIDR,例如:***********/24")
	fs.StringVar(&c.NodeIP, "node-ip", "", "节点的IP地址,例如: ***********")
	fs.StringVar(&c.DNSDomain, "dns-domain", "cluster.local", "集群的DNS域")
	fs.StringVar(&c.CertPath, "cert-path", certs.KubeDefaultCertPath, "集群默认的的证书文件目录")
	fs.StringVar(&c.CertEtcdPath, "cert-etcd-path", certs.KubeDefaultCertEtcdPath, "集群默认的etcd证书文件目录")
}

type CertUpdateFlags struct {
	AltNames []string
}

// InitCertUpdateFlags init certs update cmd flags
func (u *CertUpdateFlags) InitCertUpdateFlags(fs *pflag.FlagSet) {
	fs.StringSliceVar(&u.AltNames, "alt-names", []string{}, "需要在现有证书中添加的域名或者IP地址，若证书中已有该值，则证书不变")
}

type RouteFlags struct {
	Host    string
	Gateway string
}

type ClusterfileGenFlags struct {
	Masters     string
	Nodes       string
	Cmds        []string
	Env         []string
	User        string
	Password    string
	Pk          string
	PkPassword  string
	Port        uint16
	Output      string // 文件内容的输出方式
	ClusterName string
	Components  []string
}

// InitGenFlags 初始化generator Clusterfile
func (c *ClusterfileGenFlags) InitGenFlags(fs *pflag.FlagSet) {
	fs.StringVarP(&c.Masters, "masters", "m", "", "设置master节点信息,格式可以是数组或者IP范围")
	fs.StringVarP(&c.Nodes, "nodes", "n", "", "设置worker节点信息,格式可以是数组或者IP范围")
	fs.StringVarP(&c.User, "user", "u", "root", "填写服务器的用户名,默认建议root")
	fs.StringVarP(&c.Password, "passwd", "p", "", "填写服务器用户对应的密码")
	fs.StringVarP(&c.Output, "output", "o", "", "打印或输出到指定文件")
	fs.StringSliceVarP(&c.Components, "components", "c", []string{}, "需要安装的业务组件的集群镜像(会按照输入顺序执行)")
	fs.Uint16Var(&c.Port, "port", 22, "服务器ssh的端口")
	fs.StringVar(&c.Pk, "pk", filepath.Join(file.GetHomeDir(), ".ssh", "id_rsa"), "服务器的密钥信息")
	fs.StringVar(&c.PkPassword, "pk-passwd", "", "服务器的私钥密码")
	fs.StringVar(&c.ClusterName, "name", "kube-cluster", "自定义k8s集群名字")
	fs.StringSliceVar(&c.Cmds, "cmds", []string{}, "该信息会覆盖集群镜像中的cmd命令")
	fs.StringSliceVarP(&c.Env, "env", "e", []string{}, "设置自定义的环境变量 (key1=value1)")
}

func (c *ClusterfileGenFlags) Convert() *RunFlags {
	return &RunFlags{
		Masters:        c.Masters,
		Nodes:          c.Nodes,
		User:           c.User,
		Password:       c.Password,
		Port:           c.Port,
		Pk:             c.Pk,
		PkPassword:     c.PkPassword,
		CustomEnv:      c.Env,
		Mode:           string(constants.InstallImage),
		ClusterName:    c.ClusterName,
		Cmds:           c.Cmds,
		AppNames:       []string{},
		IgnoreCache:    false,
		DistributeMode: "sftp",
		Components:     c.Components,
	}
}
