package registry

import (
	"runtime"

	"github.com/containers/image/v5/types"
	"github.com/spf13/pflag"
)

type RegistryServeOpts struct {
	Port           int
	DisableLogging bool
	LogLevel       string
	PidFile        string
}

func (r *RegistryServeOpts) InitRegistryServeFlags(fs *pflag.FlagSet) {
	fs.IntVarP(&r.Port, "port", "p", 0, "监听端口，默认是随机未使用的端口")
	fs.BoolVar(&r.DisableLogging, "disable-logging", false, "禁用日志输出")
	fs.StringVar(&r.LogLevel, "log-level", "error", "配置日志级别")
	fs.StringVar(&r.PidFile, "pid-file", "", "将进程 ID 写入文件")
}

type RegistrySaveOpts struct {
	LocalDir     string
	Arch         string
	MaxPullProcs int
	File         string
	Images       []string
}

func (opts *RegistrySaveOpts) InitRegistrySaveFlags(fs *pflag.FlagSet) {
	fs.SetInterspersed(false)
	fs.StringVar(&opts.Arch, "arch", runtime.GOARCH, "拉取镜像的架构")
	fs.StringVar(&opts.LocalDir, "local-dir", "registry", "镜像数据目录路径")
	fs.StringVar(&opts.File, "file", "imageList", "镜像列表文件")
	fs.StringSliceVar(&opts.Images, "images", []string{}, "镜像列表")
	fs.IntVar(&opts.MaxPullProcs, "max-pull-procs", 10, "拉取镜像的最大协程数")
}

type RegistrySyncOpts struct {
	OverrideArch    string
	OverrideOS      string
	OverrideVariant string
	All             bool
}

func (opts *RegistrySyncOpts) NewSystemContext() *types.SystemContext {
	ctx := &types.SystemContext{
		ArchitectureChoice:          opts.OverrideArch,
		OSChoice:                    opts.OverrideOS,
		VariantChoice:               opts.OverrideVariant,
		DockerInsecureSkipTLSVerify: types.OptionalBoolTrue,
	}
	return ctx
}

func (opts *RegistrySyncOpts) RegistrySyncFlags(fs *pflag.FlagSet) {
	fs.StringVar(&opts.OverrideArch, "override-arch", "arm64", "使用 `ARCH` 代替机器的架构来选择镜像")
	fs.StringVar(&opts.OverrideOS, "override-os", "linux", "使用 `OS` 代替运行的操作系统来选择镜像")
	fs.StringVar(&opts.OverrideVariant, "override-variant", "", "使用 `VARIANT` 代替运行的架构变体来选择镜像")
	//fs.BoolVarP(&opts.All, "All", "a", false, "如果设置，则同步所有列表中的镜像")
}
