package v1beta1

import (
	"encoding/json"

	ociv1 "github.com/opencontainers/image-spec/specs-go/v1"
	apiv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/image/v1beta1"
	entv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/application/version"
)

const (
	KubepilotImageExtension          = "kubepilot.image.extension"
	KubepilotImageContainerImageList = "kubepilot.image.container.images"
	ClusterType                      = "k8s"
	AppType                          = "app"
	ImageSpecSchemaVersionV1Beta1    = "v1beta1"
)

// ValidImgType 校验镜像类型
func ValidImgType(imgType string) bool {
	LegalImgType := map[string]struct{}{
		ClusterType: {},
		AppType:     {},
	}
	_, ok := LegalImgType[imgType]

	return ok
}

type ImageSpec struct {
	// ID the image id
	ID string `json:"id"`

	// Name the image name
	Name string `json:"name"`

	// Digest the image digest
	Digest string `json:"digest"`

	// ManifestV1 OCI version v1 image manifest
	ManifestV1 ociv1.Manifest `json:"manifestv1"`

	// OCIv1 OCI version v1 image spec
	OCIv1 ociv1.Image `json:"ociv1"`

	ImageExtension ImageExtension `json:"imageExtension"`

	// ContainerImageList the container image list contained in the cluster image
	//
	// TODO: populate this value during the build phase
	ContainerImageList []*ContainerImage `json:"containerImageList,omitempty"`
}

// IsRootfs 判断是否为k8s集群镜像
func (s *ImageSpec) IsRootfs() bool {
	return s.ImageExtension.Type == ClusterType
}

// IsApplication 判断是否为业务组件集群镜像
func (s *ImageSpec) IsApplication() bool {
	return s.ImageExtension.Type == AppType
}

type ContainerImage struct {
	// Image the container image name
	Image string `json:"image" yaml:"image"`

	// AppName the mame of the app to which the container image belongs
	//
	// NOTE: A container image may not belong to any app. In this case, the appName value is null.
	AppName string `json:"appName,omitempty" yaml:"appName"`

	// Platform the container image platform
	Platform *apiv1beta1.Platform `json:"platform" yaml:"platform"`

	// TODO: add more info about container image if necessary such as resourceKind, resourceName, etc.
}

func GetImageSliceFromContainerImageList(containerImageList []*ContainerImage) []string {
	var images []string
	for _, containerImage := range containerImageList {
		images = append(images, containerImage.Image)
	}
	return images
}

// ImageExtension NOTE: the UnmarshalJSON function of ImageExtension has been overridden
type ImageExtension struct {
	// BuildClient build client info
	BuildClient BuildClient `json:"buildClient"`

	// image spec schema version
	SchemaVersion string `json:"schemaVersion"`

	// cluster image type, like AppImage
	Type string `json:"type,omitempty"`

	// applications in the cluster image
	Applications []version.Interface `json:"applications,omitempty"`

	// Labels are metadata to the cluster image
	Labels map[string]string `json:"labels,omitempty" yaml:"labels,omitempty"`

	// Env is a set of key value pair.
	// set to cluster image some default parameters which is in global level.
	// user could overwrite it through v1.ClusterSpec at run stage.
	Env map[string]string `json:"env,omitempty"`

	// launch spec will declare
	Launch Launch `json:"launch,omitempty"`
}

type BuildClient struct {
	KubepilotVersion string `json:"kubepilotVersion"`

	BuildahVersion string `json:"buildahVersion"`
}

type Launch struct {
	Cmds []string `json:"cmds,omitempty"`

	// user specified LAUNCH instruction
	AppNames []string `json:"app_names,omitempty"`
}

// TransitionalImageExtension the image extension is transitional
type TransitionalImageExtension struct {
	// BuildClient build client info
	BuildClient BuildClient `json:"buildClient"`
	// image spec schema version
	SchemaVersion string `json:"schemaVersion"`
	// cluster image type, like AppImage
	Type string `json:"type,omitempty"`
	// Labels are metadata to the cluster image
	Labels map[string]string `json:"labels,omitempty" yaml:"labels,omitempty"`
	// applications in the cluster image
	Applications []entv1beta1.Application `json:"applications,omitempty"`
	// launch spec will declare
	Launch Launch `json:"launch,omitempty"`
	// Env global env
	Env map[string]string `json:"env,omitempty"`
}

func (ie *ImageExtension) UnmarshalJSON(data []byte) error {
	*ie = ImageExtension{}
	tie := TransitionalImageExtension{}
	if err := json.Unmarshal(data, &tie); err != nil {
		return err
	}

	(*ie).BuildClient = tie.BuildClient
	(*ie).SchemaVersion = tie.SchemaVersion
	(*ie).Labels = tie.Labels
	(*ie).Env = tie.Env
	(*ie).Type = tie.Type
	(*ie).Applications = make([]version.Interface, len(tie.Applications))
	for i, app := range tie.Applications {
		tmpApp := app
		(*ie).Applications[i] = &tmpApp
	}
	(*ie).Launch = tie.Launch
	return nil
}
