package image

import (
	"fmt"

	"github.com/containers/buildah/pkg/parse"
	"github.com/spf13/pflag"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	entv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/entity/image/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/version"
	"k8s.io/klog/v2"
)

const (
	WithLiteMode = "lite"
	WithAllMode  = "all"
)

// SupportedBuildModes 暂时支持的构建方式
var SupportedBuildModes = []string{
	WithLiteMode,
	WithAllMode,
}

// validBuildMode 校验构建模式
func validBuildMode(mode string) bool {
	validMode := map[string]struct{}{
		WithAllMode:  {},
		WithLiteMode: {},
	}
	_, ok := validMode[mode]

	return ok
}

// BuildOpts should be out of buildah scope.
type BuildOpts struct {
	Kubefile          string
	DockerFilePath    string
	ContextDir        string
	PullPolicy        string
	ImageType         string
	Tag               string
	BuildArgs         []string
	ScriptsEnv        []string // 执行init.sh脚本需要传参时用到
	Platforms         []string
	Labels            []string
	Annotations       []string
	NoCache           bool
	Base              bool
	ImageList         string
	ImageListWithAuth string
	IgnoredImageList  string
	BuildMode         string // BuildMode means whether to download container image during the build process,default value is download all container images.
}

func (b *BuildOpts) BuildFlags(fs *pflag.FlagSet) {
	fs.StringVarP(&b.Kubefile, "file", "f", "Kubefile", "kubefile的文件路径")
	fs.StringVarP(&b.Tag, "tag", "t", "", "集群镜像名称")
	fs.StringVar(&b.ImageList, "image-list", "manifests/imageList", "imageList文件的目录，如果设置，kubepilot将读取其内容并下载额外的docker镜像")
	fs.StringVar(&b.ImageListWithAuth, "image-list-with-auth", "", "imageListWithAuth的路径名。如果设置了yaml文件路径，kubepilot将读取其内容并将额外的docker镜像下载到rootfs(通常不使用)")
	fs.StringVar(&b.IgnoredImageList, "ignored-image-list", "manifests/ignoredImageList", "被忽略的镜像列表文件路径，如果设置，kubepilot将读取其内容并阻止下载相应的docker镜像")
	fs.StringVar(&b.PullPolicy, "pull", "ifnewer", "拉取策略:常见的有 --pull, --pull=true, --pull=false, --pull=never, --pull=always, --pull=ifnewer")
	fs.StringVar(&b.ImageType, "type", entv1beta1.AppType, fmt.Sprintf("指定集群镜像的类型,例如: --type=%s, --type=%s", entv1beta1.ClusterType, entv1beta1.AppType))
	fs.StringSliceVar(&b.Platforms, "platform", []string{parse.DefaultPlatform()}, "设置目标架构,例如: --platform=linux/amd64 or --platform=linux/amd64/v7. 多架构则: --platform=linux/amd64,linux/amd64/v7")
	fs.StringSliceVar(&b.BuildArgs, "args", []string{}, "设置自定义的构建参数")
	fs.StringSliceVarP(&b.ScriptsEnv, "env", "e", []string{}, "设置init.sh脚本运行的参数(用法: -e 'k1=v1,k2=v2')")
	fs.StringSliceVar(&b.Annotations, "annotation", []string{}, "为集群镜像添加注解.例如: --annotation key=[value]")
	fs.StringSliceVar(&b.Labels, "label", []string{setDefaultLabel()}, "为集群镜像添加标签.例如: --label key=[value]")
	fs.BoolVar(&b.NoCache, "no-cache", true, "是否使用现有的缓存图像进行构建,默认是使用一组新的缓存层从头构建")
	fs.StringVar(&b.BuildMode, "build-mode", WithAllMode, "是否在构建过程中下载镜像")
	if !validBuildMode(b.BuildMode) {
		klog.Fatalf("Build mode [%s] isn't supported", b.BuildMode)
	}
	if !entv1beta1.ValidImgType(b.ImageType) {
		klog.Fatalf("Image type [%s] isn't supported", b.ImageType)
	}
}

func setDefaultLabel() string {
	return "bingokube.bingosoft.net=" + version.Get().GitVersion
}

type FromOpts struct {
	Image string
	Quiet bool
}

type MountOpts struct {
	Containers []string
}

type JSONMount struct {
	Container  string `json:"container,omitempty"`
	MountPoint string `json:"mountPoint"`
}

type CopyOpts struct {
	AddHistory             bool
	Quiet                  bool
	IgnoreFile             string
	ContextDir             string
	Container              string
	SourcesRel2CxtDir      []string
	DestinationInContainer string
}

type ConfigOpts struct {
	ContainerID string
	Annotations []string
}

type CommitOpts struct {
	Format             string
	Manifest           string
	Timestamp          int64
	Quiet              bool
	Rm                 bool
	Squash             bool
	DisableCompression bool
	ContainerID        string
	Image              string
}

type LoginOpts struct {
	Domain        string
	Username      string
	Password      string
	SkipTLSVerify bool
}

func (l *LoginOpts) InitLoginFlags(fs *pflag.FlagSet) {
	fs.StringVarP(&l.Username, "username", "u", "", "登录registry的用户名")
	fs.StringVarP(&l.Password, "passwd", "p", "", "登录registry的密码")
	fs.BoolVar(&l.SkipTLSVerify, "skip-tls-verify", true, "访问registry时是否跳过HTTPS和验证证书")
}

type LogoutOpts struct {
	All    bool
	Domain string
}

type PushOpts struct {
	Authfile      string
	CertDir       string
	Format        string
	Image         string
	Destination   string
	Rm            bool
	Quiet         bool
	SkipTLSVerify bool
	All           bool
}

func (p *PushOpts) InitPushFlags(fs *pflag.FlagSet) {
	fs.StringVar(&p.Authfile, "authfile", file.DefaultAuthFilePath(), "登录后存储授权文件的路径,后面需要使用此授权访问registry")
	fs.BoolVar(&p.SkipTLSVerify, "skip-tls-verify", true, "访问registry时是否跳过HTTPS和验证证书")
	fs.BoolVarP(&p.Quiet, "quiet", "q", false, "推送镜像的时候是否展示进度信息 (默认false,不展示)")
	fs.BoolVar(&p.All, "all", true, "是否同时推送镜像列表中的镜像")
}

type PullOpts struct {
	CertDir       string
	Quiet         bool
	SkipTLSVerify bool
	PullPolicy    string
	Image         string
	Platform      string
}

// InitPullFlags 初始化pull image 命令行参数
func (p *PullOpts) InitPullFlags(fs *pflag.FlagSet) {
	fs.StringVar(&p.Platform, "platform", parse.DefaultPlatform(), fmt.Sprintf("选择你想推送的镜像架构 (默认是宿主机的架构-[%s]).", parse.DefaultPlatform()))
	fs.StringVar(&p.PullPolicy, "policy", "always", "镜像拉取策略,包括`always,missing,always,ifnewer,never`")
	fs.BoolVarP(&p.Quiet, "quiet", "q", false, "拉取镜像时候是否展示进度信息 (默认false,不展示)")
	fs.BoolVar(&p.SkipTLSVerify, "skip-tls-verify", true, "访问registry时是否跳过HTTPS和验证证书")
}

type ImagesOpts struct {
	All       bool
	Digests   bool
	NoHeading bool
	NoTrunc   bool
	Quiet     bool
	History   bool
	JSON      bool
}

func (i *ImagesOpts) InitImagesFlags(fs *pflag.FlagSet) {
	fs.BoolVarP(&i.All, "all", "a", false, "是否显示所有镜像,包括构建产生的中间镜像 (默认false)")
	fs.BoolVar(&i.Digests, "digests", false, "是否显示镜像摘要 (默认false)")
	fs.BoolVar(&i.JSON, "json", false, "是否使用JSON格式显示 (默认false)")
	fs.BoolVarP(&i.NoHeading, "noheading", "n", false, "是否打印栏目标题 (默认false)")
	fs.BoolVar(&i.NoTrunc, "no-trunc", false, "是否分段输出 (默认false)")
	fs.BoolVarP(&i.Quiet, "quiet", "q", false, "是否只显示镜像ID (默认false)")
	fs.BoolVarP(&i.History, "history", "", false, "是否显示镜像的历史名称(默认false)")
}

type SaveOpts struct {
	Compress          bool
	Format            string
	MultiImageArchive bool // don't support currently
	Output            string
	Quiet             bool
	ImageNameOrID     string
	TmpDir            string
}

func (s *SaveOpts) InitSaveFlags(fs *pflag.FlagSet) {
	fs.StringVar(&s.Format, "format", constants.OCIArchive, "保存镜像到oci-archive, oci-dir(带有oci清单类型的目录),docker-archive, docker-dir(带有v2s2清单类型的目录)")
	fs.StringVarP(&s.Output, "output", "o", "", "将镜像写到指定目录")
	fs.BoolVarP(&s.Quiet, "quiet", "q", false, "是否只显示镜像ID (默认false)")
	fs.StringVar(&s.TmpDir, "tmp-dir", "", "保存镜像的临时目录 (默认会使用系统的临时目录)")
	fs.BoolVar(&s.Compress, "compress", false, "压缩镜像层时，使用dir传输方式保存到一个目录 (默认是与源相同的压缩类型)")
}

type LoadOpts struct {
	Input  string
	TmpDir string
	Quiet  bool
}

func (l *LoadOpts) InitLoadFlags(fs *pflag.FlagSet) {
	fs.StringVarP(&l.Input, "input", "i", "", "要加载到集群镜像registry的tar文件")
	fs.BoolVarP(&l.Quiet, "quiet", "q", false, "是否标准输出 (默认false)")
	fs.StringVar(&l.TmpDir, "tmp-dir", "", "保存镜像文件的临时目录 (默认会使用系统的临时目录)")
}

type InspectOpts struct {
	Format        string
	InspectType   string
	ImageNameOrID string
}

func (i *InspectOpts) InitInspectFlags(fs *pflag.FlagSet) {
	fs.StringVarP(&i.Format, "format", "f", "", "使用何种格式作为Go模板来格式化输出 (默认是标准输出stdOut)")
	fs.StringVarP(&i.InspectType, "type", "t", "image", "查看指定的名称和类型(container/image)")
}

type BuildRootfsOpts struct {
	ImageNameOrID string
	DestDir       string
}

type RemoveImageOpts struct {
	ImageNamesOrIDs []string
	Force           bool
	Prune           bool
}

func (r *RemoveImageOpts) InitRmiFlags(fs *pflag.FlagSet) {
	// prune 用来删除不再使用的镜像(具体定义查看https://www.runoob.com/note/43787)
	// docker查看悬空镜像: docker images --filter dangling=true
	fs.BoolVarP(&r.Prune, "prune", "p", false, "是否删除悬空镜像")
	fs.BoolVarP(&r.Force, "force", "f", false, "是否强制删除该镜像以及使用该镜像的容器")
}

type EngineGlobalConfig struct {
	AuthFile  string
	GraphRoot string
	RunRoot   string
}

type RemoveContainerOpts struct {
	ContainerNamesOrIDs []string
	All                 bool
}

type TagOpts struct {
	ImageNameOrID string
	Tags          []string
}

type ManifestCreateOpts struct {
}

type ManifestInspectOpts struct {
}

type ManifestDeleteOpts struct {
}

type ManifestAddOpts struct {
	Os          string
	Arch        string
	Variant     string
	OsVersion   string
	OsFeatures  []string
	Annotations []string
	All         bool
	TargetName  string
}

func (b *ManifestAddOpts) ManifestAddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&b.Os, "os", "", "覆盖指定集群镜像的OS")
	fs.StringVar(&b.Arch, "arch", "", "覆盖指定集群镜像的arch")
	fs.StringVar(&b.Variant, "variant", "", "覆盖指定集群镜像的variant")
	fs.StringVar(&b.OsVersion, "os-version", "", "覆盖指定集群镜像的OS版本")
	fs.StringSliceVar(&b.OsFeatures, "os-features", nil, "覆盖指定集群镜像的OS feature")
	fs.StringSliceVar(&b.Annotations, "annotation", nil, "为指定集群镜像设置annotation")
	fs.BoolVar(&b.All, "all", false, "是否添加列表的所有集群镜像")
	fs.StringVarP(&b.TargetName, "target", "t", "", "集群镜像的tag,若不存在则创建新的tag名称")
}

type ManifestRemoveOpts struct{}
