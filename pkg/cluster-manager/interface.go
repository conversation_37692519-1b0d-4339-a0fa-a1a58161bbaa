package clustermanager

import (
	"net"

	"sigs.k8s.io/controller-runtime/pkg/client"
)

type Installer interface {
	// Install exec init phase for cluster.
	Install() error
	// GetCurrentRuntimeDriver return current runtime driver info
	GetCurrentRuntimeDriver() (Driver, error)
	// Reset exec reset phase for cluster.TODO: make the annotation more comprehensive
	Reset() error
	// ScaleUp exec joining phase for cluster, add master role for these nodes. net.IP is the master node IP array.
	ScaleUp(newMasters, newWorkers []net.IP) error
	// ScaleDown exec deleting phase for deleting cluster master role nodes. net.IP is the master node IP array.
	ScaleDown(mastersToDelete, workersToDelete []net.IP) error
	// Upgrade exec upgrading phase for cluster.TODO: make the annotation more comprehensive
	Upgrade() error
}

type Driver interface {
	client.Client
	GetAdminKubeconfig() string
}
