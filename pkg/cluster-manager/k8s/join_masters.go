package k8s

import (
	"fmt"
	"net"

	clustermanager "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/kubeadm"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/shellcommand"
	"k8s.io/klog/v2"

	"k8s.io/kubernetes/cmd/kubeadm/app/apis/kubeadm/v1beta3"
)

func (k *Runtime) joinMasters(newMasters []net.IP, master0 net.IP, kubeadmConfig kubeadm.KubeadmConfig, token v1beta3.BootstrapTokenDiscovery, certKey string) error {
	if len(newMasters) == 0 {
		return nil
	}

	klog.V(5).Infof("%s will be added as master", newMasters)

	if err := k.initKube(newMasters); err != nil {
		return err
	}

	if err := k.copyStaticFiles(newMasters); err != nil {
		return err
	}

	if err := k.sendKubeConfigFilesToMaster(newMasters, AdminConf, ControllerConf, SchedulerConf); err != nil {
		return err
	}

	if err := k.sendKubeadmFile(newMasters); err != nil {
		return err
	}

	// TODO only needs send ca?
	if err := k.sendClusterCert(newMasters); err != nil {
		return err
	}

	// 为master生成kubealived.yaml静态pod文件
	for _, host := range newMasters {
		if err := k.prepareAndSendKubealivedStaticPodToNode(host); err != nil {
			return err
		}
	}

	// set master0 as APIServerEndpoint when join master
	ep := net.JoinHostPort(master0.String(), "6443")
	for _, m := range newMasters {
		klog.Infof("start to join %s as master", m)

		joinCmd, err := k.BuildKubeadmCmd(JoinMaster, k.getNodeNameOverride(m))
		if err != nil {
			return fmt.Errorf("failed to get join master command: %v", err)
		}

		hostname, err := k.infra.GetHostName(m)
		if err != nil {
			return err
		}

		if output, err := k.infra.CmdToString(m, nil, GetCustomizeCRISocket, ""); err == nil && output != "" {
			kubeadmConfig.JoinConfiguration.NodeRegistration.CRISocket = output
		}

		kubeadmConfig.JoinConfiguration.Discovery.BootstrapToken = &token
		kubeadmConfig.JoinConfiguration.Discovery.BootstrapToken.APIServerEndpoint = ep
		kubeadmConfig.JoinConfiguration.ControlPlane.LocalAPIEndpoint.AdvertiseAddress = m.String()
		kubeadmConfig.JoinConfiguration.ControlPlane.LocalAPIEndpoint.BindPort = int32(6443)
		kubeadmConfig.JoinConfiguration.ControlPlane.CertificateKey = certKey
		str, err := file.MarshalWithDelimiter(kubeadmConfig.JoinConfiguration, kubeadmConfig.KubeletConfiguration)
		if err != nil {
			return err
		}
		cmd := fmt.Sprintf("mkdir -p /etc/kubernetes && echo \"%s\" > %s", str, KubeadmFileYml)
		if err = k.infra.CmdAsync(m, nil, cmd); err != nil {
			return fmt.Errorf("failed to set join kubeadm config on host(%s) with cmd(%s): %v", m, cmd, err)
		}

		if err = k.infra.CmdAsync(m, nil, shellcommand.CommandSetHostAlias(k.getAPIServerDomain(), master0.String())); err != nil {
			return fmt.Errorf("failed to config cluster hosts file cmd: %v", err)
		}

		certCMD := clustermanager.RemoteCertCmd(kubeadmConfig.GetCertSANS(), m, hostname, kubeadmConfig.GetSvcCIDR(), "")
		if err = k.infra.CmdAsync(m, nil, certCMD); err != nil {
			return fmt.Errorf("failed to exec command(%s) on master(%s): %v", certCMD, m, err)
		}

		if err = k.infra.CmdAsync(m, nil, joinCmd); err != nil {
			return fmt.Errorf("failed to exec command(%s) on master(%s): %v", joinCmd, m, err)
		}
		// 为节点`/etc/hosts`设置域名解析 例如: apiserver.cluster.local *************
		// if err = k.infra.CmdAsync(m, nil, shellcommand.CommandSetHostAlias(k.getAPIServerDomain(), m.String())); err != nil {
		if err = k.infra.CmdAsync(m, nil, shellcommand.CommandSetHostAlias(k.getAPIServerDomain(), k.getAPIServerVIP().String())); err != nil {
			return fmt.Errorf("failed to config cluster hosts file cmd: %v", err)
		}

		if err = k.infra.CmdAsync(m, nil, "rm -rf .kube/config && mkdir -p /root/.kube && cp /etc/kubernetes/admin.conf /root/.kube/config"); err != nil {
			return err
		}

		// 一开始，我们将APIServerDomain直接设置为master0，然后kubeadm启动kube-scheduler和kube-controller-manager，
		// 然后我们将APIServerDomain重置为master本身，但是kube-scheduler和kube-controller-manager已经加载了domain并且不会重新加载。
		// 因此，我们需要在重置APIServerDomain后重新启动它们
		if err = k.infra.CmdAsync(m, nil, "mv /etc/kubernetes/manifests/kube-scheduler.yaml /tmp/ && mv /tmp/kube-scheduler.yaml /etc/kubernetes/manifests/"); err != nil {
			return err
		}
		if err = k.infra.CmdAsync(m, nil, "mv /etc/kubernetes/manifests/kube-controller-manager.yaml /tmp/ && mv /tmp/kube-controller-manager.yaml /etc/kubernetes/manifests/"); err != nil {
			return err
		}

		klog.Infof("succeeded in joining %s as master", m)
	}
	return nil
}
