package k8s

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"time"

	clustermanager "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/shellcommand"

	"golang.org/x/sync/errgroup"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/tools/clientcmd"
	runtimeClient "sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	AuditPolicyYml = "audit-policy.yml"
	KubeadmFileYml = "/etc/kubernetes/kubeadm.yaml"
	AdminConf      = "admin.conf"
	ControllerConf = "controller-manager.conf"
	SchedulerConf  = "scheduler.conf"
	KubeletConf    = "kubelet.conf"

	// kube file
	KUBECONTROLLERCONFIGFILE = "/etc/kubernetes/controller-manager.conf"
	KUBESCHEDULERCONFIGFILE  = "/etc/kubernetes/scheduler.conf"
	AdminKubeConfPath        = "/etc/kubernetes/admin.conf"
	KubealivedPodFileName    = "kubealived.yaml"
)

const (
	GetCustomizeCRISocket         = "cat /etc/kube/cri/socket-path"
	RemoteCleanCustomizeCRISocket = "rm -f /etc/kube/cri/socket-path"
	RemoteAddEtcHosts             = "cat /etc/hosts |grep '%s' || echo '%s' >> /etc/hosts"
	RemoteReplaceKubeConfig       = `grep -qF "apiserver.cluster.local" %s  && sed -i 's/apiserver.cluster.local/%s/' %s && sed -i 's/apiserver.cluster.local/%s/' %s`
	RemoveKubeConfig              = "rm -rf /usr/bin/kube* && rm -rf ~/.kube/"
	RemoteRemoveAPIServerEtcHost  = "echo \"$(sed \"/%s/d\" /etc/hosts)\" > /etc/hosts"
	KubeDeleteNode                = "kubectl delete node %s"

	RemoteCheckRoute = "pilotctl route check --host %s"
	RemoteAddRoute   = "pilotctl route add --host %s --gateway %s"
	RemoteDelRoute   = "if command -v pilotctl > /dev/null 2>&1; then pilotctl route del --host %s --gateway %s; fi"
)

// StaticFile :static file should not be template, will never be changed while initialization.
type StaticFile struct {
	DestinationDir string
	Name           string
}

// MasterStaticFiles Put static files here, can be moved to all master nodes before kubeadm execution
var MasterStaticFiles = []*StaticFile{
	{
		DestinationDir: "/etc/kubernetes",
		Name:           AuditPolicyYml,
	},
}

// getNodeNameByCmd return node name from k8s cluster, if not found, return "" and error is nil
func (k *Runtime) getNodeNameByCmd(host net.IP) (string, error) {
	cli, err := k.GetCurrentRuntimeDriver()
	if err != nil {
		return "", err
	}
	nodes := &corev1.NodeList{}
	if err = cli.List(context.Background(), nodes); err != nil {
		return "", err
	}

	for _, nodeInfo := range nodes.Items {
		for _, addr := range nodeInfo.Status.Addresses {
			if addr.Type == corev1.NodeInternalIP && host.String() == addr.Address {
				return nodeInfo.Name, nil
			}
		}
	}

	return "", fmt.Errorf("failed to find node name for %s", host.String())
}

// vlogToStr
// Deprecated
func vlogToStr(vlog int) string {
	str := strconv.Itoa(vlog)
	return " -v " + str
}

type CommandType string

const InitMaster CommandType = "initMaster"
const JoinMaster CommandType = "joinMaster"
const JoinNode CommandType = "joinNode"

// BuildKubeadmCmd Build the cmd with some parameters for kubeadm
func (k *Runtime) BuildKubeadmCmd(name CommandType, nodeNameOverride string) (string, error) {
	// cmds := make(map[CommandType]string)
	// Please convert your v1beta1 configuration files to v1beta2 using the
	// "kubeadm config migrate" command of kubeadm v1.15.x, so v1.14 not support multi network interface.
	cmds := map[CommandType]string{
		InitMaster: fmt.Sprintf("kubeadm init --config=%s --upload-certs", KubeadmFileYml),
		JoinMaster: fmt.Sprintf("kubeadm join --config=%s", KubeadmFileYml),
		JoinNode:   fmt.Sprintf("kubeadm join --config=%s", KubeadmFileYml),
	}

	v, ok := cmds[name]
	if !ok {
		return "", fmt.Errorf("failed to get kubeadm command: %v", cmds)
	}
	if nodeNameOverride != "" {
		v = fmt.Sprintf("%s --node-name %s", v, nodeNameOverride)
	}

	if clustermanager.IsInContainer() {
		return fmt.Sprintf("%s%s", v, " --ignore-preflight-errors=all"), nil
	}
	if name == InitMaster || name == JoinMaster {
		return fmt.Sprintf("%s%s", v, " --ignore-preflight-errors=SystemVerification,Port-10250,DirAvailable--etc-kubernetes-manifests"), nil
	}

	return fmt.Sprintf("%s%s", v, " --ignore-preflight-errors=Port-10250,DirAvailable--etc-kubernetes-manifests"), nil
}

func (k *Runtime) getNodeNameOverride(ip net.IP) string {
	// 优先从 hostEnv 获取配置
	if hostValue, hostExists := k.infra.GetHostEnv(ip)[constants.EnvUseIPasNodeName]; hostExists {
		if hostValue == "true" {
			return ip.String()
		}
		// 如果明确设置为 false，返回空字符串使用默认节点名
		return ""
	}

	// 如果 hostEnv 没有配置，则从 clusterEnv 获取
	if clusterValue, clusterExists := k.infra.GetClusterEnv()[constants.EnvUseIPasNodeName]; clusterExists {
		if clusterValue == "true" {
			return ip.String()
		}
		// 如果明确设置为 false，返回空字符串使用默认节点名
		return ""
	}

	// 如果都没有配置，返回空字符串使用默认节点名
	return ""
}

func GetClientFromConfig(adminConfPath string) (runtimeClient.Client, error) {
	adminConfig, err := clientcmd.BuildConfigFromFlags("", adminConfPath)
	if nil != err {
		return nil, err
	}

	var ret runtimeClient.Client

	timeout := time.Second * 30
	err = wait.PollImmediate(time.Second*10, timeout, func() (done bool, err error) {
		cli, err := runtimeClient.New(adminConfig, runtimeClient.Options{})
		if nil != err {
			return false, err
		}

		ns := corev1.Namespace{}
		if err = cli.Get(context.Background(), runtimeClient.ObjectKey{Name: "default"}, &ns); nil != err {
			return false, err
		}

		ret = cli

		return true, nil
	})

	return ret, err
}

func (k *Runtime) setHostAlias(workers []net.IP) error {
	eg, _ := errgroup.WithContext(context.Background())

	// flush all cluster nodes as latest ipvs policy.
	// 每个worker节点
	for i := range workers {
		node := workers[i]
		eg.Go(func() error {
			if err := k.infra.CmdAsync(node, nil, shellcommand.CommandSetHostAlias(k.getAPIServerDomain(), k.getAPIServerVIP().String())); err != nil {
				return fmt.Errorf("failed to set host alias for worker %s: %v", node, err)
			}
			return nil
		})
	}

	return eg.Wait()
}

// genKubealivedImageName 生成kubealived镜像名称
func genKubealivedImageName(registryUrl string) string {
	return fmt.Sprintf("%s/bingokube/kubealived", registryUrl)
}
