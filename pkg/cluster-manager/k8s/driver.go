package k8s

import (
	"fmt"

	clustermanager "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager"
	runtimeClient "sigs.k8s.io/controller-runtime/pkg/client"
)

type kubeDriver struct {
	runtimeClient.Client
	kubeConfig string
}

func NewKubeDriver(kubeConfig string) (clustermanager.Driver, error) {
	client, err := GetClientFromConfig(kubeConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to new k8s runtime client via adminconf: %v", err)
	}

	k := &kubeDriver{
		kubeConfig: kubeConfig,
	}
	k.Client = client

	return k, nil
}

// GetAdminKubeconfig returns the file path of admin kubeconfig is using.
func (k kubeDriver) GetAdminKubeconfig() string {
	return k.kubeConfig
}
