package k8s

import (
	"bytes"
	"context"
	"fmt"
	"net"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/certs"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/kubeadm"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/env"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/ipvs"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/template"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rand"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/shellcommand"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
	runtimeClient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/yaml"

	"k8s.io/kubernetes/cmd/kubeadm/app/apis/kubeadm/v1beta3"

	"golang.org/x/sync/errgroup"
)

func (k *Runtime) initKubeadmConfig(masters []net.IP) (kubeadm.KubeadmConfig, error) {
	extraSANsStr := k.infra.GetClusterEnv()[constants.EnvCertSANs]
	var extraSANs []string
	if extraSANsStr != "" {
		extraSANs = strings.Split(extraSANsStr, ",")
	}
	conf, err := kubeadm.NewKubeadmConfig(
		k.Config.KubeadmConfigFromClusterFile,
		k.getDefaultKubeadmConfig(),
		masters,
		k.getAPIServerDomain(),
		k.Config.containerRuntimeOpts.CgroupDriver,
		k.Config.RegistryInfo.URL,
		k.getAPIServerVIP(), extraSANs)
	if err != nil {
		return kubeadm.KubeadmConfig{}, err
	}

	if output, err := k.infra.CmdToString(masters[0], nil, GetCustomizeCRISocket, ""); err == nil && output != "" {
		conf.InitConfiguration.NodeRegistration.CRISocket = output
	}

	bs, err := file.MarshalWithDelimiter(&conf.InitConfiguration,
		&conf.ClusterConfiguration,
		&conf.KubeletConfiguration,
		&conf.KubeProxyConfiguration,
		&conf.JoinConfiguration)
	if err != nil {
		return kubeadm.KubeadmConfig{}, err
	}

	localTmpFile := "/tmp/kubeadm.yaml"
	if err = os.WriteFile(localTmpFile, bs, 0600); err != nil {
		return kubeadm.KubeadmConfig{}, err
	}

	if err = k.infra.Copy(masters[0], localTmpFile, KubeadmFileYml); err != nil {
		return kubeadm.KubeadmConfig{}, err
	}
	if err = k.infra.Copy(masters[0], localTmpFile, path.Join(k.infra.GetClusterRootfsPath(), "kubeadm.yaml")); err != nil {
		return kubeadm.KubeadmConfig{}, err
	}

	if err = os.Remove(localTmpFile); err != nil {
		return kubeadm.KubeadmConfig{}, err
	}

	return conf, nil
}

func (k *Runtime) generateCert(kubeadmConf kubeadm.KubeadmConfig, master0 net.IP) error {
	hostName, err := k.infra.GetHostName(master0)
	if err != nil {
		return err
	}

	return certs.GenerateAllKubernetesCerts(
		k.getPKIPath(),
		k.getEtcdCertPath(),
		hostName,
		kubeadmConf.GetSvcCIDR(),
		kubeadmConf.GetDNSDomain(),
		kubeadmConf.GetCertSANS(),
		master0,
	)
}

func (k *Runtime) createKubeConfig(master0 net.IP) error {
	hostName, err := k.infra.GetHostName(master0)
	if err != nil {
		return err
	}
	// 若设置了是以节点IP为节点名称，则覆盖初始化hostname
	if nno := k.getNodeNameOverride(master0); nno != "" {
		hostName = nno
	}
	// TODO：kubeconfig暂时改为vip
	// controlPlaneEndpoint := fmt.Sprintf("https://%s", net.JoinHostPort(k.getAPIServerDomain(), "6443"))
	controlPlaneEndpoint := fmt.Sprintf("https://%s", net.JoinHostPort(k.getAPIServerVIP().String(), "6443"))
	// 生成/etc/kubernetes下所有相关的配置文件: admin.conf,controller-manager.conf, kubelet.conf,scheduler.conf
	return certs.CreateJoinControlPlaneKubeConfigFiles(k.infra.GetClusterRootfsPath(), k.getPKIPath(),
		"ca", hostName, controlPlaneEndpoint, "kubernetes")
}

func (k *Runtime) copyStaticFiles(nodes []net.IP) error {
	for _, sf := range MasterStaticFiles {
		staticFilePath := filepath.Join(k.getStaticFileDir(), sf.Name)
		cmdLinkStatic := fmt.Sprintf("mkdir -p %s && cp -f %s %s", sf.DestinationDir, staticFilePath, filepath.Join(sf.DestinationDir, sf.Name))
		eg, _ := errgroup.WithContext(context.Background())
		for _, host := range nodes {
			h := host
			eg.Go(func() error {
				if err := k.infra.CmdAsync(h, nil, cmdLinkStatic); err != nil {
					return fmt.Errorf("[%s] failed to link static file: %s", h, err.Error())
				}

				return nil
			})
		}
		if err := eg.Wait(); err != nil {
			return err
		}
	}
	return nil
}

// buildGenKubealivedCmd 构建生成kubealived静态文件的命令
// Deprecated 转为直接拷贝 /etc/kubernetes/kubealived.yaml
func (k *Runtime) buildGenKubealivedCmd() (string, error) {
	klog.V(5).Info("build cmd about generate kubealived static pod file")
	// 为master0生成kubealived.yaml文件至/etc/kubernetes/
	spec := ipvs.BuildKubealivedStaticPodFromSpec(&ipvs.Kubealived{})
	data, err := yaml.Marshal(spec)
	if err != nil {
		return "", fmt.Errorf("uable marshal static Pod spec: %v", err)
	}
	cmd := ipvs.BuildKubealivedStaticPodCmd(string(data), KubealivedPodFileName)

	return cmd, nil
}

// prepareAndWriteStaticPodToDisk generate kubealived static pod file
// Deprecated
func (k *Runtime) prepareAndWriteStaticPodToDisk() error {
	// 为master0生成kubealived.yaml文件至/etc/kubernetes/
	kl := &ipvs.Kubealived{
		NetCardName:          k.getDefaultNetCard(),
		ImageName:            k.getKubealivedImgName(),
		ImageVersion:         k.getKubealivedImgVersion(),
		InCluster:            false,
		Port:                 k.getAPIServerVIPPort(),
		Address:              k.getAPIServerVIP().String(),
		Subnet:               constants.DefaultKubealivedVipSubnet,
		Namespace:            metav1.NamespaceSystem,
		EnableLeaderElection: "true",
		LeaseName:            "kubealived-leader-election",
		LeaderElectionType:   "kubernetes",
		IpvsLBType:           "rr",
		ForwardMethod:        "local",
	}
	spec := ipvs.BuildKubealivedStaticPodFromSpec(kl)
	data, err := yaml.Marshal(spec)
	if err != nil {
		return fmt.Errorf("uable marshal static Pod spec: %v", err)
	}
	if err = os.MkdirAll(constants.StaticPodDir, 0700); err != nil {
		return errors.Wrapf(err, "failed to create directory %q", constants.StaticPodDir)
	}
	filename := staticPodFilepath(constants.KubealivedStaticPodName, constants.StaticPodDir)
	if err = os.WriteFile(filename, data, 0600); err != nil {
		return errors.Wrapf(err, "failed to write static pod manifest file for %q (%q)", constants.KubealivedStaticPodName, filename)
	}
	klog.Info("success generate kubealived static pod file")
	return nil
}

// getVipSubnet 获取KubealivedVipSubnet。默认为/32
func (k *Runtime) getKubealivedVipSubnet() string {
	vipSubnet := fmt.Sprintf("/%s", constants.DefaultKubealivedVipSubnet)
	if subnet, ok := k.infra.GetClusterEnv()[constants.KubealivedVipSubnet]; ok {
		vipSubnet = fmt.Sprintf("/%s", subnet)
	}
	return vipSubnet
}

func (k *Runtime) prepareAndSendKubealivedStaticPodToNode(host net.IP) error {
	// 1. 准备 kubealived 配置
	kl := &ipvs.Kubealived{
		NetCardName:          k.infra.GetHostEnv(host)[constants.NetCardName],
		ImageName:            k.getKubealivedImgName(),
		ImageVersion:         k.getKubealivedImgVersion(),
		InCluster:            false,
		Port:                 k.getAPIServerVIPPort(),
		Address:              k.getAPIServerVIP().String(),
		Subnet:               k.getKubealivedVipSubnet(),
		Namespace:            metav1.NamespaceSystem,
		EnableLeaderElection: "true",
		LeaseName:            "kubealived-leader-election",
		LeaderElectionType:   "kubernetes",
		IpvsLBType:           "rr",
		ForwardMethod:        "local",
	}

	// 2. 生成并发送静态 Pod 配置
	if err := k.generateAndSendStaticPodFile(host, kl); err != nil {
		return fmt.Errorf("failed to handle static pod: %w", err)
	}

	// 3. 处理 IPVS 规则配置
	if err := k.generateAndSendIpvsConfig(host); err != nil {
		return fmt.Errorf("failed to handle ipvs rules: %w", err)
	}

	klog.Info("successfully generated kubealived static pod file")
	return nil
}

func (k *Runtime) generateAndSendStaticPodFile(host net.IP, kl *ipvs.Kubealived) error {
	spec := ipvs.BuildKubealivedStaticPodFromSpec(kl)
	data, err := yaml.Marshal(spec)
	if err != nil {
		return fmt.Errorf("unable to marshal static Pod spec: %v", err)
	}

	// 创建临时文件
	localTmpFile := filepath.Join(k.infra.GetClusterRootfsPath(), fmt.Sprintf("tmpfile-%s", rand.GenerateRandomName(8)))
	defer os.Remove(localTmpFile) // 确保清理临时文件

	if err := os.WriteFile(localTmpFile, data, 0600); err != nil {
		return fmt.Errorf("unable to write static Pod spec to temp file: %v", err)
	}

	// 发送到目标节点
	dstFile := staticPodFilepath(constants.KubealivedStaticPodName, constants.StaticPodDir)
	if err := k.infra.Copy(host, localTmpFile, dstFile); err != nil {
		return fmt.Errorf("failed to copy static pod file: %w", err)
	}

	return nil
}

func (k *Runtime) generateAndSendIpvsConfig(host net.IP) error {
	// 获取并解析 IPVS 规则
	ipvsRules, err := k.getIpvsRules()
	if err != nil {
		return err
	}

	// 使用 template.Parse 渲染 IPVS 规则模板
	tmpl, err := template.Parse(ipvs.IpvsRulesTemplate)
	if err != nil {
		return fmt.Errorf("failed to parse template: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, map[string]interface{}{
		constants.IpvsRules: ipvsRules,
	}); err != nil {
		return fmt.Errorf("failed to execute template: %w", err)
	}

	// 创建并发送配置文件
	tmpFile := filepath.Join(k.infra.GetClusterRootfsPath(), fmt.Sprintf("tmpfile-%s", rand.GenerateRandomName(8)))
	defer os.Remove(tmpFile)

	if err := ipvs.CreateKubealivedConfig(tmpFile, buf.String()); err != nil {
		return fmt.Errorf("failed to create kubealived config: %w", err)
	}

	if err := k.infra.Copy(host, tmpFile, constants.IPVSRuleConfigPath); err != nil {
		return fmt.Errorf("failed to copy ipvs rules config: %w", err)
	}

	return nil
}

func (k *Runtime) getIpvsRules() ([]map[string]string, error) {
	if info, ok := k.infra.GetClusterEnv()[constants.IpvsRules]; ok {
		rules, err := env.ConvertJsonStrToMap(info)
		if err != nil {
			return nil, fmt.Errorf("failed to convert ipvs rules to map: %w", err)
		}
		return rules, nil
	}
	return nil, nil
}

func staticPodFilepath(componentName, manifestsDir string) string {
	return filepath.Join(manifestsDir, componentName+".yaml")
}

// Add new helper method to check if kubealived is enabled
func (k *Runtime) isKubealivedEnabled() bool {
	if val, exists := k.infra.GetClusterEnv()[constants.EnvEnableKubealived]; exists {
		enabled, _ := strconv.ParseBool(val)
		return enabled
	}
	return true // Default to true if not specified
}

// initMaster0 is using kubeadm init to start up the cluster master0.
func (k *Runtime) initMaster0(master0 net.IP) (v1beta3.BootstrapTokenDiscovery, string, error) {
	if err := k.initKube([]net.IP{master0}); err != nil {
		return v1beta3.BootstrapTokenDiscovery{}, "", err
	}

	if err := k.sendClusterCert([]net.IP{master0}); err != nil {
		return v1beta3.BootstrapTokenDiscovery{}, "", err
	}

	if err := k.sendKubeConfigFilesToMaster([]net.IP{master0}, AdminConf, ControllerConf, SchedulerConf, KubeletConf); err != nil {
		return v1beta3.BootstrapTokenDiscovery{}, "", err
	}

	// 如果启用 kubealived，则生成 kubealived 静态 Pod 配置文件 和 IPVS 规则配置文件
	if k.isKubealivedEnabled() {
		if err := k.prepareAndSendKubealivedStaticPodToNode(master0); err != nil {
			return v1beta3.BootstrapTokenDiscovery{}, "", err
		}
	}

	// 为节点`/etc/hosts`设置域名解析 例如: apiserver.cluster.local *************
	// 这里为何是master0的ip，而不是vip呢？因为master0的kubelet需要先能访问kube-apiserver，才能安装完集群，
	// 然后才能生成kubealived.yaml这个静态文件，才能将vip绑定到指定网卡中，vip才能使用，否则kubelet会一直启动不了，
	// init master0也一直失败，这就是先有鸡还是先有蛋的问题。
	if err := k.infra.CmdAsync(master0, nil, shellcommand.CommandSetHostAlias(k.getAPIServerDomain(), master0.String())); err != nil {
		// if err := k.infra.CmdAsync(master0, nil, shellcommand.CommandSetHostAlias(k.getAPIServerDomain(), k.getAPIServerVIP().String())); err != nil {
		return v1beta3.BootstrapTokenDiscovery{}, "", fmt.Errorf("failed to config cluster hosts file cmd: %v", err)
	}

	cmdInit, err := k.BuildKubeadmCmd(InitMaster, k.getNodeNameOverride(master0))
	if err != nil {
		return v1beta3.BootstrapTokenDiscovery{}, "", err
	}
	klog.Infof("start to init master0 [%s]...", master0.String())

	// TODO skip containerd/docker version error check for test
	output, err := k.infra.Cmd(master0, nil, cmdInit)
	if err != nil {
		_, wErr := constants.StdOut.WriteString(string(output))
		if wErr != nil {
			return v1beta3.BootstrapTokenDiscovery{}, "", err
		}
		return v1beta3.BootstrapTokenDiscovery{}, "", fmt.Errorf("failed to init master0: %s. Please clean and reinstall", err)
	}
	cmdInitKubeconfig := "rm -rf .kube/config && mkdir -p /root/.kube && cp /etc/kubernetes/admin.conf /root/.kube/config"
	if err = k.infra.CmdAsync(master0, nil, cmdInitKubeconfig); err != nil {
		return v1beta3.BootstrapTokenDiscovery{}, "", err
	}

	// 如果安装 kubealived 组件，才需要等待 kubealived 绑定 vip
	if k.isKubealivedEnabled() {
		u := url.URL{
			Scheme: "https",
			Host:   fmt.Sprintf("%s%s", k.getAPIServerVIP().String(), ":6443"),
		}
		urlPath := fmt.Sprintf("%s%s", u.String(), constants.ProbeAPI)

		klog.Infof("Waiting for vip [%s] availability...", urlPath)
		f := func() (bool, error) {
			return unet.WaitVipRunning(urlPath)
		}
		if err = wait.Poll(time.Second, 40*time.Second, f); err != nil {
			klog.Warningf("It seems kubealived not running: %v", err)
		}
		klog.V(5).Infof("Kuealived has bind vip addr on [%s]", k.getDefaultNetCard())

		incluster := k.getKubealivedRunType()

		if incluster {
			// TODO: 为kubealived创建一个serviceAccount
			driver, err := k.GetCurrentRuntimeDriver()
			if err != nil {
				return v1beta3.BootstrapTokenDiscovery{}, "", err
			}
			sa := &v1.ServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.ServiceAccountName,
					Namespace: metav1.NamespaceSystem,
				},
			}
			if err = driver.Create(context.Background(), sa, &runtimeClient.CreateOptions{}); err != nil && !apierrors.IsAlreadyExists(err) {
				klog.Warningf("failed to create serviceAccount for kubealived: %v, Please create manually later be sure: `kubectl -n kube-system create sa kubealived`", err)
			}
		}
	}

	token, certKey := k.decodeMaster0Output(output)

	return token, certKey, nil
}

// decode output to join token hash and key
func (k *Runtime) decodeMaster0Output(output []byte) (v1beta3.BootstrapTokenDiscovery, string) {
	s0 := string(output)
	klog.V(5).Infof("decodeOutput: %s", s0)
	slice := strings.Split(s0, "kubeadm join")
	slice1 := strings.Split(slice[1], "Please note")
	klog.Infof("join command is: kubeadm join %s", slice1[0])

	return k.decodeJoinCmd(slice1[0])
}

// decodeJoinCmd decode cmd about join node
// 192.168.0.200:6443 --token 9vr73a.a8uxyaju799qwdjv --discovery-token-ca-cert-hash sha256:7c2e69131a36ae2a042a339b33381c6d0d43887e2de83720eff5359e26aec866
// --experimental-control-plane --certificate-key f8902e114ef118304e561c3ecd4d0b543adc226b7a07f675f56564185ffe0c07
func (k *Runtime) decodeJoinCmd(cmd string) (v1beta3.BootstrapTokenDiscovery, string) {
	klog.V(5).Infof("[globals]decodeJoinCmd: %s", cmd)
	stringSlice := strings.Split(cmd, " ")

	token := v1beta3.BootstrapTokenDiscovery{}
	var certKey string

	for i, r := range stringSlice {
		// upstream error, delete \t, \\, \n, space.
		r = strings.ReplaceAll(r, "\t", "")
		r = strings.ReplaceAll(r, "\n", "")
		r = strings.ReplaceAll(r, "\\", "")
		r = strings.TrimSpace(r)
		if strings.Contains(r, "--token") {
			token.Token = stringSlice[i+1]
		}
		if strings.Contains(r, "--discovery-token-ca-cert-hash") {
			token.CACertHashes = []string{stringSlice[i+1]}
		}
		if strings.Contains(r, "--certificate-key") {
			certKey = stringSlice[i+1][:64]
		}
	}

	return token, certKey
}

// initKube do some initialize kubelet works, such as configuring the host environment, initializing the kubelet service, and so on.
func (k *Runtime) initKube(hosts []net.IP) error {
	initKubeletCmd := fmt.Sprintf("cd %s && export RegistryURL=%s && bash %s", filepath.Join(k.infra.GetClusterRootfsPath(), constants.Scripts), k.Config.RegistryInfo.URL, "init-kube.sh")
	klog.V(5).Infof("The cmd info to init kubelet: %s", initKubeletCmd)
	eg, _ := errgroup.WithContext(context.Background())
	for _, h := range hosts {
		host := h
		eg.Go(func() error {
			if err := k.infra.CmdAsync(host, nil, initKubeletCmd, shellcommand.CommandSetHostAlias(k.getAPIServerDomain(), k.getAPIServerVIP().String())); err != nil {
				return fmt.Errorf("failed to init Kubelet Service on (%s): %s", host, err.Error())
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return err
	}
	return nil
}

func (k *Runtime) sendClusterCert(hosts []net.IP) error {
	f := func(host net.IP) error {
		if err := k.infra.Copy(host, k.getPKIPath(), certs.KubeDefaultCertPath); err != nil {
			return fmt.Errorf("failed to copy cluster cert: %v", err)
		}
		if err := k.infra.Copy(host, k.getPKIPath(), k.getPKIPath()); err != nil {
			return fmt.Errorf("failed to copy cluster cert: %v", err)
		}
		return nil
	}

	return k.infra.Execute(hosts, f)
}

func (k *Runtime) sendKubeadmFile(hosts []net.IP) error {
	f := func(host net.IP) error {
		if err := k.infra.Copy(host, path.Join(k.infra.GetClusterRootfsPath(), "kubeadm.yaml"), path.Join(k.infra.GetClusterRootfsPath(), "kubeadm.yaml")); err != nil {
			return fmt.Errorf("failed to copy kubeadm file: %v", err)
		}
		return nil
	}

	return k.infra.Execute(hosts, f)
}

// sendKubeConfigFilesToMaster 发送组件配置到新加入的master节点的 `/etc/kubernetes/`下.
func (k *Runtime) sendKubeConfigFilesToMaster(masters []net.IP, files ...string) error {
	for _, kubeFile := range files {
		src := filepath.Join(k.infra.GetClusterRootfsPath(), kubeFile)
		dest := filepath.Join(certs.KubernetesConfigDir, kubeFile)

		f := func(host net.IP) error {
			if err := k.infra.Copy(host, src, dest); err != nil {
				return fmt.Errorf("failed to copy cluster kubeconfig file : %v", err)
			}
			if err := k.infra.Copy(host, src, src); err != nil {
				return fmt.Errorf("failed to copy cluster kubeconfig file : %v", err)
			}
			return nil
		}
		if err := k.infra.Execute(masters, f); err != nil {
			return err
		}
	}

	return nil
}

// sendKubealivedFileToMaster 为每个master节点生成kubealived.yaml文件
func (k *Runtime) sendKubealivedFileToMaster(masters []net.IP) error {
	f := func(host net.IP) error {
		if err := k.infra.Copy(host, path.Join(constants.StaticPodDir, constants.KubealivedFile), path.Join(constants.StaticPodDir, constants.KubealivedFile)); err != nil {
			return fmt.Errorf("failed to copy kubealived file: %v", err)
		}
		return nil
	}
	return k.infra.Execute(masters, f)
}

func (k *Runtime) getJoinTokenHashAndKey(master0 net.IP) (v1beta3.BootstrapTokenDiscovery, string, error) {
	cmd := `kubeadm init phase upload-certs --upload-certs`

	output, err := k.infra.CmdToString(master0, nil, cmd, "\r\n")
	if err != nil {
		return v1beta3.BootstrapTokenDiscovery{}, "", err
	}
	klog.V(5).Infof("The cmd about decode certs: %s", output)

	slice := strings.Split(output, "Using certificate key:")
	if len(slice) != 2 {
		return v1beta3.BootstrapTokenDiscovery{}, "", fmt.Errorf("failed to get certifacate key: %s", slice)
	}
	key := strings.Replace(slice[1], "\r\n", "", -1)
	certKey := strings.Replace(key, "\n", "", -1)

	cmd = "kubeadm token create --print-join-command"

	out, err := k.infra.Cmd(master0, nil, cmd)
	if err != nil {
		return v1beta3.BootstrapTokenDiscovery{}, "", fmt.Errorf("failed to create kubeadm join token: %v", err)
	}

	token, certKey2 := k.decodeMaster0Output(out)

	if certKey == "" {
		certKey = certKey2
	}

	return token, certKey, nil
}
