package k8s

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net"
	"os"
	"path"
	"path/filepath"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/consts"
	clustermanager "gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/kubeadm"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	containerruntime "gitlab.bingosoft.net/bingokube/kubepilot/pkg/container-runtime"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/env"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infradriver"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/registry"
	utils "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/decoder"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/shellcommand"
	"golang.org/x/sync/errgroup"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
	k8snet "k8s.io/utils/net"
	runtimeClient "sigs.k8s.io/controller-runtime/pkg/client"
)

type Config struct {
	VIP                          string
	Port                         string
	NetCard                      string
	ImageName                    string
	ImageVersion                 string
	InCluster                    string
	RegistryInfo                 registry.Info
	containerRuntimeOpts         containerruntime.RuntimeOpts
	KubeadmConfigFromClusterFile kubeadm.KubeadmConfig
	APIServerDomain              string
}

// Runtime struct is the runtime interface for kubernetes
type Runtime struct {
	infra  infradriver.InfraDriver
	Config *Config
}

func NewKubeadmRuntime(clusterFileKubeConfig kubeadm.KubeadmConfig, infra infradriver.InfraDriver, opts containerruntime.RuntimeOpts, registryInfo registry.Info) (clustermanager.Installer, error) {
	k := &Runtime{
		infra: infra,
		Config: &Config{
			KubeadmConfigFromClusterFile: clusterFileKubeConfig,
			APIServerDomain:              constants.DefaultAPIserverDomain,
			VIP:                          constants.DefaultVIP,
			Port:                         constants.DefaultVipPort,
			NetCard:                      constants.DefaultNetCard,
			ImageName:                    genKubealivedImageName(registryInfo.URL),
			ImageVersion:                 constants.DefaultKubealivedImgVersion,
			RegistryInfo:                 registryInfo,
			containerRuntimeOpts:         opts,
		},
	}
	if infra.IsRemoteRegistry() {
		k.Config.Port = "6443"
	}

	if hosts := infra.GetHostIPList(); len(hosts) > 0 && k8snet.IsIPv6(hosts[0]) {
		k.Config.VIP = constants.DefaultVIPForIPv6
	}

	if domain, ok := infra.GetClusterEnv()[constants.EnvClusterDomain]; ok {
		k.Config.APIServerDomain = domain
	}

	if inCluster, ok := infra.GetClusterEnv()[constants.EnvInCluster]; ok {
		k.Config.InCluster = inCluster
	}

	if ipv4, ok := infra.GetClusterEnv()[constants.EnvIPvsVIPForIPv4]; ok {
		klog.V(5).Infof("it will use IPv4 VIP [%s]", ipv4)
		k.Config.VIP = ipv4
	}

	if ipv6, ok := infra.GetClusterEnv()[constants.EnvIPvsVIPForIPv6]; ok {
		klog.V(5).Infof("it will use IPv6 VIP [%s]", ipv6)
		k.Config.VIP = ipv6
	}

	if port, ok := infra.GetClusterEnv()[constants.Port]; ok {
		k.Config.Port = port
	}

	// 将每个节点的管理网卡信息添加到hostEnv，用于生成kubealived静态pod yaml文件时所需
	if info, ok := infra.GetClusterEnv()[constants.NodesInfo]; ok {
		nodes, err := env.ConvertJsonStrToMap(info)
		if err != nil {
			return nil, fmt.Errorf("failed to convert node info to map,err: %w", err)
		}

		for _, node := range nodes {
			manageIP, ok := node[constants.ManagementNetworkIP]
			if !ok {
				return nil, errors.New("unable to get managementNetworkIP")
			}

			manageNIC, ok := node[constants.ManagementNetworkInterface]
			if !ok {
				return nil, errors.New("unable to get managementNetworkInterface")
			}
			if manageNIC == "" {
				manageNIC = constants.DefaultNetCard
			}

			k.infra.AddHostEnv(net.ParseIP(manageIP), constants.NetCardName, manageNIC)
		}
	}

	if imgName, ok := infra.GetClusterEnv()[constants.KubealivedImgName]; ok {
		k.Config.ImageName = imgName
	}

	if imgVesion, ok := infra.GetClusterEnv()[constants.KubealivedImgVesion]; ok {
		k.Config.ImageVersion = imgVesion
	}

	return k, nil
}

func (k *Runtime) Install() error {
	masters := k.infra.GetHostIPListByRole(consts.MASTER)
	workers := k.infra.GetHostIPListByRole(consts.NODE)

	kubeadmConf, err := k.initKubeadmConfig(masters)
	if err != nil {
		return err
	}

	if err = k.generateCert(kubeadmConf, masters[0]); err != nil {
		return err
	}

	if err = k.createKubeConfig(masters[0]); err != nil {
		return err
	}

	if err = k.copyStaticFiles(masters[0:1]); err != nil {
		return err
	}
	// 初始化第一个master节点
	token, certKey, err := k.initMaster0(masters[0])
	if err != nil {
		return err
	}

	if err = k.resetNodeHostFile(); err != nil {
		return err
	}

	if err = k.joinMasters(masters[1:], masters[0], kubeadmConf, token, certKey); err != nil {
		return err
	}

	if err = k.joinNodes(workers, masters, kubeadmConf, token); err != nil {
		return err
	}

	driver, err := k.GetCurrentRuntimeDriver()
	if err != nil {
		return err
	}

	if err := k.dumpKubeConfigIntoCluster(driver, masters[0]); err != nil {
		return err
	}

	klog.Info("succeeded in creating a new cluster.")
	return nil
}

func (k *Runtime) GetCurrentRuntimeDriver() (clustermanager.Driver, error) {
	return NewKubeDriver(AdminKubeConfPath)
}

func (k *Runtime) resetNodeHostFile() error {
	var (
		all             = k.infra.GetHostIPList()
		vip             = k.getAPIServerVIP().String()
		registryAddress = k.getAPIServerVIP().String()
		apiServerDomain = k.getAPIServerDomain()
	)

	if k.infra.GetClusterRegistry().ExternalRegistry != nil {
		registryAddress = k.infra.GetClusterRegistry().ExternalRegistry.RegistryVIP
	}

	clusterEnv := k.infra.GetClusterEnv()
	registryDomain, ok := clusterEnv[constants.EnvRegistryDomain]
	if !ok {
		return fmt.Errorf("missing required environment variable: %s", constants.EnvRegistryDomain)
	}

	clusterEnv[constants.APIServerDomain] = apiServerDomain
	if err := k.infra.SetClusterHostFile(all, registryAddress, registryDomain); err != nil {
		return fmt.Errorf("failed to config cluster hosts file cmd: %v", err)
	}

	if err := k.infra.SetClusterHostFile(all, vip, apiServerDomain); err != nil {
		return fmt.Errorf("failed to config cluster hosts file cmd: %v", err)
	}
	return nil
}

func (k *Runtime) Upgrade() error {
	panic("now not support upgrade")
}

func (k *Runtime) Reset() error {
	masters := k.infra.GetHostIPListByRole(consts.MASTER)
	workers := k.infra.GetHostIPListByRole(consts.NODE)
	return k.reset(masters, workers)
}

func (k *Runtime) ScaleUp(newMasters, newWorkers []net.IP) error {
	masters := k.infra.GetHostIPListByRole(consts.MASTER)
	all := append(newMasters, newWorkers...)
	if err := k.remoteRegistryHostalias(all); err != nil {
		return err
	}
	kubeadmConfig, err := kubeadm.LoadKubeadmConfigs(path.Join(k.infra.GetClusterRootfsPath(), "kubeadm.yaml"), utils.DecodeCRDFromFile)
	if err != nil {
		return err
	}

	token, certKey, err := k.getJoinTokenHashAndKey(masters[0])
	if err != nil {
		return err
	}

	if err = k.joinMasters(newMasters, masters[0], kubeadmConfig, token, certKey); err != nil {
		return err
	}

	if err = k.joinNodes(newWorkers, masters, kubeadmConfig, token); err != nil {
		return err
	}
	klog.Info("cluster scale up succeeded!")

	return nil
}

func (k *Runtime) ScaleDown(mastersToDelete, workersToDelete []net.IP) error {
	masters := k.infra.GetHostIPListByRole(consts.MASTER)
	// 剩余的master节点
	remainMasters := unet.RemoveIPs(masters, mastersToDelete)
	if len(remainMasters) == 0 {
		return fmt.Errorf("cleaning up all masters is illegal, unless you give the --all flag, which will delete the entire cluster")
	}

	if len(workersToDelete) > 0 {
		if err := k.deleteNodes(workersToDelete, remainMasters); err != nil {
			return err
		}
	}

	if len(mastersToDelete) > 0 {
		if err := k.deleteMasters(mastersToDelete, remainMasters); err != nil {
			return err
		}
	}
	klog.Info("cluster scale down succeeded!")

	return nil
}

// remoteRegistryHostalias need set registry hostalias for nodes
func (k *Runtime) remoteRegistryHostalias(nodes []net.IP) error {
	if !k.infra.IsRemoteRegistry() {
		return nil
	}
	klog.V(5).Infof("start set registry hostalias for new nodes %s", nodes)
	eg, _ := errgroup.WithContext(context.Background())

	for _, node := range nodes {
		n := node
		eg.Go(func() error {
			klog.V(5).Infof("start set registry hostalias for new node %s", n)
			if err := k.infra.CmdAsync(n, nil, shellcommand.CommandSetHostAlias(registry.ExternalRegistryDomain(k.infra), registry.ExternalRegistryVIP(k.infra))); err != nil {
				return fmt.Errorf("failed to exec set hostalias command on node [%s]: %v", n.String(), err)
			}
			klog.V(5).Infof("succeeded in set registry hostalias for node %s", n)
			return nil
		})
	}

	return eg.Wait()
}

// dumpKubeConfigIntoCluster save AdminKubeConf to cluster as secret resource.
func (k *Runtime) dumpKubeConfigIntoCluster(driver clustermanager.Driver, master0 net.IP) error {
	kubeConfigContent, err := os.ReadFile(AdminKubeConfPath)
	if err != nil {
		return err
	}

	kubeConfigContent = bytes.ReplaceAll(kubeConfigContent, []byte(constants.APIServerDomain), []byte(master0.String()))

	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "admin.conf",
			Namespace: metav1.NamespaceSystem,
		},
		Type: corev1.SecretTypeOpaque,
		Data: map[string][]byte{
			"admin.conf": kubeConfigContent,
		},
	}

	if err = driver.Create(context.Background(), secret, &runtimeClient.CreateOptions{}); err != nil {
		if !apierrors.IsAlreadyExists(err) {
			return fmt.Errorf("unable to create secret: %v", err)
		}

		if err = driver.Update(context.Background(), secret, &runtimeClient.UpdateOptions{}); err != nil {
			return fmt.Errorf("unable to update secret: %v", err)
		}
	}

	return nil
}

// /var/lib/kubepilot/data/my-cluster/pki
func (k *Runtime) getPKIPath() string {
	return filepath.Join(k.infra.GetClusterRootfsPath(), "pki")
}

// /var/lib/kubepilot/data/my-cluster/pki/etcd
func (k *Runtime) getEtcdCertPath() string {
	return filepath.Join(k.getPKIPath(), "etcd")
}

// /var/lib/kubepilot/data/my-cluster/rootfs/statics
func (k *Runtime) getStaticFileDir() string {
	return filepath.Join(k.infra.GetClusterRootfsPath(), "statics")
}

// /var/lib/kubepilot/data/my-cluster/mount/etc/kubeadm.yml
func (k *Runtime) getDefaultKubeadmConfig() string {
	return filepath.Join(k.infra.GetClusterRootfsPath(), constants.Etc, "kubeadm.yml")
}

func (k *Runtime) getAPIServerDomain() string {
	return k.Config.APIServerDomain
}

func (k *Runtime) getAPIServerVIP() net.IP {
	return net.ParseIP(k.Config.VIP)
}

func (k *Runtime) getExternalRegistryDomain() string {
	return k.Config.APIServerDomain
}

func (k *Runtime) getExternalRegistryVIP() net.IP {
	return net.ParseIP(k.Config.VIP)
}

func (k *Runtime) getAPIServerVIPPort() string {
	return k.Config.Port
}

// getDefaultNetCard 获取kubealived使用的网卡名称
func (k *Runtime) getDefaultNetCard() string {
	return k.Config.NetCard
}

// getKubealivedImgName 获取 kubealived的docker镜像名称
func (k *Runtime) getKubealivedImgName() string {
	return k.Config.ImageName
}

// getKubealivedImgVersion 获取 kubealived的docker镜像版本
func (k *Runtime) getKubealivedImgVersion() string {
	return k.Config.ImageVersion
}

func (k *Runtime) getKubealivedRunType() bool {
	return k.Config.InCluster == "true"
}
