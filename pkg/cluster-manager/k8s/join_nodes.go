package k8s

import (
	"context"
	"fmt"
	"net"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/cluster-manager/k8s/kubeadm"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	unet "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/net"
	"k8s.io/klog/v2"

	"golang.org/x/sync/errgroup"
	"k8s.io/kubernetes/cmd/kubeadm/app/apis/kubeadm/v1beta3"
)

func (k *Runtime) joinNodes(workers, masters []net.IP, kubeadmConfig kubeadm.KubeadmConfig, token v1beta3.BootstrapTokenDiscovery) error {
	if len(workers) == 0 {
		return nil
	}

	// TODO: bugfix: keep the same CRISocket with InitConfiguration
	if err := k.initKube(workers); err != nil {
		return err
	}

	kubeadmConfig.JoinConfiguration.Discovery.BootstrapToken = &token
	kubeadmConfig.JoinConfiguration.Discovery.BootstrapToken.APIServerEndpoint = net.JoinHostPort(k.getAPIServerVIP().String(), "6443")
	kubeadmConfig.JoinConfiguration.ControlPlane = nil

	eg, _ := errgroup.WithContext(context.Background())

	for _, n := range workers {
		klog.V(5).Info("start adding woker nodes concurrently")
		node := n
		eg.Go(func() error {
			klog.Infof("start to join %s as worker", node)
			joinNodeCmd, err := k.BuildKubeadmCmd(JoinNode, k.getNodeNameOverride(node))
			if err != nil {
				return err
			}

			myKubeadmConfig := kubeadmConfig

			if output, err := k.infra.CmdToString(node, nil, GetCustomizeCRISocket, ""); err == nil && output != "" {
				myKubeadmConfig.JoinConfiguration.NodeRegistration.CRISocket = output
			}
			joinConfig, err := file.MarshalWithDelimiter(myKubeadmConfig.JoinConfiguration, myKubeadmConfig.KubeletConfiguration)
			if err != nil {
				return err
			}
			writeJoinConfigCmd := fmt.Sprintf("mkdir -p /etc/kubernetes && echo \"%s\" > %s", joinConfig, KubeadmFileYml)

			if err = k.infra.CmdAsync(node, nil, writeJoinConfigCmd); err != nil {
				return fmt.Errorf("failed to set join kubeadm config on host(%s) with cmd(%s): %v", node, writeJoinConfigCmd, err)
			}

			if err = k.infra.CmdAsync(node, nil, joinNodeCmd); err != nil {
				return fmt.Errorf("failed to join node %s: %v", node, err)
			}
			klog.Infof("succeess to join worker [%s] into cluster", node)

			return nil
		})
	}
	return eg.Wait()
}

// checkMultiNetworkAddVIPRoute
// Deprecated
func (k *Runtime) checkMultiNetworkAddVIPRoute(node net.IP) error {
	result, err := k.infra.CmdToString(node, nil, fmt.Sprintf(RemoteCheckRoute, node), "")
	if err != nil {
		return err
	}
	if result == unet.RouteOK {
		return nil
	}

	cmd := fmt.Sprintf(RemoteAddRoute, k.getAPIServerVIP(), node)
	_, err = k.infra.Cmd(node, nil, cmd)

	return err
}
