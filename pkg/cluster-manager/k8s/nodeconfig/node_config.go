package nodeconfig

import (
	"context"
	"fmt"
	"time"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/client/k8s"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/yaml"
)

const (
	// CreateCluster 表示创建集群的操作
	CreateCluster = "createCluster"
	// AddNode 表示添加节点的操作
	AddNode = "addNode"
	// DeleteNode 表示删除节点的操作
	DeleteNode = "deleteNode"
)

// ManageNodeConfigmapFunc 是用于通过 ConfigMap 管理节点配置的函数类型的别名
type ManageNodeConfigmapFunc func([]map[string]string) error

// ManageNodeConfigmapFuncMap 将操作与相应的函数关联起来
var ManageNodeConfigmapFuncMap = map[string]ManageNodeConfigmapFunc{
	CreateCluster: initNodeInfoToConfigmap,
	AddNode:       addNodeInfoToConfigmap,
	DeleteNode:    deleteNodeInfoFromConfigmap,
}

// extractFieldKeys 是要从节点配置中提取的字段键列表
var extractFieldKeys = []string{
	constants.ManagementNetworkIP,
	constants.ManagementNetworkInterface,
	constants.DataNetworkIP,
	constants.DataNetworkInterface,
	constants.BusinessNetworkIP,
	constants.BusinessNetworkInterface,
}

// filterNodesFunc 是用于过滤节点的函数类型的别名
type filterNodesFunc func(existingNodes []map[string]string, nodes []map[string]string) []map[string]string

// filterNodesFuncMap 将操作与相应的过滤函数关联起来
var filterNodesFuncMap = map[string]filterNodesFunc{
	AddNode:    filterAddNodes,
	DeleteNode: filterDeleteNodes,
}

// initNodeInfoToConfigmap 将节点信息初始化到 configmap 中
func initNodeInfoToConfigmap(nodes []map[string]string) error {
	// 创建 Kubernetes 客户端
	cli, err := k8s.NewK8sClient()
	if err != nil {
		return fmt.Errorf("failed to init k8s client,err: %w", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 从节点配置中提取指定字段
	nodes = extractNodeFields(nodes, extractFieldKeys)

	// 将节点切片转换为 JSON 格式
	nodesJSON, err := yaml.Marshal(nodes)
	if err != nil {
		return fmt.Errorf("failed to marshal nodes info,err: %w", err)
	}

	// 创建一个新的 ConfigMap 并设置节点数据
	// TODO: 是否需要考虑该配置已存在的问题?
	_, err = cli.ConfigMap(constants.KubepilotNodeInfoConfigMapNamespace).
		Create(ctx, &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name: constants.KubepilotNodeInfoConfigConfigMapName,
			},
			Data: map[string]string{
				constants.KubepilotNodeInfoConfigMapDataName: string(nodesJSON),
			},
		}, metav1.CreateOptions{})

	if err != nil {
		return fmt.Errorf("failed to create configMap for %s,err: %w", constants.KubepilotNodeInfoConfigConfigMapName, err)
	}

	return nil
}

// addNodeInfoToConfigmap 将节点信息添加到configmap中
func addNodeInfoToConfigmap(nodes []map[string]string) error {
	// 创建 Kubernetes 客户端
	cli, err := k8s.NewK8sClient()
	if err != nil {
		return fmt.Errorf("failed to init k8s client,err: %w", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 获取当前的 ConfigMap
	cm, err := cli.ConfigMap(constants.KubepilotNodeInfoConfigMapNamespace).
		Get(ctx, constants.KubepilotNodeInfoConfigConfigMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get configMap,err: %w", err)
	}

	// 反序列化现有的数据
	var existingNodes []map[string]string
	if err = yaml.Unmarshal([]byte(cm.Data[constants.KubepilotNodeInfoConfigMapDataName]), &existingNodes); err != nil {
		return fmt.Errorf("failed to unmarshal for configMap,err: %w", err)
	}

	// 过滤并合并新节点
	newNodes := filterNodesFuncMap[AddNode](existingNodes, nodes)

	// 序列化更新后的节点数据
	nodesJSON, err := yaml.Marshal(newNodes)
	if err != nil {
		return fmt.Errorf("failed to marshal new nodes info,err: %w", err)
	}

	// 更新 ConfigMap 设置新数据
	cm.Data[constants.KubepilotNodeInfoConfigMapDataName] = string(nodesJSON)
	_, err = cli.ConfigMap(constants.KubepilotNodeInfoConfigMapNamespace).
		Update(ctx, cm, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("failed to update configMap,err: %w", err)
	}

	return nil
}

// deleteNodeInfoFromConfigmap 从ConfigMap中把节点信息删除
func deleteNodeInfoFromConfigmap(nodes []map[string]string) error {
	// 创建 Kubernetes 客户端
	cli, err := k8s.NewK8sClient()
	if err != nil {
		return fmt.Errorf("failed to init k8s client,err: %w", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 获取当前的 ConfigMap
	cm, err := cli.ConfigMap(constants.KubepilotNodeInfoConfigMapNamespace).
		Get(ctx, constants.KubepilotNodeInfoConfigConfigMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get configMap,err: %w", err)
	}

	// 反序列化现有的数据
	var existingNodes []map[string]string
	if err = yaml.Unmarshal([]byte(cm.Data[constants.KubepilotNodeInfoConfigMapDataName]), &existingNodes); err != nil {
		return fmt.Errorf("failed to unmarshal configMap,err: %w", err)
	}

	// 过滤并移除指定节点
	newNodes := filterNodesFuncMap[DeleteNode](existingNodes, nodes)

	// 序列化更新后的节点数据
	nodesJSON, err := yaml.Marshal(newNodes)
	if err != nil {
		return fmt.Errorf("failed to marshal new nodes info,err: %w", err)
	}

	// 更新 ConfigMap 设置新数据
	cm.Data[constants.KubepilotNodeInfoConfigMapDataName] = string(nodesJSON)
	_, err = cli.ConfigMap(constants.KubepilotNodeInfoConfigMapNamespace).
		Update(ctx, cm, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("failed to update configMap,err: %w", err)
	}

	return nil
}

// extractNodeFields 从一系列节点配置中提取指定字段,例如节点保存了密码等敏感字段，所以需要`脱敏`
func extractNodeFields(nodeConfig []map[string]string, keys []string) []map[string]string {
	// 提取所需的键值对到一个新的节点配置切片中
	var extractedNodeConfig []map[string]string
	for _, config := range nodeConfig {
		extractedConfig := make(map[string]string)
		for _, key := range keys {
			// 弱水三千，只取一瓢
			if value, exists := config[key]; exists {
				extractedConfig[key] = value
			}
		}
		if len(extractedConfig) > 0 {
			extractedNodeConfig = append(extractedNodeConfig, extractedConfig)
		}
	}

	return extractedNodeConfig
}

// filterDeleteNodes 从现有的节点列表中移除指定的节点
// 接收两个参数：existingNodes 和 nodes
// existingNodes 是当前存在的节点列表，每个节点是一个包含管理网络 IP 的键值对的 map
// nodes 包含要移除的节点列表
// 函数返回一个新的节点列表，其中不包含要移除的节点
func filterDeleteNodes(existingNodes []map[string]string, nodes []map[string]string) []map[string]string {
	// 创建一个用于快速查找的映射，以管理网络 IP 作为键
	nodeMap := make(map[string]struct{})
	for _, node := range nodes {
		if ip, ok := node[constants.ManagementNetworkIP]; ok {
			nodeMap[ip] = struct{}{}
		}
	}

	// 过滤现有节点，排除那些需要被移除的节点
	var tmpNodes []map[string]string
	for _, node := range existingNodes {
		if ip, ok := node[constants.ManagementNetworkIP]; ok {
			if _, found := nodeMap[ip]; !found {
				tmpNodes = append(tmpNodes, node)
			}
		}
	}

	// 从过滤后的节点列表中提取指定字段
	newNodes := extractNodeFields(tmpNodes, extractFieldKeys)

	return newNodes
}

// filterDeleteNodes 从现有的节点列表中添加节点
// 接收两个参数：existingNodes 和 nodes
// existingNodes 是当前存在的节点列表，每个节点是一个包含管理网络 IP 的键值对的 map
// nodes 包含要新增加的节点
// 函数返回一个新的节点列表，如果nodes存在则覆盖，不存在则新增
func filterAddNodes(existingNodes []map[string]string, nodes []map[string]string) []map[string]string {
	// 创建一个用于快速查找的映射
	nodeMap := make(map[string]map[string]string)

	// 将现有节点添加到映射中
	for _, node := range existingNodes {
		if ip, ok := node[constants.ManagementNetworkIP]; ok {
			nodeMap[ip] = node
		}
	}

	// 更新或添加新节点
	for _, node := range nodes {
		if ip, ok := node[constants.ManagementNetworkIP]; ok {
			nodeMap[ip] = node
		}
	}

	// 从映射中提取节点
	var tmpNodes []map[string]string
	for _, node := range nodeMap {
		tmpNodes = append(tmpNodes, node)
	}

	newNodes := extractNodeFields(tmpNodes, extractFieldKeys)

	return newNodes
}
