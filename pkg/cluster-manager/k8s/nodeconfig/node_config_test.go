package nodeconfig

import (
	"context"
	"testing"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/client/k8s"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/yaml"
)

// TestCreateClusterConfig tests the initNodeInfoToConfigmap function.
func TestCreateClusterConfig(t *testing.T) {
	// Arrange
	nodes := []map[string]string{
		{
			"managementNetworkIP": "***********",
			"businessNetworkIP":   "*********",
			"IncorrectField":      "..",
			// ... other fields as needed
		},
	}

	// Act
	err := initNodeInfoToConfigmap(nodes)

	// Assert
	if err != nil {
		t.Errorf("initNodeInfoToConfigmap() error = %v, wantErr %v", err, false)
		return
	}

	// Verify the ConfigMap was created with the correct data.
	client, _ := k8s.NewK8sClient()
	cm, err := client.ConfigMap(constants.KubepilotNodeInfoConfigMapNamespace).
		Get(context.TODO(), constants.KubepilotNodeInfoConfigConfigMapName, metav1.GetOptions{})
	if err != nil {
		t.Fatalf("Failed to get ConfigMap: %v", err)
	}

	defer func() {
		_ = client.ConfigMap(constants.KubepilotNodeInfoConfigMapNamespace).Delete(context.TODO(), constants.KubepilotNodeInfoConfigConfigMapName, metav1.DeleteOptions{})
	}()

	newNodes := extractNodeFields(nodes, extractFieldKeys)
	expectedData, _ := yaml.Marshal(newNodes)
	if cm.Data[constants.KubepilotNodeInfoConfigMapDataName] != string(expectedData) {
		t.Errorf("ConfigMap data mismatch")
	}
}

// TestAddNodeConfig tests the addNodeInfoToConfigmap function.
func TestAddNodeConfig(t *testing.T) {
	// Arrange
	newNodes := []map[string]string{
		{
			"managementNetworkIP": "***********",
			"businessNetworkIP":   "*********",
			"IncorrectField":      "..",
			// ... other fields as needed
		},
	}

	// Act
	err := addNodeInfoToConfigmap(newNodes)

	// Assert
	if err != nil {
		t.Errorf("AddNodeInfoToConfigmap() error = %v, wantErr %v", err, false)
	}

	// Verify the ConfigMap was updated with the new nodes.
	client, _ := k8s.NewK8sClient()
	cm, err := client.ConfigMap(constants.KubepilotNodeInfoConfigMapNamespace).
		Get(context.TODO(), constants.KubepilotNodeInfoConfigConfigMapName, metav1.GetOptions{})
	if err != nil {
		t.Fatalf("Failed to get ConfigMap: %v", err)
	}

	// Unmarshal the existing data.
	var existingNodes []map[string]string
	if err = yaml.Unmarshal([]byte(cm.Data[constants.KubepilotNodeInfoConfigMapDataName]), &existingNodes); err != nil {
		t.Fatalf("failed to unmarshal existing node data: %v", err)
	}

	// Check if the new nodes were added.
	for _, node := range newNodes {
		if !containsNode(existingNodes, node) {
			t.Errorf("Node was not added: %v", node)
		}
	}
}

// containsNode checks if a node exists in the list of nodes based on the management IP.
func containsNode(nodes []map[string]string, searchNode map[string]string) bool {
	for _, node := range nodes {
		if node["managementNetworkIP"] == searchNode["managementNetworkIP"] {
			return true
		}
	}
	return false
}

// TestDeleteNodeConfig tests the deleteNodeInfoFromConfigmap function.
func TestDeleteNodeConfig(t *testing.T) {
	// Arrange
	nodesToDelete := []map[string]string{
		{
			"managementNetworkIP": "***********",
			"IncorrectField":      "..",
			// ... other fields as needed
		},
	}

	// Act
	err := deleteNodeInfoFromConfigmap(nodesToDelete)

	// Assert
	if err != nil {
		t.Errorf("DeleteNodeInfoFromConfigmap() error = %v, wantErr %v", err, false)
	}

	// Verify the ConfigMap was updated with the nodes removed.
	client, _ := k8s.NewK8sClient()
	cm, err := client.ConfigMap(constants.KubepilotNodeInfoConfigMapNamespace).
		Get(context.TODO(), constants.KubepilotNodeInfoConfigConfigMapName, metav1.GetOptions{})
	if err != nil {
		t.Fatalf("Failed to get ConfigMap: %v", err)
	}

	// Unmarshal the existing data.
	var existingNodes []map[string]string
	if err := yaml.Unmarshal([]byte(cm.Data[constants.KubepilotNodeInfoConfigMapDataName]), &existingNodes); err != nil {
		t.Fatalf("failed to unmarshal existing node data: %v", err)
	}

	// Check if the nodes were deleted.
	for _, node := range nodesToDelete {
		if containsNode(existingNodes, node) {
			t.Errorf("Node was not deleted: %v", node)
		}
	}
}
