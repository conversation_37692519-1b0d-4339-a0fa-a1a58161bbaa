package kubeadm

import (
	"fmt"
	"os"
	"testing"

	utils "gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/decoder"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"k8s.io/klog/v2"
)

var (
	testClusterfile = `apiVersion: bingokube.bingosoft.net/v1beta1
kind: KubeadmConfig
metadata:
  name: default-kubernetes-config
spec:
  localAPIEndpoint:
    advertiseAddress: *************
    bindPort: 6443
  nodeRegistration:
    criSocket: /var/run/dockershim.sock
  kubernetesVersion: v1.22.22
  controlPlaneEndpoint: "apiserver.cluster.local:6443"
  imageRepository: sea.hub:5000/library
  networking:
    podSubnet: **********/10
    serviceSubnet: *********/22
  apiServer:
    certSANs:
      - bingokube.local
      - 127.0.0.1
      - Partial.custom.config
  clusterDomain: cluster.local
  nodeLeaseDurationSeconds: 99
  nodeStatusReportFrequency: 99s
  nodeStatusUpdateFrequency: 99s
---
apiVersion: bingokube.bingosoft.net/v1beta1
kind: Cluster
metadata:
  name: default-kubernetes-cluster
spec:
  image: kubernetes:v1.22.22
---
apiVersion: bingokube.bingosoft.net/v1beta1
kind: Infra
metadata:
  name: alicloud
spec:
  provider: ALI_CLOUD
  ssh:
    passwd: xxx
    port: 2222
  hosts:
    - count: 3
      role: [ master ]
      cpu: 4
      memory: 4
      systemDisk: 100
      dataDisk: [ 100,200 ]
    - count: 3
      role: [ node ]
      cpu: 4
      memory: 4
      systemDisk: 100
      dataDisk: [ 100, 200 ]
---
apiVersion: kubelet.config.k8s.io/v1beta1
kind: KubeletConfiguration
authentication:
  anonymous:
    enabled: false
  webhook:
    cacheTTL: 2m0s
    enabled: true
  x509:
    clientCAFile: /etc/kubernetes/pki/ca.crt
authorization:
  mode: Webhook
  webhook:
    cacheAuthorizedTTL: 5m0s
    cacheUnauthorizedTTL: 30s
cgroupsPerQOS: true
clusterDomain: cluster.local
configMapAndSecretChangeDetectionStrategy: Watch
containerLogMaxFiles: 5
containerLogMaxSize: 10Mi
contentType: application/vnd.kubernetes.protobuf
cpuCFSQuota: true
cpuCFSQuotaPeriod: 100ms
cpuManagerPolicy: none
cpuManagerReconcilePeriod: 10s
enableControllerAttachDetach: true
enableDebuggingHandlers: true
enforceNodeAllocatable:
  - pods
eventBurst: 10
eventRecordQPS: 5
evictionHard:
  imagefs.available: 15%
  memory.available: 100Mi
  nodefs.available: 10%
  nodefs.inodesFree: 5%
evictionPressureTransitionPeriod: 5m0s
failSwapOn: true
fileCheckFrequency: 20s
hairpinMode: promiscuous-bridge
healthzBindAddress: 127.0.0.1
healthzPort: 10248
httpCheckFrequency: 20s
imageGCHighThresholdPercent: 85
imageGCLowThresholdPercent: 80
imageMinimumGCAge: 2m0s
iptablesDropBit: 15
iptablesMasqueradeBit: 14
kubeAPIBurst: 10
kubeAPIQPS: 5
makeIPTablesUtilChains: true
maxOpenFiles: 1000000
maxPods: 110
nodeLeaseDurationSeconds: 40
nodeStatusReportFrequency: 10s
nodeStatusUpdateFrequency: 10s
oomScoreAdj: -999
podPidsLimit: -1
port: 10250
registryBurst: 10
registryPullQPS: 5
rotateCertificates: true
runtimeRequestTimeout: 2m0s
serializeImagePulls: true
staticPodPath: /etc/kubernetes/manifests
streamingConnectionIdleTimeout: 4h0m0s
syncFrequency: 1m0s
volumeStatsAggPeriod: 1m0s
---
apiVersion: kubeadm.k8s.io/v1beta3
kind: ClusterConfiguration
networking:
  podSubnet: **********/10
  serviceSubnet: *********/22
apiServer:
  certSANs:
    - default.raw.config
---
apiVersion: kubeadm.k8s.io/v1beta3
kind: InitConfiguration
localAPIEndpoint:
  advertiseAddress: 127.0.0.1 
  bindPort: 6443
nodeRegistration:
  criSocket: /var/run/dockershim.sock`
)

func TestKubeadmConfig_LoadFromClusterfile(t *testing.T) {
	type fields struct {
		KubeConfig *KubeadmConfig
	}
	type args struct {
		kubeadmconfig []byte
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "test kubeadm config from Clusterfile",
			fields: fields{&KubeadmConfig{}},
			args: args{
				[]byte(testClusterfile),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			k := tt.fields.KubeConfig
			testfile := "test-Clusterfile"
			err := os.WriteFile(testfile, tt.args.kubeadmconfig, 0644)
			if err != nil {
				t.Errorf("WriteFile %s error = %v, wantErr %v", testfile, err, tt.wantErr)
			}
			defer func() {
				err = os.Remove(testfile)
				if err != nil {
					t.Errorf("Remove %s error = %v, wantErr %v", testfile, err, tt.wantErr)
				}
			}()
			KubeadmConfig, err := LoadKubeadmConfigs(testfile, utils.DecodeCRDFromFile)
			if err != nil {
				t.Errorf("err: %v", err)
				return
			}
			if err := k.LoadFromClusterfile(KubeadmConfig); (err != nil) != tt.wantErr {
				t.Errorf("LoadFromClusterfile() error = %v, wantErr %v", err, tt.wantErr)
			}
			klog.Infof("k.InitConfiguration.Kind: %v", k.InitConfiguration.Kind)
			out, err := file.MarshalWithDelimiter(k.InitConfiguration, k.ClusterConfiguration,
				k.JoinConfiguration, k.KubeletConfiguration, k.KubeProxyConfiguration)
			if (err != nil) != tt.wantErr {
				t.Errorf("MarshalConfigsToYaml() error = %v, wantErr %v", err, tt.wantErr)
			}
			fmt.Println(string(out))
		})
	}
}
