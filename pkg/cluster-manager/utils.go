package clustermanager

import (
	"fmt"
	"net"
	"strings"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/rw"
)

// RemoteCertCmd 远程证书管理
func RemoteCertCmd(altNames []string, hostIP net.IP, hostName, serviceCIRD, DNSDomain string) string {
	cmd := "pilotctl certs gen "
	if hostIP != nil {
		cmd += fmt.Sprintf(" --node-ip %s", hostIP.String())
	}

	if hostName != "" {
		cmd += fmt.Sprintf(" --node-name %s", hostName)
	}

	if serviceCIRD != "" {
		cmd += fmt.Sprintf(" --service-cidr %s", serviceCIRD)
	}

	if DNSDomain != "" {
		cmd += fmt.Sprintf(" --dns-domain %s", DNSDomain)
	}

	for _, name := range append(altNames, constants.APIServerDomain) {
		if name != "" {
			cmd += fmt.Sprintf(" --alt-names %s", name)
		}
	}

	return cmd
}

func IsInContainer() bool {
	data, err := rw.NewFileReader("/proc/1/environ").ReadAll()
	if err != nil {
		return false
	}
	return strings.Contains(string(data), "container=docker")
}
