package exec

import (
	"context"
	"fmt"
	"net"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/ssh"

	"golang.org/x/sync/errgroup"
)

type Exec struct {
	cluster *ctv1beta1.Cluster
	ipList  []net.IP
}

func NewExecCmd(cluster *ctv1beta1.Cluster, ipList []net.IP) Exec {
	return Exec{cluster: cluster, ipList: ipList}
}

func (e *Exec) RunCmd(cmd string) error {
	eg, _ := errgroup.WithContext(context.Background())
	for _, ipAddr := range e.ipList {
		ip := ipAddr
		eg.Go(func() error {
			sshClient, sshErr := ssh.NewStdoutSSHClient(ip, e.cluster)
			if sshErr != nil {
				return sshErr
			}
			err := sshClient.CmdAsync(ip, nil, cmd)
			if err != nil {
				return err
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return fmt.Errorf("failed to exec command (%s): %v", cmd, err)
	}
	return nil
}
