package exec

import (
	"testing"
)

func Test_RunCmd(t *testing.T) {
	type args struct {
		cmd string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			"test run cmd",
			args{
				"mkdir -p /var/lib/kubepilot/data/my-cluster/rootfs/test.yaml",
			},
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := Exec{}
			if err := e.RunCmd(tt.args.cmd); (err != nil) != tt.wantErr {
				t.Errorf("Run() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
