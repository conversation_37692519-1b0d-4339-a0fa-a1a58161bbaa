package template

import (
	"bytes"
	"testing"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/ipvs"
)

// clusterfile模板
const clusterfileTemplate = `
# 计算master节点数量
{{- define "masterCount" -}}
{{- $count := 0 -}}
{{- range .Values.nodes }}
  {{- if eq .role "master" }}
    {{- $count = add $count 1 -}}
  {{- end }}
{{- end }}
{{- $count -}}
{{- end -}}

# 定义isNVSCNI变量
{{- $isNVSCNI := .Values.network.cni | eq "nvs" }}

apiVersion: bingokube.bingosoft.net/v1beta1
kind: Cluster
metadata:
  name: {{ .Values.clusterName }}
spec:
  env:
# ===内置参数 begin ===
    - nodes={{ toJson .Values.nodes }}
# ===内置参数 end ===
# ===网络相关参数 begin ===
    - vip={{ .Values.network.vip }}
	{{- if not $isNVSCNI }}
    - flannelPodCidr={{ .Values.network.podCidr }}
    {{- end }}
# ===网络相关参数 end ===
    {{- if $isNVSCNI }}
# ===nvs相关参数 begin ===
    - kubeNvsOuterPoolCidr={{ .Values.nvs.ipPool.cidr }}
    - kubeNvsOuterPoolGateway={{ .Values.nvs.ipPool.gateway }}
    - kubeNvsOuterPoolVlan={{ .Values.nvs.ipPool.vlan }}
    - kubeNvsOuterPoolIPRanges={{ toJson .Values.nvs.ipPool.ipRange }}
    - kubeNvsDefaultVNCidr={{ .Values.nvs.businessNetwork.cidr }}
    - kubeNvsDefaultVNGateway={{ .Values.nvs.businessNetwork.gateway }}
    - kubeNvsSystemVNCidr={{ .Values.nvs.managerNetwork.cidr }}
    - kubeNvsSystemVNGateway={{ .Values.nvs.managerNetwork.gateway }}
    - kubeNvsNetworkMode={{ .Values.nvs.networkMode }}
    - kubeNvsSystemVnOuterIP={{ .Values.SystemVnOuterIP }}
# ===nvs相关参数 begin ===
    {{- end }}
# ===etcdCluster start ===
    - etcdClusterReplicaCount={{ template "masterCount" . }}
# ===etcdCluster end ===
# ===kubeverse start ===
    - kubeverseReplicaCount=1
    - kubeverseKubeLb={{ $isNVSCNI }}
    - kubeverseVip={{ .Values.verseVip }}
    - kubeverseVipPool=ip-pool-default
# ===kubeverse end ===
# ===kubedupont begin ===
    - highAvailPolicyModule=false
    - tenantModule=true
    - kubeNvsModule={{ $isNVSCNI }}
# ===kubedupont end ===
  hosts:
    {{- range .Values.nodes }}
    - ips:
        - {{ .managementNetworkIP }}
      {{- if eq .role "master" }}
      roles: ["master"]
      {{- else -}}
      roles: ["worker"]
      {{- end }}
      ssh:
        port: {{ default 22 .Values.ssh.port }}
        user: {{ default "root" .Values.ssh.user }}
        passwd: {{ .Values.ssh.passwd }}
    {{- end }}
  image: registry.bingosoft.net/bingokube/cluster-image/k8s-containerd:v1.22.25-tenant
  components:
    - registry.bingosoft.net/bingokube/cluster-image/helm:develop-v1.0.0
    {{ if eq .Values.network.cni "flannel" -}}
    - registry.bingosoft.net/bingokube/cluster-image/flannel:v1.0.0
    {{- else -}}
    - registry.bingosoft.net/bingokube/cluster-image/kube-nvs:develop-v1.0.0-test
    {{- end }}
    - registry.bingosoft.net/bingokube/cluster-image/kubedupont:v1.0.0-t1-1119
    - registry.bingosoft.net/bingokube/cluster-image/etcd-cluster:v0.2.0
    - registry.bingosoft.net/bingokube/cluster-image/kubeverse:develop-v1.0.0
  ssh:
    port: {{ default 22 .Values.ssh.port }}
    user: {{ default "root" .Values.ssh.user }}
    passwd: {{ .Values.ssh.passwd }}
`

func TestTemplateFlannelCluster(t *testing.T) {
	config := map[string]interface{}{
		"clusterName": "kube-cluster",
		"nodes": []map[string]interface{}{
			{
				"managementNetworkIP":        "*************",
				"managementNetworkInterface": "enp1s0",
				"role":                       "master",
				"passwd":                     "bingo@word1",
				"port":                       22,
				"user":                       "root",
			},
		},
		"ssh": map[string]interface{}{
			"passwd": "bingo@word1",
			"port":   22,
			"user":   "root",
		},
		"network": map[string]interface{}{
			"vip":     "*************",
			"cni":     "flannel",
			"podCidr": "**********/10",
		},
	}

	data := map[string]interface{}{
		"Values": config,
	}

	out := bytes.NewBuffer(nil)
	tpl, isOk, err := TryParse(clusterfileTemplate)
	if isOk {
		if err != nil {
			t.Errorf("TryParse err:%+v", err)
		}
		if err = tpl.Execute(out, data); err != nil {
			t.Errorf("tpl.Execute err: %+v", err)
		}
	}
	t.Log(out.String())
}

func TestTemplateNvsCluster(t *testing.T) {
	config := map[string]interface{}{
		"clusterName": "kube-cluster",
		"nodes": []map[string]interface{}{
			{
				"managementNetworkIP":        "*************",
				"managementNetworkInterface": "enp1s0",
				"uplinkIP":                   "**********",
				"uplinkNic":                  "enp2s0",
				"innerNicIP":                 "**********",
				"innerNic":                   "enp10s0",
				"role":                       "master",
			},
		},
		"ssh": map[string]interface{}{
			"passwd": "bingo@word1",
			"port":   22,
			"user":   "root",
		},
		"network": map[string]interface{}{
			"vip": "*************",
			"cni": "nvs",
		},
		"nvs": map[string]interface{}{
			"networkMode": "businessDataFusion",
			"managerNetwork": map[string]interface{}{
				"cidr":    "**********/16",
				"gateway": "************",
			},
			"businessNetwork": map[string]interface{}{
				"cidr":    "**********/16",
				"gateway": "***********",
			},
			"ipPool": map[string]interface{}{
				"vlan":    0,
				"gateway": "*************",
				"cidr":    "***********/21",
				"ipRange": []string{
					"*************-*************",
					"*************-*************",
					"*************-*************",
				},
			},
		},
		"verseVip":        "*************",
		"SystemVnOuterIP": "*************",
	}

	data := map[string]interface{}{
		"Values": config,
	}

	out := bytes.NewBuffer(nil)
	tpl, isOk, err := TryParse(clusterfileTemplate)
	if isOk {
		if err != nil {
			t.Errorf("TryParse err:%+v", err)
		}
		if err = tpl.Execute(out, data); err != nil {
			t.Errorf("tpl.Execute err: %+v", err)
		}
	}
	t.Log(out.String())
}

func TestIpvsRulesTemplate(t *testing.T) {
	config := map[string]interface{}{
		"ipvsRules": []map[string]string{
			{
				"name":             "registry",
				"servicePort":      "5000",
				"destinationPort":  "5000",
				"forwardingMethod": "local",
			},
			{
				"name":             "apiserver",
				"servicePort":      "6443",
				"destinationPort":  "6443",
				"forwardingMethod": "local",
			},
			{
				"name":                        "kubeverse-web",
				"servicePort":                 "8989",
				"destinationPort":             "8989",
				"enabledPersistentConnection": "true",
				"timeout":                     "10800",
			},
			{
				"name":            "uam-web",
				"servicePort":     "8566",
				"destinationPort": "8566",
			},
			{
				"name":            "uam-dex",
				"servicePort":     "5554",
				"destinationPort": "5554",
			},
			{
				"name":            "uam-grpc-server",
				"servicePort":     "8567",
				"destinationPort": "8567",
			},
			{
				"name":            "ingress-nginx-http",
				"servicePort":     "80",
				"destinationPort": "180",
			},
			{
				"name":            "ingress-nginx-https",
				"servicePort":     "443",
				"destinationPort": "1443",
			},
		},
	}

	out := bytes.NewBuffer(nil)
	tpl, err := Parse(ipvs.IpvsRulesTemplate) // Changed from TryParse to Parse
	if err != nil {
		t.Errorf("Parse err:%+v", err)
	}
	if err = tpl.Execute(out, config); err != nil {
		t.Errorf("tpl.Execute err: %+v", err)
	}
	t.Log(out.String())
}
