package template

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestToUnescapedJSON(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected string
	}{
		{
			name:     "简单字符串",
			input:    "hello world",
			expected: "hello world",
		},
		{
			name:     "包含引号的字符串",
			input:    `hello "world"`,
			expected: `hello "world"`,
		},
		{
			name:     "包含转义字符的字符串",
			input:    "hello\nworld",
			expected: "hello\nworld",
		},
		{
			name:     "简单数字",
			input:    123,
			expected: "123",
		},
		{
			name:     "布尔值",
			input:    true,
			expected: "true",
		},
		{
			name: "简单对象",
			input: map[string]string{
				"key": "value",
			},
			expected: `{"key":"value"}`,
		},
		{
			name: "嵌套对象",
			input: map[string]interface{}{
				"string": "value",
				"number": 123,
				"object": map[string]string{
					"nested": "data",
				},
			},
			expected: `{"number":123,"object":{"nested":"data"},"string":"value"}`,
		},
		{
			name:     "数组",
			input:    []string{"a", "b", "c"},
			expected: `["a","b","c"]`,
		},
		{
			name:     "nil值",
			input:    nil,
			expected: "null",
		},
		{
			name: "特殊字符",
			input: map[string]string{
				"key1": "6Z$^7Z)#c&%6@",
			},
			expected: `{"key1":"6Z$^7Z)#c&%6@"}`,
		},
		{
			name: "复杂的节点信息数组",
			input: []map[string]string{
				{
					"businessNetworkInterface":   "enp125s0f0",
					"dataNetworkIP":              "************",
					"dataNetworkInterface":       "enp125s0f0",
					"managementNetworkIP":        "************",
					"managementNetworkInterface": "enp125s0f0",
					"role":                       "master",
					"passwd":                     "6Z$^7Z)#c&%6@",
				},
			},
			expected: `[{"businessNetworkInterface":"enp125s0f0","dataNetworkIP":"************","dataNetworkInterface":"enp125s0f0","managementNetworkIP":"************","managementNetworkInterface":"enp125s0f0","passwd":"6Z$^7Z)#c&%6@","role":"master"}]`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := toUnescapedJSON(tt.input)
			assert.Equal(t, tt.expected, result,
				"toRawJSON(%v) = %v; 期望 %v", tt.input, result, tt.expected)
		})
	}
}
