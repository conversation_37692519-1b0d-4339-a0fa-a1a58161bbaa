package template

// nosemgrep: go.lang.security.audit.xss.import-text-template.import-text-template
import (
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"net"
	"strings"
	"text/template"

	"github.com/BurntSushi/toml"
	"github.com/Masterminds/sprig/v3"
	"sigs.k8s.io/yaml"
)

// funcMap returns a mapping of all of the functions that Engine has.
func funcMap() template.FuncMap {
	f := sprig.TxtFuncMap()
	// delete(f, "env")
	// delete(f, "expandenv")

	// Add some extra functionality
	extra := template.FuncMap{
		"toToml":          toTOML,
		"toYaml":          toYAML,
		"fromYaml":        fromYAML,
		"fromYamlArray":   fromYAMLArray,
		"toJson":          toJSON,
		"toUnescapedJson": toUnescapedJSON,
		"fromJson":        fromJSON,
		"fromJsonArray":   fromJSONArray,
		"ipNet":           ipNet,
		"ipAt":            ipAt,
	}

	for k, v := range extra {
		f[k] = v
	}

	return f
}

// toYAM<PERSON> takes an interface, marshals it to yaml, and returns a string. It will
// always return a string, even on marshal error (empty string).
//
// This is designed to be called from a template.
func toYAML(v interface{}) string {
	data, err := yaml.Marshal(v)
	if err != nil {
		// Swallow errors inside of a template.
		return ""
	}
	return strings.TrimSuffix(string(data), "\n")
}

// fromYAML converts a YAML document into a map[string]interface{}.
//
// This is not a general-purpose YAML parser, and will not parse all valid
// YAML documents. Additionally, because its intended use is within templates
// it tolerates errors. It will insert the returned error message string into
// m["Error"] in the returned map.
func fromYAML(str string) map[string]interface{} {
	m := map[string]interface{}{}

	if err := yaml.Unmarshal([]byte(str), &m); err != nil {
		m["Error"] = err.Error()
	}
	return m
}

// fromYAMLArray converts a YAML array into a []interface{}.
//
// This is not a general-purpose YAML parser, and will not parse all valid
// YAML documents. Additionally, because its intended use is within templates
// it tolerates errors. It will insert the returned error message string as
// the first and only item in the returned array.
func fromYAMLArray(str string) []interface{} {
	a := []interface{}{}

	if err := yaml.Unmarshal([]byte(str), &a); err != nil {
		a = []interface{}{err.Error()}
	}
	return a
}

// toTOML takes an interface, marshals it to toml, and returns a string. It will
// always return a string, even on marshal error (empty string).
//
// This is designed to be called from a template.
func toTOML(v interface{}) string {
	b := bytes.NewBuffer(nil)
	e := toml.NewEncoder(b)
	if err := e.Encode(v); err != nil {
		return err.Error()
	}
	return b.String()
}

// toJSON takes an interface, marshals it to json, and returns a string. It will
// always return a string, even on marshal error (empty string).
//
// This is designed to be called from a template.
func toJSON(v interface{}) string {
	data, err := json.Marshal(v)
	if err != nil {
		// Swallow errors inside of a template.
		return ""
	}
	return string(data)
}

// toUnescapedJSON 将输入转换为未转义的 JSON 字符串。
// 处理规则：
// 1. 如果输入是有效的 JSON 字符串，解析并重新序列化为未转义的 JSON
// 2. 如果输入是普通字符串，直接返回原值
// 3. 其他类型（如数组、结构体、Map）序列化为未转义的 JSON
func toUnescapedJSON(v interface{}) string {
	// 创建一个不转义 HTML 的 JSON 编码器
	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	encoder.SetEscapeHTML(false) // 关键：关闭 HTML 转义

	// 检查输入是否为字符串类型
	if str, ok := v.(string); ok {
		var parsed interface{}
		// 尝试将字符串解析为 JSON 值
		if err := json.Unmarshal([]byte(str), &parsed); err == nil {
			// 解析成功：将解析后的值重新序列化为未转义的 JSON
			if err := encoder.Encode(parsed); err != nil {
				return str // 编码失败时返回原始字符串
			}
			return strings.TrimSuffix(buf.String(), "\n")
		}
		// 不是有效的 JSON，返回原始字符串
		return str
	}

	// 处理非字符串类型
	if err := encoder.Encode(v); err != nil {
		// 序列化失败时，尝试转换为字符串后返回
		if str, ok := v.(fmt.Stringer); ok {
			return str.String()
		}
		return fmt.Sprint(v)
	}

	return strings.TrimSuffix(buf.String(), "\n")
}

// fromJSON converts a JSON document into a map[string]interface{}.
//
// This is not a general-purpose JSON parser, and will not parse all valid
// JSON documents. Additionally, because its intended use is within templates
// it tolerates errors. It will insert the returned error message string into
// m["Error"] in the returned map.
func fromJSON(str string) map[string]interface{} {
	m := make(map[string]interface{})

	if err := json.Unmarshal([]byte(str), &m); err != nil {
		m["Error"] = err.Error()
	}
	return m
}

// fromJSONArray converts a JSON array into a []interface{}.
//
// This is not a general-purpose JSON parser, and will not parse all valid
// JSON documents. Additionally, because its intended use is within templates
// it tolerates errors. It will insert the returned error message string as
// the first and only item in the returned array.
func fromJSONArray(str string) []interface{} {
	a := []interface{}{}

	if err := json.Unmarshal([]byte(str), &a); err != nil {
		a = []interface{}{err.Error()}
	}
	return a
}

func ipNet(s string) string {
	_, ipv4Net, err := net.ParseCIDR(s)
	if err != nil {
		return fmt.Sprintf("invalid cidr: %s", err)
	}
	return ipv4Net.String()
}

func ipAt(s string, idx uint32) string {
	_, ipv4Net, err := net.ParseCIDR(s)
	if err != nil {
		return fmt.Sprintf("invalid cidr: %s", err)
	}
	mask := binary.BigEndian.Uint32(ipv4Net.Mask)
	start := binary.BigEndian.Uint32(ipv4Net.IP)
	finish := (start & mask) | (mask ^ 0xffffffff)
	if start+idx > finish {
		return "out of ip address range"
	}
	ip := make(net.IP, 4)
	binary.BigEndian.PutUint32(ip, start+idx)
	return ip.String()
}
