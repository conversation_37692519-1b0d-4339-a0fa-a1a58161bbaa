package infra

import (
	"fmt"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infra/container"
)

type Interface interface {
	// Apply IAAS resources and save metadata info like vpc instance id to cluster status
	// https://github.com/fanux/sealgate/tree/master/cloud
	Apply() error
}

func NewDefaultProvider(cluster *v1beta1.Cluster) (Interface, error) {
	return nil, nil
}

func NewContainerProvider(cluster *v1beta1.Cluster) (Interface, error) {
	if container.IsDockerAvailable() {
		return nil, fmt.Errorf("please install docker on your system")
	}

	cli, err := container.NewClientWithCluster(cluster)
	if err != nil {
		return nil, fmt.Errorf("new container client failed")
	}

	return cli, nil
}
