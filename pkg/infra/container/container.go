package container

import (
	"fmt"
	"net"
	"os"

	ctv1beta1 "gitlab.bingosoft.net/bingokube/kubepilot/pkg/apis/cluster/v1beta1"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infra/container/client"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infra/container/client/docker"
	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/file"
	"k8s.io/klog/v2"
)

const (
	CONTAINER         = "CONTAINER"
	DockerHost        = "/var/run/docker.sock"
	DefaultPassword   = "bingo@word1"
	MASTER            = "master"
	NODE              = "node"
	ChangePasswordCmd = "echo root:%s | chpasswd" // #nosec
	// for check rootless docker in info.SecurityOptions
	RootlessDocker = "name=rootless"
	// for check overlay2 StorageDriver in info.StorageDriver
	Overlay2 = "overlay2"
)

type ApplyProvider struct {
	Cluster  *ctv1beta1.Cluster
	Provider client.ProviderService
}

type ApplyResult struct {
	ToJoinNumber   int
	ToDeleteIPList []net.IP
	Role           string
}

func (a *ApplyProvider) Apply() error {
	return nil
}

func (a *ApplyProvider) CheckServerInfo() error {
	/*
		1,rootless docker:do not support rootless docker currently.if support, CgroupVersion must = 2
		2,StorageDriver:overlay2
		3,cpu num >1
		4,docker host : /var/run/docker.sock. set env DOCKER_HOST to override
	*/
	info, err := a.Provider.GetServerInfo()
	if err != nil {
		return fmt.Errorf("failed to get docker server, please check docker server running status")
	}

	for _, opt := range info.SecurityOptions {
		if opt == RootlessDocker {
			return fmt.Errorf("do not support rootless docker currently")
		}
	}

	if info.StorageDriver != Overlay2 {
		return fmt.Errorf("only support storage driver overlay2 ,but current is :%s", info.StorageDriver)
	}

	if info.CPUNumber <= 1 {
		return fmt.Errorf("cpu number of docker server must greater than 1 ,but current is :%d", info.CPUNumber)
	}

	if !info.MemoryLimit || !info.PidsLimit || !info.CPUShares {
		return fmt.Errorf("requires setting systemd property \"Delegate=yes\"")
	}

	if !file.IsFileExist(DockerHost) && os.Getenv("DOCKER_HOST") == "" {
		return fmt.Errorf("kubepilot user default docker host /var/run/docker.sock, please set env DOCKER_HOST='' to override it")
	}

	return nil
}

func (a *ApplyProvider) ReconcileContainer() error {
	return nil
}

func (a *ApplyProvider) applyResult(result *ApplyResult) error {
	return nil
}

func (a *ApplyProvider) applyToJoin(toJoinNumber int, role string) ([]net.IP, error) {
	return nil, nil
}

func (a *ApplyProvider) changeDefaultPasswd(containerIP net.IP) error {
	return nil
}

func (a *ApplyProvider) applyToDelete(deleteIPList []net.IP) error {
	// delete container and return deleted ip list
	for _, ip := range deleteIPList {
		id, ok := a.Cluster.Annotations[ip.String()]
		if !ok {
			klog.Warningf("failed to delete container %s", ip)
			continue
		}
		err := a.Provider.RmContainer(id)
		if err != nil {
			return fmt.Errorf("failed to delete container:%s", id)
		}
		delete(a.Cluster.Annotations, ip.String())
	}
	return nil
}

func (a *ApplyProvider) CleanUp() error {
	return nil
}

func NewClientWithCluster(cluster *ctv1beta1.Cluster) (*ApplyProvider, error) {
	p, err := docker.NewDockerProvider()
	if err != nil {
		return nil, err
	}

	return &ApplyProvider{
		Cluster:  cluster,
		Provider: p,
	}, nil
}
