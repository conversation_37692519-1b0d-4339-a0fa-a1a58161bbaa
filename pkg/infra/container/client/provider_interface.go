package client

import (
	"github.com/docker/docker/api/types/mount"
)

type ProviderService interface {
	GetServerInfo() (*DockerInfo, error)
	RunContainer(opts *CreateOptsForContainer) (string, error)
	GetContainerInfo(containerID string, networkName string) (*Container, error)
	RmContainer(containerID string) error
	PullImage(imageName string) (string, error)
}

type Container struct {
	ContainerID       string
	NetworkID         string
	ContainerName     string
	ContainerHostName string
	ContainerIP       string
	Status            string
	ContainerLabel    map[string]string
}

type CreateOptsForContainer struct {
	ImageName         string
	NetworkName       string
	ContainerName     string
	ContainerHostName string
	ContainerLabel    map[string]string
	Mount             []mount.Mount
}

type DockerInfo struct {
	CgroupDriver    string
	CgroupVersion   string
	StorageDriver   string
	MemoryLimit     bool
	PidsLimit       bool
	CPUShares       bool
	CPUNumber       int
	SecurityOptions []string
}
