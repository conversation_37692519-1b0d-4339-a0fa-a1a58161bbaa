package docker

import (
	"context"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/infra/container/client"

	dc "github.com/docker/docker/client"
)

type Provider struct {
	DockerClient *dc.Client
	Ctx          context.Context
}

func NewDockerProvider() (client.ProviderService, error) {
	ctx := context.Background()
	cli, err := dc.NewClientWithOpts(dc.FromEnv, dc.WithAPIVersionNegotiation())
	if err != nil {
		return nil, err
	}
	return &Provider{
		Ctx:          ctx,
		DockerClient: cli,
	}, nil
}
