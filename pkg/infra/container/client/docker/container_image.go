package docker

import (
	"fmt"
	"io"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/constants"
	"k8s.io/klog/v2"

	dockerstreams "github.com/docker/cli/cli/streams"
	"github.com/docker/docker/api/types"
	dockerjsonmessage "github.com/docker/docker/pkg/jsonmessage"
)

func (p *Provider) DeleteImageResource(imageID string) error {
	_, err := p.DockerClient.ImageRemove(p.Ctx, imageID, types.ImageRemoveOptions{
		Force:         true,
		PruneChildren: true,
	})
	return err
}

func (p *Provider) PullImage(imageName string) (string, error) {
	// if existed, only set id no need to pull.
	if imageID := p.GetImageIDByName(imageName); imageID != "" {
		return imageID, nil
	}
	out, err := p.DockerClient.ImagePull(p.Ctx, imageName, types.ImagePullOptions{})
	if err != nil {
		return "", err
	}

	defer func() {
		_ = out.Close()
	}()

	err = dockerjsonmessage.DisplayJSONMessagesToStream(out, dockerstreams.NewOut(constants.StdOut), nil)
	if err != nil && err != io.ErrClosedPipe {
		klog.Warningf("error occurs in display progressing, err: %s", err)
	}
	klog.Infof("success to pull docker image: %s", imageName)

	imageID := p.GetImageIDByName(imageName)
	if imageID != "" {
		return imageID, nil
	}

	return "", fmt.Errorf("failed to pull image:%s", imageName)
}

func (p *Provider) GetImageIDByName(name string) string {
	images, err := p.DockerClient.ImageList(p.Ctx, types.ImageListOptions{})
	if err != nil {
		return ""
	}
	for _, ima := range images {
		named := ima.RepoTags
		for _, imaName := range named {
			if imaName == name {
				return ima.ID
			}
		}
	}
	return ""
}

func (p *Provider) GetImageResourceByID(id string) (*types.ImageInspect, error) {
	image, _, err := p.DockerClient.ImageInspectWithRaw(p.Ctx, id)
	return &image, err
}
