package container

import (
	"crypto/rand"
	"fmt"
	"strings"

	"gitlab.bingosoft.net/bingokube/kubepilot/pkg/utils/exec"
)

func IsDockerAvailable() bool {
	lines, err := exec.RunSimpleCmd("docker -v")
	if err != nil || len(lines) != 1 {
		return false
	}
	return strings.Contains(lines, "docker version")
}

func GenUniqueID(n int) string {
	randBytes := make([]byte, n/2)
	if _, err := rand.Read(randBytes); err != nil {
		return ""
	}
	return fmt.Sprintf("%x", randBytes)
}
