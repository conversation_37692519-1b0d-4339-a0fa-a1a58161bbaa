package certs

import (
	"net"
	"testing"
)

func TestGenerateAll(t *testing.T) {
	basePath := "/tmp/kubernetes/pki"
	etcdBasePath := "/tmp/kubernetes/pki/etcd"
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			"generate all certs",
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := GenerateAllKubernetesCerts(basePath, etcdBasePath, "master1", "*********/10", "cluster.local", []string{"test.com", "***********", "kubernetes.default.svc.sealyun"}, net.ParseIP("*************")); (err != nil) != tt.wantErr {
				t.Errorf("GenerateAll() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUpdateAPIServerCert(t *testing.T) {
	tests := []struct {
		pkiPath  string
		certSans []string
		name     string
		wantErr  bool
	}{
		{"/tmp/kubernetes/pki",
			[]string{"kaka.com", "apiserver.cluster.local", "*************"},
			"Update APIServer Cert sans",
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := UpdateAPIServerCertSans(tt.pkiPath, tt.certSans); (err != nil) != tt.wantErr {
				t.Errorf("UpdateAPIServerCert() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
