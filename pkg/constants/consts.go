package constants

import (
	"os"
)

var Logo = `
      __ __      __               _ __      __ 
     / //___  __/ /_  ___  ____  (_/ ____  / /_
    / ,< / / / / __ \/ _ \/ __ \/ / / __ \/ __/
   / /| / /_/ / /_/ /  __/ /_/ / / / /_/ / /_  
  /_/ |_\__,_/_.___/\___/ .___/_/_/\____/\__/  
                       /_/
            
       Website: bingokube.bingosoft.net
       Version: %s

`

var (
	ClientName = "kubepilot"
)

var (
	StdOut = os.Stdout
	StdInt = os.Stdin
	StdErr = os.Stderr
)

const (
	Docker     = "docker"
	Containerd = "containerd"
)
const (
	DefaultDockerCRISocket     = "/var/run/dockershim.sock"
	DefaultCgroupDriver        = "systemd"
	DefaultDockerCertsDir      = "/etc/docker/certs.d"
	DefaultContainerdCRISocket = "/run/containerd/containerd.sock"
	DefaultContainerdCertsDir  = "/etc/containerd/certs.d"
	DockerConfigFileName       = "config.json"
)

const (
	K0s string = "k0s"
	K3s string = "k3s"
	K8s string = "kubernetes"
)

// Default dir and file path
const (
	DefaultLogDir              = "/var/lib/kubepilot/log"
	DefaultKubepilotDataDir    = "/var/lib/kubepilot/data"
	KubeAdminConf              = "/etc/kubernetes/admin.conf"
	ClusterfileName            = "ClusterfileName"
	KubealivedStaticPodName    = "kubealived"
	RegKubealivedStaticPodName = "reg-kubealived"
	IPVSRuleConfigName         = "ipvs-rules"
	IPVSRuleConfigPath         = "/etc/kubernetes/ipvs-rules.yaml"
	StaticPodDir               = "/etc/kubernetes/manifests"
	KubealivedFile             = "kubealived.yaml"
	KubealivedImageName        = "bingokube/kubealived"
	KubealivedImageTag         = "v0.1.0"
	KubealivedServiceAccount   = "kubealived"
	CgroupDriverArg            = "CgroupDriver"
	ProbeAPI                   = "/healthz"
	DefaultKubealivedVipSubnet = "32"
)

// Envs
const (
	EnvHostIP                 = "hostIP"
	EnvHostIPFamily           = "hostIPFamily"
	EnvContainerRuntime       = "containerRuntime"
	EnvIPv6DualStack          = "ipv6DualStack"
	EnvRegistryDomain         = "registryDomain"
	EnvRegistryPort           = "registryPort"
	EnvRegistryURL            = "registryURL"
	EnvLocalRegistryDomain    = "localRegistryDomain"
	EnvLocalRegistryPort      = "localRegistryPort"
	EnvLocalRegistryURL       = "localRegistryURL"
	EnvExternalRegistryDomain = "externalRegistryDomain"
	EnvExternalRegistryPort   = "externalRegistryPort"
	EnvExternalRegistryURL    = "externalRegistryURL"
	EnvExternalRegistryVIP    = "externalRegistryVIP" // 外部仓库(管理集群的仓库)-vip
	EnvCertSANs               = "certSANs"
	EnvClusterDomain          = "clusterDomain"
	EnvInCluster              = "inCluster"
	EnvIPvsVIPForIPv4         = "vip"
	EnvIPvsVIPForIPv6         = "v6VIP"
	EnvSvcCIDR                = "svcCIDR"
	EnvPodCIDR                = "podCIDR"
	EnvDNSSvcIP               = "dnsSvcIP"
	EnvKubeSvcIP              = "kubeSvcIP"
	EnvUseIPasNodeName        = "useIPasNodeName"
	EnvRegistryDataDir        = "registryDataDir"
	EnvEnableKubealived       = "enableKubealived" // 是否部署kubealived组件
	EnvClusterPlatform        = "clusterPlatform"  // 集群架构，格式如：["Linux/arm64","Linux/amd64"]
)

const (
	MasterRoleLabel = "node-role.kubernetes.io/master"
)

type HandleImageMode string

const (
	InstallImage HandleImageMode = "installImage"
	LoadImage    HandleImageMode = "loadImage"
)

// image module
const (
	DefaultMetadataName         = "Metadata"
	DefaultRegistryDomain       = "registry.kube.io"
	DefaultRegistryDataDir      = "/kube/data/registry" // registry默认数据目录
	DefaultRegistryPort         = 5000
	DefaultRegistryURL          = "registry.kube.io:5000"
	DefaultRegistryHtPasswdFile = "registry_htpasswd"
)

// about infra
const (
	RegistryDirName = "registry"
)

// CRD kind
const (
	KubeletConfiguration   = "KubeletConfiguration"
	KubeProxyConfiguration = "KubeProxyConfiguration"
)

// plugin type
const (
	TAINT    = "TAINT"
	LABEL    = "LABEL"
	HOSTNAME = "HOSTNAME"
)

// default cluster runtime configuration
const (
	DefaultVIP = "*************"
	// DefaultVipPort 端口传给kubealived之后会进行分割,然后实现一个VIP有多个Port的ipvs规则.
	// 默认集群是会有自己的registry端口，但有一种情况例外：就是装的是业务集群且使用外部registry的时候，
	// 则不应该有`5000`端口
	DefaultVipPort              = "6443,5000,8989,8566,8567,5554"
	DefaultNetCard              = "enp1s0"
	DefaultKubealivedImgVersion = "v0.1.2"
	DefaultVIPForIPv6           = "0000:0000:0000:0000:0000:ffff:0a10:cb93"
	DefaultAPIserverDomain      = "apiserver.cluster.local"
)

const (
	BAREMETAL = "BAREMETAL"
	AliCloud  = "ALI_CLOUD"
	CONTAINER = "CONTAINER"
)

const (
	FileMode0755 = 0755
	FileMode0644 = 0644
)

const APIServerDomain = "apiserver.cluster.local"

const (
	CdAndExecCmd        = "cd %s && %s"
	CdIfExistAndExecCmd = "if [ ! -d %s ];then exit 0;fi; cd %s && %s"
)

const (
	ExecBinaryFileName = "kubepilot"
	ROOT               = "root"
	WINDOWS            = "windows"
)

const (
	// WhiteoutPrefix means this file is a whiteout(deleted at the merge layer)
	WhiteoutPrefix = ".wh."

	// WhiteoutMetaPrefix prefix means whiteout has a special meaning and is not
	// for removing an actual file. Normally these files are excluded from exported
	// archives.
	WhiteoutMetaPrefix = WhiteoutPrefix + WhiteoutPrefix

	// WhiteoutLinkDir is a directory AUFS uses for storing hardlink links to other
	// layers. Normally these should not go into exported archives and all changed
	// hardlinks should be copied to the top layer.
	WhiteoutLinkDir = WhiteoutMetaPrefix + "plnk"

	// WhiteoutOpaqueDir file means directory has been made opaque - meaning
	// readdir calls to this directory do not follow to lower layers.
	WhiteoutOpaqueDir = WhiteoutMetaPrefix + ".opq"
)

const (
	ClusterKind     = "Cluster"
	PluginKind      = "Plugin"
	ConfigKind      = "Config"
	ApplicationKind = "Application"
)

const (
	ClusterfileConfigMapNamespace = "kube-system"
	ClusterfileConfigMapName      = "kubepilot-clusterfile"
	ClusterfileConfigMapDataName  = "Clusterfile"
)

const (
	OCIManifestDir  = "oci-dir"
	OCIArchive      = "oci-archive"
	V2s2ManifestDir = "docker-dir"
	V2s2Archive     = "docker-archive"
)

type InstallMode string

const (
	InstallWithClusterImage InstallMode = "cluster-image"
	InstallWithClusterfile  InstallMode = "clusterfile"
)

const (
	// EnableARP 是否开启ARP广播包功能
	EnableARP = "enableARP"
	// LeaderElection 是否开启租约分布式锁
	LeaderElection = "enableLeaderElection"
	// LeaseName 租约名
	LeaseName = "leaseName"
	// LeaseDuration 租约持有时长
	LeaseDuration = "leaseDuration"
	// RenewDeadline 租约续租时间
	RenewDeadline = "renewDeadline"
	// RetryPeriod 租约重试时间
	RetryPeriod = "retryPeriod"
	// EnableLB 是否开启LB
	EnableLB = "enableLB"
	// IPVSLB ipvs的LB类型
	IPVSLB = "ipvsLBType"
	// ForwardMethod 转发方式
	ForwardMethod = "forwardMethod"
	// LeaseAnnotations 租约注解
	LeaseAnnotations = "leaseAnnotations"
	// NetCardName 网卡名
	NetCardName = "netCardName"
	// NodesInfo 节点信息
	NodesInfo = "nodes"
	// Address IP地址
	Address = "address"
	// Subnet 子网掩码(网段)
	Subnet = "subnet"
	// Port 端口
	Port = "port"
	// Namespace 命名空间
	Namespace = "namespace"
	// IsMaster 是否在master节点上运行
	IsMaster = "isMaster"
	// InCluster 是否以incluster模式部署
	InCluster         = "inCluster"
	KubealivedImgName = "kubealivedImgName"
	// KubealivedImgVesion 镜像版本
	KubealivedImgVesion      = "kubealivedImgVesion"
	KubealivedSerivceAccount = "kubealivedServiceAccount"
	// KubealivedVipSubnet kubealived vip 子网掩码(网段)
	KubealivedVipSubnet = "KubealivedVipSubnet"
	// ManifestDir 生成manifest文件的目录
	ManifestDir = "manifestDir"
	// Annotions 注解
	Annotions = "annotations"
	// ElectionType 分布式锁类型(Kubernetes租约/etcd 分布式锁)
	ElectionType = "leaderElectionType"
	// IpvsRules ipvs 规则
	IpvsRules = "ipvsRules"
)

type CommandName string

const (
	Run      CommandName = "run"
	Generate CommandName = "generate"
)

// kubernetes
const (
	ConfigName          = "kubeconfig"
	MoutPath            = "/etc/kubernetes/admin.conf"
	HostIP              = "127.0.0.1"
	HostName            = "kubernetes"
	ServiceAccountName  = "kubealived"
	KubeSystemNamespace = "kube-system"
	DefaultQPS          = 100
	DefaultBurst        = 250
)

type SetupMode string

const (
	ARPMode SetupMode = "arp"
	BGPMode SetupMode = "bgp"
)

type LeaderElectionType string

const (
	Kubernetes LeaderElectionType = "kubernetes"
	Etcd       LeaderElectionType = "etcd"
)

// ipvs 负载均衡策略
const (
	// ROUNDROBIN 轮询
	ROUNDROBIN = "rr"
	// LEASTCONN 最少连接数
	LEASTCONN = "lc"
	// DESTINATIONHASH 目的地址哈希
	DESTINATIONHASH = "dh"
	// SOURCEHASH 源地址哈希
	SOURCEHASH = "sh"
	// SHORTESEXPECTEDDELAY 最短期望延时
	SHORTESEXPECTEDDELAY = "sed"
	// NEVERQUEUE 从不排队
	NEVERQUEUE = "nq"
)

var (
	Scheduler = map[string]bool{
		ROUNDROBIN:           true,
		LEASTCONN:            true,
		DESTINATIONHASH:      true,
		SOURCEHASH:           true,
		SHORTESEXPECTEDDELAY: true,
		NEVERQUEUE:           true,
	}

	Forward = map[string]bool{
		"masquerade":  true,
		"local":       true,
		"tunnel":      true,
		"directroute": true,
		"bypass":      true,
	}
)

const (
	KubepilotNodeInfoConfigMapNamespace  = "kube-system"
	KubepilotNodeInfoConfigConfigMapName = "kubepilot-nodeconfig"
	KubepilotNodeInfoConfigMapDataName   = "NodeInfo"
)

const (
	ManagementNetworkIP        = "managementNetworkIP"        // 管理网络IP
	BusinessNetworkIP          = "businessNetworkIP"          // 业务网络IP
	DataNetworkIP              = "dataNetworkIP"              // 数据网络IP
	ManagementNetworkInterface = "managementNetworkInterface" // 管理网络网卡
	BusinessNetworkInterface   = "businessNetworkInterface"   // 业务网络网网卡
	DataNetworkInterface       = "dataNetworkInterface"       // 数据网络网卡
)

// 多实例名称和命名空间
const (
	InstanceName      = "instanceName"      // 实例名称
	InstanceNameSpace = "instanceNameSpace" // 实例命名空间
)

const (
	Etc       = "etc"
	Charts    = "charts"
	Manifests = "manifests"
	Scripts   = "scripts"
	Plugins   = "plugins"
)

const (
	NodeRoleOldControlPlane = "node-role.kubernetes.io/master" // Deprecated: https://github.com/kubernetes/kubeadm/issues/2200
	NodeRoleControlPlane    = "node-role.kubernetes.io/control-plane"
)
