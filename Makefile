.DEFAULT_GOAL := build
GO_BUILD_EXTRA_FLAGS=-tags "containers_image_openpgp exclude_graphdriver_devicemapper exclude_graphdriver_btrfs"
VERSION_PACKAGE=gitlab.bingosoft.net/bingokube/kubepilot/pkg/version

.PHONY: all
all: build image upgrade

# ==============================================================================
# Includes

include build/make-rules/common.mk # make sure include common.mk at the first include line
include build/make-rules/golang.mk
include build/make-rules/helm.mk
include build/make-rules/help.mk

# ==============================================================================
# Targets

## build: Build source code for host platform.
.PHONY: build
build:
	@$(MAKE) go.build

## build.multiarch: Build source code for multiple platforms. See option PLATFORMS.
.PHONY: build.multiarch
build.multiarch:
	@$(MAKE) go.build.multiarch

## image: Build docker images for host arch.
.PHONY: image
image:
	@$(MAKE) image.build

## image.multiarch: Build docker images for multiple platforms. See option PLATFORMS.
.PHONY: image.multiarch
image.multiarch:
	@$(MAKE) image.build.multiarch

.PHONY: helm
helm:
	@$(MAKE) helm.build

.PHONY: install
install:
	@$(MAKE) install.build

.PHONY: uninstall
uninstall:
	@$(MAKE) uninstall.build

.PHONY: upgrade
upgrade:
	@$(MAKE) upgrade.build HELM_EXTRA_ARGS="--set image.tag=$(VERSION)"

## clean: Remove all files that are created by building.
.PHONY: clean
clean:
	@echo "===========> Cleaning all build output"
	@-rm -vrf $(OUTPUT_DIR)

## help: Show this help info.
.PHONY: help
help: Makefile
	@echo -e "\nUsage: make <TARGETS> <OPTIONS> ...\n\nTargets:"
	@sed -n 's/^##//p' $< | column -t -s ':' | sed -e 's/^/ /'
	@echo "$$USAGE_OPTIONS"

## update.scripts: update common scripts
.PHONY: update.scripts
update.scripts:
	wget http://************:81/bingokube/devops/update.sh
	sh ./update.sh && rm -rf ./update.sh

deepcopy:
	@hack/code-generator/code-gen.sh --NAME=$(NAME) --VERSION=${VERSION}
